(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[185],{5075:function(e,n,t){Promise.resolve().then(t.t.bind(t,4794,23)),Promise.resolve().then(t.bind(t,7474)),Promise.resolve().then(t.t.bind(t,6030,23)),Promise.resolve().then(t.bind(t,9338))},9338:function(e,n,t){"use strict";t.d(n,{Providers:function(){return a}});var i=t(8980),s=t(9150),r=t(3419),u=t(2116),l=t(1071),o=t(2208);function a(e){let{children:n}=e,[t]=(0,o.useState)(()=>new s.S({defaultOptions:{queries:{staleTime:6e4,retry:1}}}));return(0,i.jsxs)(r.aH,{client:t,children:[(0,i.jsx)(l.f,{attribute:"class",defaultTheme:"light",enableSystem:!0,disableTransitionOnChange:!0,children:n}),(0,i.jsx)(u.t,{initialIsOpen:!1})]})}},6030:function(){}},function(e){e.O(0,[616,474,669,14,512,744],function(){return e(e.s=5075)}),_N_E=e.O()}]);