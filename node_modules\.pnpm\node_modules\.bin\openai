#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/augment-projects/agent_framework/node_modules/.pnpm/openai@4.104.0_ws@8.18.3_zod@3.25.67/node_modules/openai/bin/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/agent_framework/node_modules/.pnpm/openai@4.104.0_ws@8.18.3_zod@3.25.67/node_modules/openai/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/agent_framework/node_modules/.pnpm/openai@4.104.0_ws@8.18.3_zod@3.25.67/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/agent_framework/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/augment-projects/agent_framework/node_modules/.pnpm/openai@4.104.0_ws@8.18.3_zod@3.25.67/node_modules/openai/bin/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/agent_framework/node_modules/.pnpm/openai@4.104.0_ws@8.18.3_zod@3.25.67/node_modules/openai/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/agent_framework/node_modules/.pnpm/openai@4.104.0_ws@8.18.3_zod@3.25.67/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/agent_framework/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../openai/bin/cli" "$@"
else
  exec node  "$basedir/../openai/bin/cli" "$@"
fi
