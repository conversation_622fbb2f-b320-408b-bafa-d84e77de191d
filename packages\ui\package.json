{"name": "@flowwise-clone/ui", "version": "1.0.0", "description": "Frontend React application for Flowwise Clone", "private": true, "scripts": {"build": "next build", "dev": "next dev", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "clean": "rimraf .next out"}, "dependencies": {"@flowwise-clone/shared": "workspace:*", "next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "reactflow": "^11.10.1", "@reactflow/core": "^11.10.1", "@reactflow/controls": "^11.2.7", "@reactflow/background": "^11.3.7", "@reactflow/minimap": "^11.7.7", "@reactflow/node-toolbar": "^1.3.7", "@reactflow/node-resizer": "^2.2.7", "zustand": "^4.4.7", "@tanstack/react-query": "^5.8.4", "axios": "^1.6.2", "socket.io-client": "^4.7.4", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "clsx": "^2.0.0", "tailwind-merge": "^2.1.0", "framer-motion": "^10.16.16", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "react-hot-toast": "^2.4.1", "react-markdown": "^9.0.1", "react-syntax-highlighter": "^15.5.0", "monaco-editor": "^0.45.0", "@monaco-editor/react": "^4.6.0", "lucide-react": "^0.294.0", "cmdk": "^0.2.0", "react-dropzone": "^14.2.3", "date-fns": "^2.30.0"}, "devDependencies": {"@types/node": "^20.10.0", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "@types/react-syntax-highlighter": "^15.5.11", "typescript": "^5.3.0", "tailwindcss": "^3.3.6", "postcss": "^8.4.32", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-config-next": "^14.0.4", "rimraf": "^5.0.5"}}