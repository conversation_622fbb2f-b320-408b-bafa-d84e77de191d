import { z } from 'zod';
/**
 * Validates data against a Zod schema and returns a result object
 */
export declare function validateData<T>(schema: z.ZodSchema<T>, data: unknown): {
    success: true;
    data: T;
} | {
    success: false;
    errors: string[];
};
/**
 * Safely parses data with a Zod schema, returning null on error
 */
export declare function safeParseData<T>(schema: z.ZodSchema<T>, data: unknown): T | null;
/**
 * Validates an array of data against a schema
 */
export declare function validateArray<T>(schema: z.ZodSchema<T>, data: unknown[]): {
    success: true;
    data: T[];
} | {
    success: false;
    errors: string[];
};
//# sourceMappingURL=validation.d.ts.map