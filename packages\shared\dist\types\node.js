"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NodeTemplateSchema = exports.NodeExecutionResultSchema = exports.FlowConnectionSchema = exports.NodeSchema = exports.NodeConfigSchema = exports.NodeConnectionSchema = void 0;
const zod_1 = require("zod");
const common_1 = require("./common");
// Node connection schema
exports.NodeConnectionSchema = zod_1.z.object({
    id: zod_1.z.string(),
    type: zod_1.z.nativeEnum(common_1.ConnectionType),
    dataType: zod_1.z.nativeEnum(common_1.DataType),
    label: zod_1.z.string(),
    required: zod_1.z.boolean().default(false),
    multiple: zod_1.z.boolean().default(false)
});
// Node configuration schema
exports.NodeConfigSchema = zod_1.z.record(zod_1.z.any());
// Node schema
exports.NodeSchema = common_1.BaseEntitySchema.extend({
    type: zod_1.z.nativeEnum(common_1.NodeType),
    name: zod_1.z.string(),
    label: zod_1.z.string(),
    description: zod_1.z.string().optional(),
    category: zod_1.z.string(),
    version: zod_1.z.string().default('1.0.0'),
    position: common_1.PositionSchema,
    inputs: zod_1.z.array(exports.NodeConnectionSchema),
    outputs: zod_1.z.array(exports.NodeConnectionSchema),
    config: exports.NodeConfigSchema,
    flowId: zod_1.z.string().uuid(),
    isCustom: zod_1.z.boolean().default(false),
    icon: zod_1.z.string().optional(),
    color: zod_1.z.string().optional(),
    documentation: zod_1.z.string().optional()
});
// Flow connection schema (connections between nodes)
exports.FlowConnectionSchema = zod_1.z.object({
    id: zod_1.z.string(),
    sourceNodeId: zod_1.z.string().uuid(),
    sourceHandle: zod_1.z.string(),
    targetNodeId: zod_1.z.string().uuid(),
    targetHandle: zod_1.z.string(),
    flowId: zod_1.z.string().uuid()
});
// Node execution result schema
exports.NodeExecutionResultSchema = zod_1.z.object({
    nodeId: zod_1.z.string().uuid(),
    status: zod_1.z.enum(['success', 'error', 'pending']),
    output: zod_1.z.any().optional(),
    error: zod_1.z.string().optional(),
    executionTime: zod_1.z.number().optional(),
    timestamp: zod_1.z.string().datetime()
});
// Node template schema for creating new nodes
exports.NodeTemplateSchema = zod_1.z.object({
    type: zod_1.z.nativeEnum(common_1.NodeType),
    name: zod_1.z.string(),
    label: zod_1.z.string(),
    description: zod_1.z.string(),
    category: zod_1.z.string(),
    version: zod_1.z.string(),
    inputs: zod_1.z.array(exports.NodeConnectionSchema),
    outputs: zod_1.z.array(exports.NodeConnectionSchema),
    defaultConfig: exports.NodeConfigSchema,
    icon: zod_1.z.string().optional(),
    color: zod_1.z.string().optional(),
    documentation: zod_1.z.string().optional(),
    isCustom: zod_1.z.boolean().default(false)
});
//# sourceMappingURL=node.js.map