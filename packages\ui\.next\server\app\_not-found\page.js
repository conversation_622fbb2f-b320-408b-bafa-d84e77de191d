(()=>{var e={};e.id=409,e.ids=[409],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},9613:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>c,pages:()=>u,routeModule:()=>f,tree:()=>d}),r(4019),r(5999),r(2367);var n=r(7188),o=r(8659),s=r(2067),i=r.n(s),a=r(9786),l={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let d=["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.t.bind(r,5999,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,2367)),"C:\\Users\\<USER>\\Documents\\augment-projects\\agent_framework\\packages\\ui\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,5999,23)),"next/dist/client/components/not-found-error"]}],u=[],c="/_not-found/page",p={require:r,loadChunk:()=>Promise.resolve()},f=new n.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},294:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,3143,23)),Promise.resolve().then(r.t.bind(r,3627,23)),Promise.resolve().then(r.t.bind(r,8958,23)),Promise.resolve().then(r.t.bind(r,9501,23)),Promise.resolve().then(r.t.bind(r,543,23)),Promise.resolve().then(r.t.bind(r,8789,23))},1459:(e,t,r)=>{Promise.resolve().then(r.bind(r,6600)),Promise.resolve().then(r.bind(r,5682))},5682:(e,t,r)=>{"use strict";r.d(t,{Providers:()=>d});var n=r(7491),o=r(843),s=r(5668),i=r(675),a=r(2781),l=r(6364);function d({children:e}){let[t]=(0,l.useState)(()=>new o.S({defaultOptions:{queries:{staleTime:6e4,retry:1}}}));return(0,n.jsxs)(s.aH,{client:t,children:[n.jsx(a.f,{attribute:"class",defaultTheme:"light",enableSystem:!0,disableTransitionOnChange:!0,children:e}),n.jsx(i.t,{initialIsOpen:!1})]})}},1202:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isNotFoundError:function(){return o},notFound:function(){return n}});let r="NEXT_NOT_FOUND";function n(){let e=Error(r);throw e.digest=r,e}function o(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4019:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{PARALLEL_ROUTE_DEFAULT_PATH:function(){return o},default:function(){return s}});let n=r(1202),o="next/dist/client/components/parallel-route-default.js";function s(){(0,n.notFound)()}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2367:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>l});var n=r(4519),o=r(1283),s=r.n(o);r(1369);let i=(0,r(4798).createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\agent_framework\packages\ui\src\components\providers.tsx#Providers`);var a=r(6533);let l={title:"Flowwise Clone - Visual AI Agent Builder",description:"Build AI agents, chatbots, and multi-agent systems visually through a drag-and-drop interface.",keywords:["AI","chatbot","agent","visual builder","no-code","LLM"],authors:[{name:"Flowwise Clone Team"}],viewport:"width=device-width, initial-scale=1",themeColor:"#3B82F6"};function d({children:e}){return n.jsx("html",{lang:"en",suppressHydrationWarning:!0,children:n.jsx("body",{className:s().className,children:(0,n.jsxs)(i,{children:[e,n.jsx(a.x7,{position:"top-right",toastOptions:{duration:4e3,style:{background:"hsl(var(--card))",color:"hsl(var(--card-foreground))",border:"1px solid hsl(var(--border))"}}})]})})})}},1369:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[903],()=>r(9613));module.exports=n})();