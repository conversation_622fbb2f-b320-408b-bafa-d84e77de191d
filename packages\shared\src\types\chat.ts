import { z } from 'zod';
import { BaseEntitySchema } from './common';

// Message role enum
export enum MessageRole {
  USER = 'user',
  ASSISTANT = 'assistant',
  SYSTEM = 'system',
  FUNCTION = 'function',
  TOOL = 'tool'
}

// Message schema
export const MessageSchema = BaseEntitySchema.extend({
  role: z.nativeEnum(MessageRole),
  content: z.string(),
  name: z.string().optional(),
  functionCall: z.object({
    name: z.string(),
    arguments: z.string()
  }).optional(),
  toolCalls: z.array(z.object({
    id: z.string(),
    type: z.literal('function'),
    function: z.object({
      name: z.string(),
      arguments: z.string()
    })
  })).optional(),
  toolCallId: z.string().optional(),
  metadata: z.record(z.any()).optional()
});

export type Message = z.infer<typeof MessageSchema>;

// Chat session schema
export const ChatSessionSchema = BaseEntitySchema.extend({
  flowId: z.string().uuid(),
  userId: z.string().uuid().optional(),
  sessionId: z.string(),
  title: z.string().optional(),
  messages: z.array(MessageSchema),
  isActive: z.boolean().default(true),
  metadata: z.record(z.any()).optional()
});

export type ChatSession = z.infer<typeof ChatSessionSchema>;

// Chat request schema
export const ChatRequestSchema = z.object({
  message: z.string(),
  sessionId: z.string().optional(),
  flowId: z.string().uuid(),
  userId: z.string().uuid().optional(),
  streaming: z.boolean().default(false),
  metadata: z.record(z.any()).optional()
});

export type ChatRequest = z.infer<typeof ChatRequestSchema>;

// Chat response schema
export const ChatResponseSchema = z.object({
  message: MessageSchema,
  sessionId: z.string(),
  isComplete: z.boolean().default(true),
  metadata: z.record(z.any()).optional()
});

export type ChatResponse = z.infer<typeof ChatResponseSchema>;

// Streaming chat response schema
export const StreamingChatResponseSchema = z.object({
  delta: z.string(),
  sessionId: z.string(),
  isComplete: z.boolean(),
  metadata: z.record(z.any()).optional()
});

export type StreamingChatResponse = z.infer<typeof StreamingChatResponseSchema>;
