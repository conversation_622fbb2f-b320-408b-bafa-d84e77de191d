"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-collection@1.1.7_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1";
exports.ids = ["vendor-chunks/@radix-ui+react-collection@1.1.7_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@radix-ui+react-collection@1.1.7_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@radix-ui/react-collection/dist/index.mjs":
/*!***************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@radix-ui+react-collection@1.1.7_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@radix-ui/react-collection/dist/index.mjs ***!
  \***************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createCollection: () => (/* binding */ createCollection),\n/* harmony export */   unstable_createCollection: () => (/* binding */ createCollection2)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-context@1.1.2_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-compose-refs@1.1.2_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-slot@1.2.3_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ createCollection,unstable_createCollection auto */ // src/collection-legacy.tsx\n\n\n\n\n\nfunction createCollection(name) {\n    const PROVIDER_NAME = name + \"CollectionProvider\";\n    const [createCollectionContext, createCollectionScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(PROVIDER_NAME);\n    const [CollectionProviderImpl, useCollectionContext] = createCollectionContext(PROVIDER_NAME, {\n        collectionRef: {\n            current: null\n        },\n        itemMap: /* @__PURE__ */ new Map()\n    });\n    const CollectionProvider = (props)=>{\n        const { scope, children } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const itemMap = react__WEBPACK_IMPORTED_MODULE_0__.useRef(/* @__PURE__ */ new Map()).current;\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionProviderImpl, {\n            scope,\n            itemMap,\n            collectionRef: ref,\n            children\n        });\n    };\n    CollectionProvider.displayName = PROVIDER_NAME;\n    const COLLECTION_SLOT_NAME = name + \"CollectionSlot\";\n    const CollectionSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(COLLECTION_SLOT_NAME);\n    const CollectionSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children } = props;\n        const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, context.collectionRef);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionSlotImpl, {\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n    const ITEM_SLOT_NAME = name + \"CollectionItemSlot\";\n    const ITEM_DATA_ATTR = \"data-radix-collection-item\";\n    const CollectionItemSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(ITEM_SLOT_NAME);\n    const CollectionItemSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children, ...itemData } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, ref);\n        const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n            context.itemMap.set(ref, {\n                ref,\n                ...itemData\n            });\n            return ()=>void context.itemMap.delete(ref);\n        });\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionItemSlotImpl, {\n            ...{\n                [ITEM_DATA_ATTR]: \"\"\n            },\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n    function useCollection(scope) {\n        const context = useCollectionContext(name + \"CollectionConsumer\", scope);\n        const getItems = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n            const collectionNode = context.collectionRef.current;\n            if (!collectionNode) return [];\n            const orderedNodes = Array.from(collectionNode.querySelectorAll(`[${ITEM_DATA_ATTR}]`));\n            const items = Array.from(context.itemMap.values());\n            const orderedItems = items.sort((a, b)=>orderedNodes.indexOf(a.ref.current) - orderedNodes.indexOf(b.ref.current));\n            return orderedItems;\n        }, [\n            context.collectionRef,\n            context.itemMap\n        ]);\n        return getItems;\n    }\n    return [\n        {\n            Provider: CollectionProvider,\n            Slot: CollectionSlot,\n            ItemSlot: CollectionItemSlot\n        },\n        useCollection,\n        createCollectionScope\n    ];\n}\n// src/collection.tsx\n\n\n\n\n// src/ordered-dictionary.ts\nvar __instanciated = /* @__PURE__ */ new WeakMap();\nvar OrderedDict = class _OrderedDict extends Map {\n    #keys;\n    constructor(entries){\n        super(entries);\n        this.#keys = [\n            ...super.keys()\n        ];\n        __instanciated.set(this, true);\n    }\n    set(key, value) {\n        if (__instanciated.get(this)) {\n            if (this.has(key)) {\n                this.#keys[this.#keys.indexOf(key)] = key;\n            } else {\n                this.#keys.push(key);\n            }\n        }\n        super.set(key, value);\n        return this;\n    }\n    insert(index, key, value) {\n        const has = this.has(key);\n        const length = this.#keys.length;\n        const relativeIndex = toSafeInteger(index);\n        let actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n        const safeIndex = actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n        if (safeIndex === this.size || has && safeIndex === this.size - 1 || safeIndex === -1) {\n            this.set(key, value);\n            return this;\n        }\n        const size = this.size + (has ? 0 : 1);\n        if (relativeIndex < 0) {\n            actualIndex++;\n        }\n        const keys = [\n            ...this.#keys\n        ];\n        let nextValue;\n        let shouldSkip = false;\n        for(let i = actualIndex; i < size; i++){\n            if (actualIndex === i) {\n                let nextKey = keys[i];\n                if (keys[i] === key) {\n                    nextKey = keys[i + 1];\n                }\n                if (has) {\n                    this.delete(key);\n                }\n                nextValue = this.get(nextKey);\n                this.set(key, value);\n            } else {\n                if (!shouldSkip && keys[i - 1] === key) {\n                    shouldSkip = true;\n                }\n                const currentKey = keys[shouldSkip ? i : i - 1];\n                const currentValue = nextValue;\n                nextValue = this.get(currentKey);\n                this.delete(currentKey);\n                this.set(currentKey, currentValue);\n            }\n        }\n        return this;\n    }\n    with(index, key, value) {\n        const copy = new _OrderedDict(this);\n        copy.insert(index, key, value);\n        return copy;\n    }\n    before(key) {\n        const index = this.#keys.indexOf(key) - 1;\n        if (index < 0) {\n            return void 0;\n        }\n        return this.entryAt(index);\n    }\n    /**\n   * Sets a new key-value pair at the position before the given key.\n   */ setBefore(key, newKey, value) {\n        const index = this.#keys.indexOf(key);\n        if (index === -1) {\n            return this;\n        }\n        return this.insert(index, newKey, value);\n    }\n    after(key) {\n        let index = this.#keys.indexOf(key);\n        index = index === -1 || index === this.size - 1 ? -1 : index + 1;\n        if (index === -1) {\n            return void 0;\n        }\n        return this.entryAt(index);\n    }\n    /**\n   * Sets a new key-value pair at the position after the given key.\n   */ setAfter(key, newKey, value) {\n        const index = this.#keys.indexOf(key);\n        if (index === -1) {\n            return this;\n        }\n        return this.insert(index + 1, newKey, value);\n    }\n    first() {\n        return this.entryAt(0);\n    }\n    last() {\n        return this.entryAt(-1);\n    }\n    clear() {\n        this.#keys = [];\n        return super.clear();\n    }\n    delete(key) {\n        const deleted = super.delete(key);\n        if (deleted) {\n            this.#keys.splice(this.#keys.indexOf(key), 1);\n        }\n        return deleted;\n    }\n    deleteAt(index) {\n        const key = this.keyAt(index);\n        if (key !== void 0) {\n            return this.delete(key);\n        }\n        return false;\n    }\n    at(index) {\n        const key = at(this.#keys, index);\n        if (key !== void 0) {\n            return this.get(key);\n        }\n    }\n    entryAt(index) {\n        const key = at(this.#keys, index);\n        if (key !== void 0) {\n            return [\n                key,\n                this.get(key)\n            ];\n        }\n    }\n    indexOf(key) {\n        return this.#keys.indexOf(key);\n    }\n    keyAt(index) {\n        return at(this.#keys, index);\n    }\n    from(key, offset) {\n        const index = this.indexOf(key);\n        if (index === -1) {\n            return void 0;\n        }\n        let dest = index + offset;\n        if (dest < 0) dest = 0;\n        if (dest >= this.size) dest = this.size - 1;\n        return this.at(dest);\n    }\n    keyFrom(key, offset) {\n        const index = this.indexOf(key);\n        if (index === -1) {\n            return void 0;\n        }\n        let dest = index + offset;\n        if (dest < 0) dest = 0;\n        if (dest >= this.size) dest = this.size - 1;\n        return this.keyAt(dest);\n    }\n    find(predicate, thisArg) {\n        let index = 0;\n        for (const entry of this){\n            if (Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                return entry;\n            }\n            index++;\n        }\n        return void 0;\n    }\n    findIndex(predicate, thisArg) {\n        let index = 0;\n        for (const entry of this){\n            if (Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                return index;\n            }\n            index++;\n        }\n        return -1;\n    }\n    filter(predicate, thisArg) {\n        const entries = [];\n        let index = 0;\n        for (const entry of this){\n            if (Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                entries.push(entry);\n            }\n            index++;\n        }\n        return new _OrderedDict(entries);\n    }\n    map(callbackfn, thisArg) {\n        const entries = [];\n        let index = 0;\n        for (const entry of this){\n            entries.push([\n                entry[0],\n                Reflect.apply(callbackfn, thisArg, [\n                    entry,\n                    index,\n                    this\n                ])\n            ]);\n            index++;\n        }\n        return new _OrderedDict(entries);\n    }\n    reduce(...args) {\n        const [callbackfn, initialValue] = args;\n        let index = 0;\n        let accumulator = initialValue ?? this.at(0);\n        for (const entry of this){\n            if (index === 0 && args.length === 1) {\n                accumulator = entry;\n            } else {\n                accumulator = Reflect.apply(callbackfn, this, [\n                    accumulator,\n                    entry,\n                    index,\n                    this\n                ]);\n            }\n            index++;\n        }\n        return accumulator;\n    }\n    reduceRight(...args) {\n        const [callbackfn, initialValue] = args;\n        let accumulator = initialValue ?? this.at(-1);\n        for(let index = this.size - 1; index >= 0; index--){\n            const entry = this.at(index);\n            if (index === this.size - 1 && args.length === 1) {\n                accumulator = entry;\n            } else {\n                accumulator = Reflect.apply(callbackfn, this, [\n                    accumulator,\n                    entry,\n                    index,\n                    this\n                ]);\n            }\n        }\n        return accumulator;\n    }\n    toSorted(compareFn) {\n        const entries = [\n            ...this.entries()\n        ].sort(compareFn);\n        return new _OrderedDict(entries);\n    }\n    toReversed() {\n        const reversed = new _OrderedDict();\n        for(let index = this.size - 1; index >= 0; index--){\n            const key = this.keyAt(index);\n            const element = this.get(key);\n            reversed.set(key, element);\n        }\n        return reversed;\n    }\n    toSpliced(...args) {\n        const entries = [\n            ...this.entries()\n        ];\n        entries.splice(...args);\n        return new _OrderedDict(entries);\n    }\n    slice(start, end) {\n        const result = new _OrderedDict();\n        let stop = this.size - 1;\n        if (start === void 0) {\n            return result;\n        }\n        if (start < 0) {\n            start = start + this.size;\n        }\n        if (end !== void 0 && end > 0) {\n            stop = end - 1;\n        }\n        for(let index = start; index <= stop; index++){\n            const key = this.keyAt(index);\n            const element = this.get(key);\n            result.set(key, element);\n        }\n        return result;\n    }\n    every(predicate, thisArg) {\n        let index = 0;\n        for (const entry of this){\n            if (!Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                return false;\n            }\n            index++;\n        }\n        return true;\n    }\n    some(predicate, thisArg) {\n        let index = 0;\n        for (const entry of this){\n            if (Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                return true;\n            }\n            index++;\n        }\n        return false;\n    }\n};\nfunction at(array, index) {\n    if (\"at\" in Array.prototype) {\n        return Array.prototype.at.call(array, index);\n    }\n    const actualIndex = toSafeIndex(array, index);\n    return actualIndex === -1 ? void 0 : array[actualIndex];\n}\nfunction toSafeIndex(array, index) {\n    const length = array.length;\n    const relativeIndex = toSafeInteger(index);\n    const actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n    return actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n}\nfunction toSafeInteger(number) {\n    return number !== number || number === 0 ? 0 : Math.trunc(number);\n}\n// src/collection.tsx\n\nfunction createCollection2(name) {\n    const PROVIDER_NAME = name + \"CollectionProvider\";\n    const [createCollectionContext, createCollectionScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(PROVIDER_NAME);\n    const [CollectionContextProvider, useCollectionContext] = createCollectionContext(PROVIDER_NAME, {\n        collectionElement: null,\n        collectionRef: {\n            current: null\n        },\n        collectionRefObject: {\n            current: null\n        },\n        itemMap: new OrderedDict(),\n        setItemMap: ()=>void 0\n    });\n    const CollectionProvider = ({ state, ...props })=>{\n        return state ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionProviderImpl, {\n            ...props,\n            state\n        }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionInit, {\n            ...props\n        });\n    };\n    CollectionProvider.displayName = PROVIDER_NAME;\n    const CollectionInit = (props)=>{\n        const state = useInitCollection();\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionProviderImpl, {\n            ...props,\n            state\n        });\n    };\n    CollectionInit.displayName = PROVIDER_NAME + \"Init\";\n    const CollectionProviderImpl = (props)=>{\n        const { scope, children, state } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const [collectionElement, setCollectionElement] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n        const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(ref, setCollectionElement);\n        const [itemMap, setItemMap] = state;\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n            if (!collectionElement) return;\n            const observer = getChildListObserver(()=>{});\n            observer.observe(collectionElement, {\n                childList: true,\n                subtree: true\n            });\n            return ()=>{\n                observer.disconnect();\n            };\n        }, [\n            collectionElement\n        ]);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionContextProvider, {\n            scope,\n            itemMap,\n            setItemMap,\n            collectionRef: composeRefs,\n            collectionRefObject: ref,\n            collectionElement,\n            children\n        });\n    };\n    CollectionProviderImpl.displayName = PROVIDER_NAME + \"Impl\";\n    const COLLECTION_SLOT_NAME = name + \"CollectionSlot\";\n    const CollectionSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(COLLECTION_SLOT_NAME);\n    const CollectionSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children } = props;\n        const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, context.collectionRef);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionSlotImpl, {\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n    const ITEM_SLOT_NAME = name + \"CollectionItemSlot\";\n    const ITEM_DATA_ATTR = \"data-radix-collection-item\";\n    const CollectionItemSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(ITEM_SLOT_NAME);\n    const CollectionItemSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children, ...itemData } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const [element, setElement] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, ref, setElement);\n        const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n        const { setItemMap } = context;\n        const itemDataRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(itemData);\n        if (!shallowEqual(itemDataRef.current, itemData)) {\n            itemDataRef.current = itemData;\n        }\n        const memoizedItemData = itemDataRef.current;\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n            const itemData2 = memoizedItemData;\n            setItemMap((map)=>{\n                if (!element) {\n                    return map;\n                }\n                if (!map.has(element)) {\n                    map.set(element, {\n                        ...itemData2,\n                        element\n                    });\n                    return map.toSorted(sortByDocumentPosition);\n                }\n                return map.set(element, {\n                    ...itemData2,\n                    element\n                }).toSorted(sortByDocumentPosition);\n            });\n            return ()=>{\n                setItemMap((map)=>{\n                    if (!element || !map.has(element)) {\n                        return map;\n                    }\n                    map.delete(element);\n                    return new OrderedDict(map);\n                });\n            };\n        }, [\n            element,\n            memoizedItemData,\n            setItemMap\n        ]);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionItemSlotImpl, {\n            ...{\n                [ITEM_DATA_ATTR]: \"\"\n            },\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n    function useInitCollection() {\n        return react__WEBPACK_IMPORTED_MODULE_0__.useState(new OrderedDict());\n    }\n    function useCollection(scope) {\n        const { itemMap } = useCollectionContext(name + \"CollectionConsumer\", scope);\n        return itemMap;\n    }\n    const functions = {\n        createCollectionScope,\n        useCollection,\n        useInitCollection\n    };\n    return [\n        {\n            Provider: CollectionProvider,\n            Slot: CollectionSlot,\n            ItemSlot: CollectionItemSlot\n        },\n        functions\n    ];\n}\nfunction shallowEqual(a, b) {\n    if (a === b) return true;\n    if (typeof a !== \"object\" || typeof b !== \"object\") return false;\n    if (a == null || b == null) return false;\n    const keysA = Object.keys(a);\n    const keysB = Object.keys(b);\n    if (keysA.length !== keysB.length) return false;\n    for (const key of keysA){\n        if (!Object.prototype.hasOwnProperty.call(b, key)) return false;\n        if (a[key] !== b[key]) return false;\n    }\n    return true;\n}\nfunction isElementPreceding(a, b) {\n    return !!(b.compareDocumentPosition(a) & Node.DOCUMENT_POSITION_PRECEDING);\n}\nfunction sortByDocumentPosition(a, b) {\n    return !a[1].element || !b[1].element ? 0 : isElementPreceding(a[1].element, b[1].element) ? -1 : 1;\n}\nfunction getChildListObserver(callback) {\n    const observer = new MutationObserver((mutationsList)=>{\n        for (const mutation of mutationsList){\n            if (mutation.type === \"childList\") {\n                callback();\n                return;\n            }\n        }\n    });\n    return observer;\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@radix-ui+react-collection@1.1.7_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@radix-ui/react-collection/dist/index.mjs\n");

/***/ })

};
;