"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tanstack+react-query-devtools@5.81.5_@tanstack+react-query@5.81.5_react@18.3.1";
exports.ids = ["vendor-chunks/@tanstack+react-query-devtools@5.81.5_@tanstack+react-query@5.81.5_react@18.3.1"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@tanstack+react-query-devtools@5.81.5_@tanstack+react-query@5.81.5_react@18.3.1/node_modules/@tanstack/react-query-devtools/build/modern/ReactQueryDevtools.js":
/*!***********************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@tanstack+react-query-devtools@5.81.5_@tanstack+react-query@5.81.5_react@18.3.1/node_modules/@tanstack/react-query-devtools/build/modern/ReactQueryDevtools.js ***!
  \***********************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReactQueryDevtools: () => (/* binding */ ReactQueryDevtools)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/../../node_modules/.pnpm/@tanstack+react-query@5.81.5_react@18.3.1/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/../../node_modules/.pnpm/@tanstack+query-core@5.81.5/node_modules/@tanstack/query-core/build/modern/onlineManager.js\");\n/* harmony import */ var _tanstack_query_devtools__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/query-devtools */ \"(ssr)/../../node_modules/.pnpm/@tanstack+query-devtools@5.81.2/node_modules/@tanstack/query-devtools/build/dev.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ ReactQueryDevtools auto */ // src/ReactQueryDevtools.tsx\n\n\n\n\nfunction ReactQueryDevtools(props) {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)(props.client);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const { buttonPosition, position, initialIsOpen, errorTypes, styleNonce, shadowDOMTarget } = props;\n    const [devtools] = react__WEBPACK_IMPORTED_MODULE_0__.useState(new _tanstack_query_devtools__WEBPACK_IMPORTED_MODULE_1__.TanstackQueryDevtools({\n        client: queryClient,\n        queryFlavor: \"React Query\",\n        version: \"5\",\n        onlineManager: _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.onlineManager,\n        buttonPosition,\n        position,\n        initialIsOpen,\n        errorTypes,\n        styleNonce,\n        shadowDOMTarget\n    }));\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        devtools.setClient(queryClient);\n    }, [\n        queryClient,\n        devtools\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (buttonPosition) {\n            devtools.setButtonPosition(buttonPosition);\n        }\n    }, [\n        buttonPosition,\n        devtools\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (position) {\n            devtools.setPosition(position);\n        }\n    }, [\n        position,\n        devtools\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        devtools.setInitialIsOpen(initialIsOpen || false);\n    }, [\n        initialIsOpen,\n        devtools\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        devtools.setErrorTypes(errorTypes || []);\n    }, [\n        errorTypes,\n        devtools\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (ref.current) {\n            devtools.mount(ref.current);\n        }\n        return ()=>{\n            devtools.unmount();\n        };\n    }, [\n        devtools\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n        dir: \"ltr\",\n        className: \"tsqd-parent-container\",\n        ref\n    });\n}\n //# sourceMappingURL=ReactQueryDevtools.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@tanstack+react-query-devtools@5.81.5_@tanstack+react-query@5.81.5_react@18.3.1/node_modules/@tanstack/react-query-devtools/build/modern/ReactQueryDevtools.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@tanstack+react-query-devtools@5.81.5_@tanstack+react-query@5.81.5_react@18.3.1/node_modules/@tanstack/react-query-devtools/build/modern/ReactQueryDevtoolsPanel.js":
/*!****************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@tanstack+react-query-devtools@5.81.5_@tanstack+react-query@5.81.5_react@18.3.1/node_modules/@tanstack/react-query-devtools/build/modern/ReactQueryDevtoolsPanel.js ***!
  \****************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReactQueryDevtoolsPanel: () => (/* binding */ ReactQueryDevtoolsPanel)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/../../node_modules/.pnpm/@tanstack+react-query@5.81.5_react@18.3.1/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/../../node_modules/.pnpm/@tanstack+query-core@5.81.5/node_modules/@tanstack/query-core/build/modern/onlineManager.js\");\n/* harmony import */ var _tanstack_query_devtools__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/query-devtools */ \"(ssr)/../../node_modules/.pnpm/@tanstack+query-devtools@5.81.2/node_modules/@tanstack/query-devtools/build/dev.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ ReactQueryDevtoolsPanel auto */ // src/ReactQueryDevtoolsPanel.tsx\n\n\n\n\nfunction ReactQueryDevtoolsPanel(props) {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)(props.client);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const { errorTypes, styleNonce, shadowDOMTarget } = props;\n    const [devtools] = react__WEBPACK_IMPORTED_MODULE_0__.useState(new _tanstack_query_devtools__WEBPACK_IMPORTED_MODULE_1__.TanstackQueryDevtoolsPanel({\n        client: queryClient,\n        queryFlavor: \"React Query\",\n        version: \"5\",\n        onlineManager: _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.onlineManager,\n        buttonPosition: \"bottom-left\",\n        position: \"bottom\",\n        initialIsOpen: true,\n        errorTypes,\n        styleNonce,\n        shadowDOMTarget,\n        onClose: props.onClose\n    }));\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        devtools.setClient(queryClient);\n    }, [\n        queryClient,\n        devtools\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        devtools.setOnClose(props.onClose ?? (()=>{}));\n    }, [\n        props.onClose,\n        devtools\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        devtools.setErrorTypes(errorTypes || []);\n    }, [\n        errorTypes,\n        devtools\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (ref.current) {\n            devtools.mount(ref.current);\n        }\n        return ()=>{\n            devtools.unmount();\n        };\n    }, [\n        devtools\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n        style: {\n            height: \"500px\",\n            ...props.style\n        },\n        className: \"tsqd-parent-container\",\n        ref\n    });\n}\n //# sourceMappingURL=ReactQueryDevtoolsPanel.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@tanstack+react-query-devtools@5.81.5_@tanstack+react-query@5.81.5_react@18.3.1/node_modules/@tanstack/react-query-devtools/build/modern/ReactQueryDevtoolsPanel.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@tanstack+react-query-devtools@5.81.5_@tanstack+react-query@5.81.5_react@18.3.1/node_modules/@tanstack/react-query-devtools/build/modern/index.js":
/*!**********************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@tanstack+react-query-devtools@5.81.5_@tanstack+react-query@5.81.5_react@18.3.1/node_modules/@tanstack/react-query-devtools/build/modern/index.js ***!
  \**********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReactQueryDevtools: () => (/* binding */ ReactQueryDevtools2),\n/* harmony export */   ReactQueryDevtoolsPanel: () => (/* binding */ ReactQueryDevtoolsPanel2)\n/* harmony export */ });\n/* harmony import */ var _ReactQueryDevtools_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ReactQueryDevtools.js */ \"(ssr)/../../node_modules/.pnpm/@tanstack+react-query-devtools@5.81.5_@tanstack+react-query@5.81.5_react@18.3.1/node_modules/@tanstack/react-query-devtools/build/modern/ReactQueryDevtools.js\");\n/* harmony import */ var _ReactQueryDevtoolsPanel_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ReactQueryDevtoolsPanel.js */ \"(ssr)/../../node_modules/.pnpm/@tanstack+react-query-devtools@5.81.5_@tanstack+react-query@5.81.5_react@18.3.1/node_modules/@tanstack/react-query-devtools/build/modern/ReactQueryDevtoolsPanel.js\");\n/* __next_internal_client_entry_do_not_use__ ReactQueryDevtools,ReactQueryDevtoolsPanel auto */ // src/index.ts\n\n\nvar ReactQueryDevtools2 =  false ? 0 : _ReactQueryDevtools_js__WEBPACK_IMPORTED_MODULE_0__.ReactQueryDevtools;\nvar ReactQueryDevtoolsPanel2 =  false ? 0 : _ReactQueryDevtoolsPanel_js__WEBPACK_IMPORTED_MODULE_1__.ReactQueryDevtoolsPanel;\n //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0B0YW5zdGFjaytyZWFjdC1xdWVyeS1kZXZ0b29sc0A1LjgxLjVfQHRhbnN0YWNrK3JlYWN0LXF1ZXJ5QDUuODEuNV9yZWFjdEAxOC4zLjEvbm9kZV9tb2R1bGVzL0B0YW5zdGFjay9yZWFjdC1xdWVyeS1kZXZ0b29scy9idWlsZC9tb2Rlcm4vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFFMEI7QUFDSztBQUV4QixJQUFNRSxzQkFDWEMsTUFBeUIsR0FDckIsQ0FFQyxHQUNRSCxzRUFBQTtBQUVSLElBQU1JLDJCQUNYRCxNQUF5QixHQUNyQixDQUVDLEdBQ2FGLGdGQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGZsb3d3aXNlLWNsb25lL3VpLy4uLy4uL3NyYy9pbmRleC50cz82NzQ0Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgKiBhcyBEZXZ0b29scyBmcm9tICcuL1JlYWN0UXVlcnlEZXZ0b29scydcbmltcG9ydCAqIGFzIERldnRvb2xzUGFuZWwgZnJvbSAnLi9SZWFjdFF1ZXJ5RGV2dG9vbHNQYW5lbCdcblxuZXhwb3J0IGNvbnN0IFJlYWN0UXVlcnlEZXZ0b29sczogKHR5cGVvZiBEZXZ0b29scylbJ1JlYWN0UXVlcnlEZXZ0b29scyddID1cbiAgcHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdkZXZlbG9wbWVudCdcbiAgICA/IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgcmV0dXJuIG51bGxcbiAgICAgIH1cbiAgICA6IERldnRvb2xzLlJlYWN0UXVlcnlEZXZ0b29sc1xuXG5leHBvcnQgY29uc3QgUmVhY3RRdWVyeURldnRvb2xzUGFuZWw6ICh0eXBlb2YgRGV2dG9vbHNQYW5lbClbJ1JlYWN0UXVlcnlEZXZ0b29sc1BhbmVsJ10gPVxuICBwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ2RldmVsb3BtZW50J1xuICAgID8gZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gbnVsbFxuICAgICAgfVxuICAgIDogRGV2dG9vbHNQYW5lbC5SZWFjdFF1ZXJ5RGV2dG9vbHNQYW5lbFxuIl0sIm5hbWVzIjpbIkRldnRvb2xzIiwiRGV2dG9vbHNQYW5lbCIsIlJlYWN0UXVlcnlEZXZ0b29scyIsInByb2Nlc3MiLCJSZWFjdFF1ZXJ5RGV2dG9vbHNQYW5lbCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@tanstack+react-query-devtools@5.81.5_@tanstack+react-query@5.81.5_react@18.3.1/node_modules/@tanstack/react-query-devtools/build/modern/index.js\n");

/***/ })

};
;