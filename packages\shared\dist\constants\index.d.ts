export declare const API_ENDPOINTS: {
    readonly FLOWS: "/api/flows";
    readonly NODES: "/api/nodes";
    readonly CHAT: "/api/chat";
    readonly AGENTS: "/api/agents";
    readonly USERS: "/api/users";
    readonly WORKSPACES: "/api/workspaces";
    readonly EXECUTIONS: "/api/executions";
    readonly TEMPLATES: "/api/templates";
};
export declare const NODE_CATEGORIES: {
    readonly LLM: "Large Language Models";
    readonly MEMORY: "Memory";
    readonly TOOLS: "Tools";
    readonly VECTOR_STORES: "Vector Stores";
    readonly DOCUMENT_LOADERS: "Document Loaders";
    readonly TEXT_SPLITTERS: "Text Splitters";
    readonly EMBEDDINGS: "Embeddings";
    readonly RETRIEVERS: "Retrievers";
    readonly CHAINS: "Chains";
    readonly AGENTS: "Agents";
    readonly OUTPUT_PARSERS: "Output Parsers";
    readonly PROMPT_TEMPLATES: "Prompt Templates";
    readonly UTILITIES: "Utilities";
    readonly CUSTOM: "Custom";
};
export declare const NODE_COLORS: {
    readonly LLM: "#3B82F6";
    readonly MEMORY: "#10B981";
    readonly TOOLS: "#F59E0B";
    readonly VECTOR_STORES: "#8B5CF6";
    readonly DOCUMENT_LOADERS: "#EC4899";
    readonly TEXT_SPLITTERS: "#06B6D4";
    readonly EMBEDDINGS: "#84CC16";
    readonly RETRIEVERS: "#EF4444";
    readonly CHAINS: "#6B7280";
    readonly AGENTS: "#F97316";
    readonly OUTPUT_PARSERS: "#14B8A6";
    readonly PROMPT_TEMPLATES: "#A855F7";
    readonly UTILITIES: "#64748B";
    readonly CUSTOM: "#1F2937";
};
export declare const LLM_PROVIDERS: {
    readonly OPENAI: "OpenAI";
    readonly ANTHROPIC: "Anthropic";
    readonly GOOGLE: "Google";
    readonly AZURE_OPENAI: "Azure OpenAI";
    readonly HUGGING_FACE: "Hugging Face";
    readonly COHERE: "Cohere";
    readonly REPLICATE: "Replicate";
    readonly LOCAL: "Local Models";
};
export declare const VECTOR_DATABASES: {
    readonly PINECONE: "Pinecone";
    readonly CHROMA: "Chroma";
    readonly WEAVIATE: "Weaviate";
    readonly QDRANT: "Qdrant";
    readonly MILVUS: "Milvus";
    readonly FAISS: "FAISS";
    readonly REDIS: "Redis";
    readonly ELASTICSEARCH: "Elasticsearch";
};
export declare const SUPPORTED_FILE_TYPES: {
    readonly PDF: ".pdf";
    readonly DOCX: ".docx";
    readonly DOC: ".doc";
    readonly TXT: ".txt";
    readonly MD: ".md";
    readonly CSV: ".csv";
    readonly JSON: ".json";
    readonly XML: ".xml";
    readonly HTML: ".html";
    readonly RTF: ".rtf";
};
export declare const LIMITS: {
    readonly MAX_NODES_PER_FLOW: 100;
    readonly MAX_CONNECTIONS_PER_NODE: 20;
    readonly MAX_MESSAGE_LENGTH: 10000;
    readonly MAX_FILE_SIZE: number;
    readonly MAX_EXECUTION_TIME: 300000;
    readonly MAX_CHAT_HISTORY: 100;
};
//# sourceMappingURL=index.d.ts.map