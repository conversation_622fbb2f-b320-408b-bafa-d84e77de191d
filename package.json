{"name": "flowwise-clone", "version": "1.0.0", "description": "A comprehensive Flowwise clone - Visual AI Agent Builder", "private": true, "workspaces": ["packages/*"], "scripts": {"build": "turbo run build", "dev": "turbo run dev", "start": "turbo run start", "lint": "turbo run lint", "test": "turbo run test", "clean": "turbo run clean", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "type-check": "turbo run type-check"}, "devDependencies": {"@types/node": "^20.10.0", "prettier": "^3.1.0", "turbo": "^1.11.0", "typescript": "^5.3.0"}, "engines": {"node": ">=18.15.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.10.0"}