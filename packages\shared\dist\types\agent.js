"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolSchema = exports.AgentExecutionSchema = exports.MultiAgentSystemSchema = exports.AgentSchema = exports.AgentType = void 0;
const zod_1 = require("zod");
const common_1 = require("./common");
// Agent type enum
var AgentType;
(function (AgentType) {
    AgentType["CONVERSATIONAL"] = "conversational";
    AgentType["REACT"] = "react";
    AgentType["PLAN_AND_EXECUTE"] = "planAndExecute";
    AgentType["OPENAI_FUNCTIONS"] = "openAiFunctions";
    AgentType["STRUCTURED_CHAT"] = "structuredChat";
    AgentType["CUSTOM"] = "custom";
})(AgentType || (exports.AgentType = AgentType = {}));
// Agent schema
exports.AgentSchema = common_1.BaseEntitySchema.extend({
    name: zod_1.z.string(),
    description: zod_1.z.string().optional(),
    type: zod_1.z.nativeEnum(AgentType),
    llmId: zod_1.z.string().uuid(),
    tools: zod_1.z.array(zod_1.z.string().uuid()),
    memory: zod_1.z.string().uuid().optional(),
    systemMessage: zod_1.z.string().optional(),
    maxIterations: zod_1.z.number().default(10),
    temperature: zod_1.z.number().min(0).max(2).default(0.7),
    topP: zod_1.z.number().min(0).max(1).default(1),
    frequencyPenalty: zod_1.z.number().min(-2).max(2).default(0),
    presencePenalty: zod_1.z.number().min(-2).max(2).default(0),
    stopSequences: zod_1.z.array(zod_1.z.string()).default([]),
    userId: zod_1.z.string().uuid(),
    workspaceId: zod_1.z.string().uuid().optional(),
    isActive: zod_1.z.boolean().default(true),
    metadata: zod_1.z.record(zod_1.z.any()).optional()
});
// Multi-agent system schema
exports.MultiAgentSystemSchema = common_1.BaseEntitySchema.extend({
    name: zod_1.z.string(),
    description: zod_1.z.string().optional(),
    agents: zod_1.z.array(zod_1.z.string().uuid()),
    orchestrator: zod_1.z.string().uuid().optional(),
    workflow: zod_1.z.record(zod_1.z.any()),
    userId: zod_1.z.string().uuid(),
    workspaceId: zod_1.z.string().uuid().optional(),
    isActive: zod_1.z.boolean().default(true),
    metadata: zod_1.z.record(zod_1.z.any()).optional()
});
// Agent execution schema
exports.AgentExecutionSchema = common_1.BaseEntitySchema.extend({
    agentId: zod_1.z.string().uuid(),
    systemId: zod_1.z.string().uuid().optional(),
    status: zod_1.z.nativeEnum(common_1.ExecutionStatus),
    input: zod_1.z.any(),
    output: zod_1.z.any().optional(),
    error: zod_1.z.string().optional(),
    steps: zod_1.z.array(zod_1.z.object({
        step: zod_1.z.number(),
        action: zod_1.z.string(),
        observation: zod_1.z.string().optional(),
        thought: zod_1.z.string().optional(),
        timestamp: zod_1.z.string().datetime()
    })),
    startTime: zod_1.z.string().datetime(),
    endTime: zod_1.z.string().datetime().optional(),
    executionTime: zod_1.z.number().optional(),
    userId: zod_1.z.string().uuid().optional(),
    sessionId: zod_1.z.string().optional(),
    metadata: zod_1.z.record(zod_1.z.any()).optional()
});
// Tool schema
exports.ToolSchema = common_1.BaseEntitySchema.extend({
    name: zod_1.z.string(),
    description: zod_1.z.string(),
    type: zod_1.z.enum(['api', 'function', 'webhook', 'database', 'custom']),
    config: zod_1.z.record(zod_1.z.any()),
    schema: zod_1.z.record(zod_1.z.any()).optional(),
    userId: zod_1.z.string().uuid(),
    workspaceId: zod_1.z.string().uuid().optional(),
    isActive: zod_1.z.boolean().default(true),
    isPublic: zod_1.z.boolean().default(false),
    metadata: zod_1.z.record(zod_1.z.any()).optional()
});
//# sourceMappingURL=agent.js.map