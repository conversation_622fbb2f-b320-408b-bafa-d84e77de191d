{"name": "next-themes", "version": "0.2.1", "main": "./dist/index.js", "module": "./dist/index.module.js", "types": "./dist/index.d.ts", "source": "./src/index.tsx", "license": "MIT", "files": ["dist"], "scripts": {"prepublish": "yarn build", "build": "microbundle --jsx React.createElement --compress --no-sourcemap", "test": "jest __tests__", "test:e2e": "yarn playwright test"}, "dependencies": {}, "peerDependencies": {"next": "*", "react": "*", "react-dom": "*"}, "devDependencies": {"@babel/core": "^7.13.10", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.14.5", "@babel/plugin-proposal-optional-chaining": "^7.11.0", "@babel/preset-env": "^7.13.10", "@babel/preset-react": "^7.12.13", "@babel/preset-typescript": "^7.13.0", "@playwright/test": "^1.21.1", "@testing-library/react": "^11.2.5", "@types/jest": "^26.0.21", "@types/next": "^9.0.0", "@types/react": "^16.9.53", "babel-jest": "^26.6.3", "jest": "^26.6.3", "microbundle": "^0.15.0", "prettier": "^2.2.1", "react": "^17.0.1", "react-dom": "^17.0.1", "typescript": "^4.0.3"}, "repository": {"type": "git", "url": "https://github.com/pacocoursey/next-themes.git"}, "prettier": {"semi": false, "singleQuote": true, "trailingComma": "none", "arrowParens": "avoid", "printWidth": 100}}