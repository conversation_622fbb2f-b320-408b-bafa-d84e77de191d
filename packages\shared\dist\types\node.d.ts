import { z } from 'zod';
import { NodeType, ConnectionType, DataType } from './common';
export declare const NodeConnectionSchema: z.ZodObject<{
    id: z.ZodString;
    type: z.ZodNativeEnum<typeof ConnectionType>;
    dataType: z.<PERSON><typeof DataType>;
    label: z.ZodString;
    required: z.ZodDefault<z.ZodBoolean>;
    multiple: z.ZodDefault<z.ZodBoolean>;
}, "strip", z.ZodTypeAny, {
    type: ConnectionType;
    id: string;
    dataType: DataType;
    label: string;
    required: boolean;
    multiple: boolean;
}, {
    type: ConnectionType;
    id: string;
    dataType: DataType;
    label: string;
    required?: boolean | undefined;
    multiple?: boolean | undefined;
}>;
export type NodeConnection = z.infer<typeof NodeConnectionSchema>;
export declare const NodeConfigSchema: z.ZodRecord<z.ZodString, z.ZodAny>;
export type NodeConfig = z.infer<typeof NodeConfigSchema>;
export declare const NodeSchema: z.ZodObject<{
    id: z.ZodString;
    createdAt: z.ZodString;
    updatedAt: z.ZodString;
} & {
    type: z.ZodNativeEnum<typeof NodeType>;
    name: z.ZodString;
    label: z.ZodString;
    description: z.ZodOptional<z.ZodString>;
    category: z.ZodString;
    version: z.ZodDefault<z.ZodString>;
    position: z.ZodObject<{
        x: z.ZodNumber;
        y: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        x: number;
        y: number;
    }, {
        x: number;
        y: number;
    }>;
    inputs: z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        type: z.ZodNativeEnum<typeof ConnectionType>;
        dataType: z.ZodNativeEnum<typeof DataType>;
        label: z.ZodString;
        required: z.ZodDefault<z.ZodBoolean>;
        multiple: z.ZodDefault<z.ZodBoolean>;
    }, "strip", z.ZodTypeAny, {
        type: ConnectionType;
        id: string;
        dataType: DataType;
        label: string;
        required: boolean;
        multiple: boolean;
    }, {
        type: ConnectionType;
        id: string;
        dataType: DataType;
        label: string;
        required?: boolean | undefined;
        multiple?: boolean | undefined;
    }>, "many">;
    outputs: z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        type: z.ZodNativeEnum<typeof ConnectionType>;
        dataType: z.ZodNativeEnum<typeof DataType>;
        label: z.ZodString;
        required: z.ZodDefault<z.ZodBoolean>;
        multiple: z.ZodDefault<z.ZodBoolean>;
    }, "strip", z.ZodTypeAny, {
        type: ConnectionType;
        id: string;
        dataType: DataType;
        label: string;
        required: boolean;
        multiple: boolean;
    }, {
        type: ConnectionType;
        id: string;
        dataType: DataType;
        label: string;
        required?: boolean | undefined;
        multiple?: boolean | undefined;
    }>, "many">;
    config: z.ZodRecord<z.ZodString, z.ZodAny>;
    flowId: z.ZodString;
    isCustom: z.ZodDefault<z.ZodBoolean>;
    icon: z.ZodOptional<z.ZodString>;
    color: z.ZodOptional<z.ZodString>;
    documentation: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    type: NodeType;
    id: string;
    createdAt: string;
    updatedAt: string;
    label: string;
    name: string;
    category: string;
    version: string;
    position: {
        x: number;
        y: number;
    };
    inputs: {
        type: ConnectionType;
        id: string;
        dataType: DataType;
        label: string;
        required: boolean;
        multiple: boolean;
    }[];
    outputs: {
        type: ConnectionType;
        id: string;
        dataType: DataType;
        label: string;
        required: boolean;
        multiple: boolean;
    }[];
    config: Record<string, any>;
    flowId: string;
    isCustom: boolean;
    description?: string | undefined;
    icon?: string | undefined;
    color?: string | undefined;
    documentation?: string | undefined;
}, {
    type: NodeType;
    id: string;
    createdAt: string;
    updatedAt: string;
    label: string;
    name: string;
    category: string;
    position: {
        x: number;
        y: number;
    };
    inputs: {
        type: ConnectionType;
        id: string;
        dataType: DataType;
        label: string;
        required?: boolean | undefined;
        multiple?: boolean | undefined;
    }[];
    outputs: {
        type: ConnectionType;
        id: string;
        dataType: DataType;
        label: string;
        required?: boolean | undefined;
        multiple?: boolean | undefined;
    }[];
    config: Record<string, any>;
    flowId: string;
    description?: string | undefined;
    version?: string | undefined;
    isCustom?: boolean | undefined;
    icon?: string | undefined;
    color?: string | undefined;
    documentation?: string | undefined;
}>;
export type Node = z.infer<typeof NodeSchema>;
export declare const FlowConnectionSchema: z.ZodObject<{
    id: z.ZodString;
    sourceNodeId: z.ZodString;
    sourceHandle: z.ZodString;
    targetNodeId: z.ZodString;
    targetHandle: z.ZodString;
    flowId: z.ZodString;
}, "strip", z.ZodTypeAny, {
    id: string;
    flowId: string;
    sourceNodeId: string;
    sourceHandle: string;
    targetNodeId: string;
    targetHandle: string;
}, {
    id: string;
    flowId: string;
    sourceNodeId: string;
    sourceHandle: string;
    targetNodeId: string;
    targetHandle: string;
}>;
export type FlowConnection = z.infer<typeof FlowConnectionSchema>;
export declare const NodeExecutionResultSchema: z.ZodObject<{
    nodeId: z.ZodString;
    status: z.ZodEnum<["success", "error", "pending"]>;
    output: z.ZodOptional<z.ZodAny>;
    error: z.ZodOptional<z.ZodString>;
    executionTime: z.ZodOptional<z.ZodNumber>;
    timestamp: z.ZodString;
}, "strip", z.ZodTypeAny, {
    status: "pending" | "success" | "error";
    nodeId: string;
    timestamp: string;
    output?: any;
    error?: string | undefined;
    executionTime?: number | undefined;
}, {
    status: "pending" | "success" | "error";
    nodeId: string;
    timestamp: string;
    output?: any;
    error?: string | undefined;
    executionTime?: number | undefined;
}>;
export type NodeExecutionResult = z.infer<typeof NodeExecutionResultSchema>;
export declare const NodeTemplateSchema: z.ZodObject<{
    type: z.ZodNativeEnum<typeof NodeType>;
    name: z.ZodString;
    label: z.ZodString;
    description: z.ZodString;
    category: z.ZodString;
    version: z.ZodString;
    inputs: z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        type: z.ZodNativeEnum<typeof ConnectionType>;
        dataType: z.ZodNativeEnum<typeof DataType>;
        label: z.ZodString;
        required: z.ZodDefault<z.ZodBoolean>;
        multiple: z.ZodDefault<z.ZodBoolean>;
    }, "strip", z.ZodTypeAny, {
        type: ConnectionType;
        id: string;
        dataType: DataType;
        label: string;
        required: boolean;
        multiple: boolean;
    }, {
        type: ConnectionType;
        id: string;
        dataType: DataType;
        label: string;
        required?: boolean | undefined;
        multiple?: boolean | undefined;
    }>, "many">;
    outputs: z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        type: z.ZodNativeEnum<typeof ConnectionType>;
        dataType: z.ZodNativeEnum<typeof DataType>;
        label: z.ZodString;
        required: z.ZodDefault<z.ZodBoolean>;
        multiple: z.ZodDefault<z.ZodBoolean>;
    }, "strip", z.ZodTypeAny, {
        type: ConnectionType;
        id: string;
        dataType: DataType;
        label: string;
        required: boolean;
        multiple: boolean;
    }, {
        type: ConnectionType;
        id: string;
        dataType: DataType;
        label: string;
        required?: boolean | undefined;
        multiple?: boolean | undefined;
    }>, "many">;
    defaultConfig: z.ZodRecord<z.ZodString, z.ZodAny>;
    icon: z.ZodOptional<z.ZodString>;
    color: z.ZodOptional<z.ZodString>;
    documentation: z.ZodOptional<z.ZodString>;
    isCustom: z.ZodDefault<z.ZodBoolean>;
}, "strip", z.ZodTypeAny, {
    type: NodeType;
    label: string;
    name: string;
    description: string;
    category: string;
    version: string;
    inputs: {
        type: ConnectionType;
        id: string;
        dataType: DataType;
        label: string;
        required: boolean;
        multiple: boolean;
    }[];
    outputs: {
        type: ConnectionType;
        id: string;
        dataType: DataType;
        label: string;
        required: boolean;
        multiple: boolean;
    }[];
    isCustom: boolean;
    defaultConfig: Record<string, any>;
    icon?: string | undefined;
    color?: string | undefined;
    documentation?: string | undefined;
}, {
    type: NodeType;
    label: string;
    name: string;
    description: string;
    category: string;
    version: string;
    inputs: {
        type: ConnectionType;
        id: string;
        dataType: DataType;
        label: string;
        required?: boolean | undefined;
        multiple?: boolean | undefined;
    }[];
    outputs: {
        type: ConnectionType;
        id: string;
        dataType: DataType;
        label: string;
        required?: boolean | undefined;
        multiple?: boolean | undefined;
    }[];
    defaultConfig: Record<string, any>;
    isCustom?: boolean | undefined;
    icon?: string | undefined;
    color?: string | undefined;
    documentation?: string | undefined;
}>;
export type NodeTemplate = z.infer<typeof NodeTemplateSchema>;
//# sourceMappingURL=node.d.ts.map