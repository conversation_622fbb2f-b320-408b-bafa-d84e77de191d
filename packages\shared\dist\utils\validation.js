"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateData = validateData;
exports.safeParseData = safeParseData;
exports.validateArray = validateArray;
const zod_1 = require("zod");
/**
 * Validates data against a Zod schema and returns a result object
 */
function validateData(schema, data) {
    try {
        const result = schema.parse(data);
        return { success: true, data: result };
    }
    catch (error) {
        if (error instanceof zod_1.z.ZodError) {
            const errors = error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
            return { success: false, errors };
        }
        return { success: false, errors: ['Unknown validation error'] };
    }
}
/**
 * Safely parses data with a Zod schema, returning null on error
 */
function safeParseData(schema, data) {
    try {
        return schema.parse(data);
    }
    catch {
        return null;
    }
}
/**
 * Validates an array of data against a schema
 */
function validateArray(schema, data) {
    const arraySchema = zod_1.z.array(schema);
    return validateData(arraySchema, data);
}
//# sourceMappingURL=validation.js.map