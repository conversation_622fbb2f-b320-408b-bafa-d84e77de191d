(()=>{var e={};e.id=931,e.ids=[931],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},844:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>h,originalPathname:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c}),t(6599),t(2367),t(5999);var r=t(7188),a=t(8659),l=t(2067),i=t.n(l),n=t(9786),d={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let c=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,6599)),"C:\\Users\\<USER>\\Documents\\augment-projects\\agent_framework\\packages\\ui\\src\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,2367)),"C:\\Users\\<USER>\\Documents\\augment-projects\\agent_framework\\packages\\ui\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,5999,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\Users\\<USER>\\Documents\\augment-projects\\agent_framework\\packages\\ui\\src\\app\\page.tsx"],x="/page",h={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},294:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,3143,23)),Promise.resolve().then(t.t.bind(t,3627,23)),Promise.resolve().then(t.t.bind(t,8958,23)),Promise.resolve().then(t.t.bind(t,9501,23)),Promise.resolve().then(t.t.bind(t,543,23)),Promise.resolve().then(t.t.bind(t,8789,23))},1459:(e,s,t)=>{Promise.resolve().then(t.bind(t,6600)),Promise.resolve().then(t.bind(t,5682))},3130:(e,s,t)=>{Promise.resolve().then(t.bind(t,9822))},7756:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(3509).Z)("Bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]])},9822:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>g});var r=t(7491),a=t(1082),l=t(2812),i=t(3651),n=t(7756),d=t(3509);let c=(0,d.Z)("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]]),o=(0,d.Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]),x=(0,d.Z)("Workflow",[["rect",{width:"8",height:"8",x:"3",y:"3",rx:"2",key:"by2w9f"}],["path",{d:"M7 11v4a2 2 0 0 0 2 2h4",key:"xkn7yn"}],["rect",{width:"8",height:"8",x:"13",y:"13",rx:"2",key:"1cgmvn"}]]),h=(0,d.Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),m=(0,d.Z)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]),u=(0,d.Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]);var p=t(3594);function g(){let e=(0,a.useRouter)();return(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50",children:[r.jsx("header",{className:"border-b bg-white/80 backdrop-blur-sm",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 py-4 flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(n.Z,{className:"h-8 w-8 text-blue-600"}),r.jsx("span",{className:"text-xl font-bold",children:"Flowwise Clone"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[r.jsx(l.z,{variant:"ghost",asChild:!0,children:r.jsx(p.default,{href:"/docs",children:"Documentation"})}),r.jsx(l.z,{variant:"ghost",asChild:!0,children:(0,r.jsxs)(p.default,{href:"https://github.com/your-repo/flowwise-clone",target:"_blank",children:[r.jsx(c,{className:"h-4 w-4 mr-2"}),"GitHub"]})}),r.jsx(l.z,{asChild:!0,children:r.jsx(p.default,{href:"/flows",children:"Get Started"})})]})]})}),r.jsx("section",{className:"container mx-auto px-4 py-20 text-center",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,r.jsxs)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:["Build AI Agents,"," ",r.jsx("span",{className:"text-blue-600",children:"Visually"})]}),r.jsx("p",{className:"text-xl text-gray-600 mb-8 max-w-2xl mx-auto",children:"Create powerful AI agents, chatbots, and multi-agent systems through an intuitive drag-and-drop interface. No coding required."}),(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-4",children:[(0,r.jsxs)(l.z,{size:"lg",onClick:()=>e.push("/flows"),children:["Start Building",r.jsx(o,{className:"ml-2 h-4 w-4"})]}),r.jsx(l.z,{variant:"outline",size:"lg",asChild:!0,children:r.jsx(p.default,{href:"/templates",children:"View Templates"})})]})]})}),(0,r.jsxs)("section",{className:"container mx-auto px-4 py-20",children:[(0,r.jsxs)("div",{className:"text-center mb-16",children:[r.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Everything you need to build AI agents"}),r.jsx("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"From simple chatbots to complex multi-agent systems, our platform provides all the tools you need."})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[(0,r.jsxs)(i.Zb,{children:[(0,r.jsxs)(i.Ol,{children:[r.jsx(x,{className:"h-8 w-8 text-blue-600 mb-2"}),r.jsx(i.ll,{children:"Visual Flow Builder"}),r.jsx(i.SZ,{children:"Drag-and-drop interface for building complex AI workflows without code"})]}),r.jsx(i.aY,{children:(0,r.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[r.jsx("li",{children:"• Node-based editor"}),r.jsx("li",{children:"• Real-time validation"}),r.jsx("li",{children:"• Template library"})]})})]}),(0,r.jsxs)(i.Zb,{children:[(0,r.jsxs)(i.Ol,{children:[r.jsx(h,{className:"h-8 w-8 text-green-600 mb-2"}),r.jsx(i.ll,{children:"Multi-Agent Systems"}),r.jsx(i.SZ,{children:"Coordinate multiple AI agents for complex task orchestration"})]}),r.jsx(i.aY,{children:(0,r.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[r.jsx("li",{children:"• Agent coordination"}),r.jsx("li",{children:"• Task distribution"}),r.jsx("li",{children:"• Workflow management"})]})})]}),(0,r.jsxs)(i.Zb,{children:[(0,r.jsxs)(i.Ol,{children:[r.jsx(n.Z,{className:"h-8 w-8 text-purple-600 mb-2"}),r.jsx(i.ll,{children:"LLM Integrations"}),r.jsx(i.SZ,{children:"Connect to OpenAI, Anthropic, Google, and other LLM providers"})]}),r.jsx(i.aY,{children:(0,r.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[r.jsx("li",{children:"• Multiple providers"}),r.jsx("li",{children:"• Streaming support"}),r.jsx("li",{children:"• Custom models"})]})})]}),(0,r.jsxs)(i.Zb,{children:[(0,r.jsxs)(i.Ol,{children:[r.jsx(m,{className:"h-8 w-8 text-yellow-600 mb-2"}),r.jsx(i.ll,{children:"RAG & Vector Search"}),r.jsx(i.SZ,{children:"Integrate with vector databases for knowledge retrieval"})]}),r.jsx(i.aY,{children:(0,r.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[r.jsx("li",{children:"• Document processing"}),r.jsx("li",{children:"• Vector embeddings"}),r.jsx("li",{children:"• Semantic search"})]})})]}),(0,r.jsxs)(i.Zb,{children:[(0,r.jsxs)(i.Ol,{children:[r.jsx(u,{className:"h-8 w-8 text-orange-600 mb-2"}),r.jsx(i.ll,{children:"Human-in-the-Loop"}),r.jsx(i.SZ,{children:"Add human approval workflows and manual intervention"})]}),r.jsx(i.aY,{children:(0,r.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[r.jsx("li",{children:"• Approval workflows"}),r.jsx("li",{children:"• Task review"}),r.jsx("li",{children:"• Manual override"})]})})]}),(0,r.jsxs)(i.Zb,{children:[(0,r.jsxs)(i.Ol,{children:[r.jsx(o,{className:"h-8 w-8 text-red-600 mb-2"}),r.jsx(i.ll,{children:"API & Integrations"}),r.jsx(i.SZ,{children:"REST APIs, SDKs, and embedded chat widgets"})]}),r.jsx(i.aY,{children:(0,r.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[r.jsx("li",{children:"• REST API"}),r.jsx("li",{children:"• TypeScript SDK"}),r.jsx("li",{children:"• Embedded widgets"})]})})]})]})]}),r.jsx("section",{className:"bg-blue-600 text-white py-20",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 text-center",children:[r.jsx("h2",{className:"text-3xl font-bold mb-4",children:"Ready to build your first AI agent?"}),r.jsx("p",{className:"text-xl mb-8 opacity-90",children:"Join thousands of developers building the future of AI"}),(0,r.jsxs)(l.z,{size:"lg",variant:"secondary",onClick:()=>e.push("/flows"),children:["Get Started for Free",r.jsx(o,{className:"ml-2 h-4 w-4"})]})]})}),r.jsx("footer",{className:"bg-gray-900 text-white py-12",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[r.jsx(n.Z,{className:"h-6 w-6"}),r.jsx("span",{className:"text-lg font-bold",children:"Flowwise Clone"})]}),r.jsx("p",{className:"text-gray-400",children:"Visual AI agent builder for the modern web"})]}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"font-semibold mb-4",children:"Product"}),(0,r.jsxs)("ul",{className:"space-y-2 text-gray-400",children:[r.jsx("li",{children:r.jsx(p.default,{href:"/features",children:"Features"})}),r.jsx("li",{children:r.jsx(p.default,{href:"/templates",children:"Templates"})}),r.jsx("li",{children:r.jsx(p.default,{href:"/pricing",children:"Pricing"})})]})]}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"font-semibold mb-4",children:"Resources"}),(0,r.jsxs)("ul",{className:"space-y-2 text-gray-400",children:[r.jsx("li",{children:r.jsx(p.default,{href:"/docs",children:"Documentation"})}),r.jsx("li",{children:r.jsx(p.default,{href:"/examples",children:"Examples"})}),r.jsx("li",{children:r.jsx(p.default,{href:"/community",children:"Community"})})]})]}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"font-semibold mb-4",children:"Company"}),(0,r.jsxs)("ul",{className:"space-y-2 text-gray-400",children:[r.jsx("li",{children:r.jsx(p.default,{href:"/about",children:"About"})}),r.jsx("li",{children:r.jsx(p.default,{href:"/contact",children:"Contact"})}),r.jsx("li",{children:r.jsx(p.default,{href:"/privacy",children:"Privacy"})})]})]})]}),r.jsx("div",{className:"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400",children:r.jsx("p",{children:"\xa9 2024 Flowwise Clone. All rights reserved."})})]})})]})}},5682:(e,s,t)=>{"use strict";t.d(s,{Providers:()=>c});var r=t(7491),a=t(843),l=t(5668),i=t(675),n=t(2781),d=t(6364);function c({children:e}){let[s]=(0,d.useState)(()=>new a.S({defaultOptions:{queries:{staleTime:6e4,retry:1}}}));return(0,r.jsxs)(l.aH,{client:s,children:[r.jsx(n.f,{attribute:"class",defaultTheme:"light",enableSystem:!0,disableTransitionOnChange:!0,children:e}),r.jsx(i.t,{initialIsOpen:!1})]})}},2812:(e,s,t)=>{"use strict";t.d(s,{z:()=>c});var r=t(7491),a=t(6364),l=t(7723),i=t(4295),n=t(7107);let d=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef(({className:e,variant:s,size:t,asChild:a=!1,...i},c)=>{let o=a?l.g7:"button";return r.jsx(o,{className:(0,n.cn)(d({variant:s,size:t,className:e})),ref:c,...i})});c.displayName="Button"},3651:(e,s,t)=>{"use strict";t.d(s,{Ol:()=>n,SZ:()=>c,Zb:()=>i,aY:()=>o,ll:()=>d});var r=t(7491),a=t(6364),l=t(7107);let i=a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));i.displayName="Card";let n=a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",e),...s}));n.displayName="CardHeader";let d=a.forwardRef(({className:e,...s},t)=>r.jsx("h3",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));d.displayName="CardTitle";let c=a.forwardRef(({className:e,...s},t)=>r.jsx("p",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",e),...s}));c.displayName="CardDescription";let o=a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,l.cn)("p-6 pt-0",e),...s}));o.displayName="CardContent",a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},7107:(e,s,t)=>{"use strict";t.d(s,{Ox:()=>n,cn:()=>l,p6:()=>i});var r=t(7672),a=t(2154);function l(...e){return(0,a.m6)((0,r.W)(e))}function i(e){return new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})}function n(){return Math.random().toString(36).substr(2,9)}},2367:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>c,metadata:()=>d});var r=t(4519),a=t(1283),l=t.n(a);t(1369);let i=(0,t(4798).createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\agent_framework\packages\ui\src\components\providers.tsx#Providers`);var n=t(6533);let d={title:"Flowwise Clone - Visual AI Agent Builder",description:"Build AI agents, chatbots, and multi-agent systems visually through a drag-and-drop interface.",keywords:["AI","chatbot","agent","visual builder","no-code","LLM"],authors:[{name:"Flowwise Clone Team"}],viewport:"width=device-width, initial-scale=1",themeColor:"#3B82F6"};function c({children:e}){return r.jsx("html",{lang:"en",suppressHydrationWarning:!0,children:r.jsx("body",{className:l().className,children:(0,r.jsxs)(i,{children:[e,r.jsx(n.x7,{position:"top-right",toastOptions:{duration:4e3,style:{background:"hsl(var(--card))",color:"hsl(var(--card-foreground))",border:"1px solid hsl(var(--border))"}}})]})})})}},6599:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(4798).createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\agent_framework\packages\ui\src\app\page.tsx#default`)},1369:()=>{}};var s=require("../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[903,763,594],()=>t(844));module.exports=r})();