import { z } from 'zod';
export declare enum UserRole {
    ADMIN = "admin",
    USER = "user",
    VIEWER = "viewer"
}
export declare const UserSchema: z.ZodObject<{
    id: z.ZodString;
    createdAt: z.ZodString;
    updatedAt: z.ZodString;
} & {
    email: z.ZodString;
    name: z.ZodString;
    avatar: z.ZodOptional<z.ZodString>;
    role: z.ZodNativeEnum<typeof UserRole>;
    isActive: z.ZodDefault<z.ZodBoolean>;
    lastLoginAt: z.ZodOptional<z.ZodString>;
    preferences: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: string;
    updatedAt: string;
    name: string;
    role: UserRole;
    isActive: boolean;
    email: string;
    avatar?: string | undefined;
    lastLoginAt?: string | undefined;
    preferences?: Record<string, any> | undefined;
}, {
    id: string;
    createdAt: string;
    updatedAt: string;
    name: string;
    role: User<PERSON>ole;
    email: string;
    isActive?: boolean | undefined;
    avatar?: string | undefined;
    lastLoginAt?: string | undefined;
    preferences?: Record<string, any> | undefined;
}>;
export type User = z.infer<typeof UserSchema>;
export declare const WorkspaceSchema: z.ZodObject<{
    id: z.ZodString;
    createdAt: z.ZodString;
    updatedAt: z.ZodString;
} & {
    name: z.ZodString;
    description: z.ZodOptional<z.ZodString>;
    ownerId: z.ZodString;
    isActive: z.ZodDefault<z.ZodBoolean>;
    settings: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: string;
    updatedAt: string;
    name: string;
    isActive: boolean;
    ownerId: string;
    description?: string | undefined;
    settings?: Record<string, any> | undefined;
}, {
    id: string;
    createdAt: string;
    updatedAt: string;
    name: string;
    ownerId: string;
    description?: string | undefined;
    isActive?: boolean | undefined;
    settings?: Record<string, any> | undefined;
}>;
export type Workspace = z.infer<typeof WorkspaceSchema>;
export declare const WorkspaceMemberSchema: z.ZodObject<{
    id: z.ZodString;
    createdAt: z.ZodString;
    updatedAt: z.ZodString;
} & {
    workspaceId: z.ZodString;
    userId: z.ZodString;
    role: z.ZodNativeEnum<typeof UserRole>;
    joinedAt: z.ZodString;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: string;
    updatedAt: string;
    userId: string;
    workspaceId: string;
    role: UserRole;
    joinedAt: string;
}, {
    id: string;
    createdAt: string;
    updatedAt: string;
    userId: string;
    workspaceId: string;
    role: UserRole;
    joinedAt: string;
}>;
export type WorkspaceMember = z.infer<typeof WorkspaceMemberSchema>;
export declare const ApiKeySchema: z.ZodObject<{
    id: z.ZodString;
    createdAt: z.ZodString;
    updatedAt: z.ZodString;
} & {
    name: z.ZodString;
    key: z.ZodString;
    userId: z.ZodString;
    workspaceId: z.ZodOptional<z.ZodString>;
    isActive: z.ZodDefault<z.ZodBoolean>;
    expiresAt: z.ZodOptional<z.ZodString>;
    lastUsedAt: z.ZodOptional<z.ZodString>;
    permissions: z.ZodDefault<z.ZodArray<z.ZodString, "many">>;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: string;
    updatedAt: string;
    name: string;
    userId: string;
    isActive: boolean;
    key: string;
    permissions: string[];
    workspaceId?: string | undefined;
    expiresAt?: string | undefined;
    lastUsedAt?: string | undefined;
}, {
    id: string;
    createdAt: string;
    updatedAt: string;
    name: string;
    userId: string;
    key: string;
    workspaceId?: string | undefined;
    isActive?: boolean | undefined;
    expiresAt?: string | undefined;
    lastUsedAt?: string | undefined;
    permissions?: string[] | undefined;
}>;
export type ApiKey = z.infer<typeof ApiKeySchema>;
//# sourceMappingURL=user.d.ts.map