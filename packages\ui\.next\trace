[{"name": "generate-buildid", "duration": 547, "timestamp": 648503402936, "id": 4, "parentId": 1, "tags": {}, "startTime": 1751124633141, "traceId": "8fda2455562bee8b"}, {"name": "load-custom-routes", "duration": 464, "timestamp": 648503403873, "id": 5, "parentId": 1, "tags": {}, "startTime": 1751124633142, "traceId": "8fda2455562bee8b"}, {"name": "create-pages-mapping", "duration": 371, "timestamp": 648503546032, "id": 6, "parentId": 1, "tags": {}, "startTime": 1751124633284, "traceId": "8fda2455562bee8b"}, {"name": "collect-app-paths", "duration": 3334, "timestamp": 648503546471, "id": 7, "parentId": 1, "tags": {}, "startTime": 1751124633284, "traceId": "8fda2455562bee8b"}, {"name": "create-app-mapping", "duration": 831, "timestamp": 648503549847, "id": 8, "parentId": 1, "tags": {}, "startTime": 1751124633288, "traceId": "8fda2455562bee8b"}, {"name": "public-dir-conflict-check", "duration": 859, "timestamp": 648503551168, "id": 9, "parentId": 1, "tags": {}, "startTime": 1751124633289, "traceId": "8fda2455562bee8b"}, {"name": "generate-routes-manifest", "duration": 3905, "timestamp": 648503552312, "id": 10, "parentId": 1, "tags": {}, "startTime": 1751124633290, "traceId": "8fda2455562bee8b"}, {"name": "create-dist-dir", "duration": 542, "timestamp": 648503557452, "id": 11, "parentId": 1, "tags": {}, "startTime": 1751124633295, "traceId": "8fda2455562bee8b"}, {"name": "write-routes-manifest", "duration": 1506, "timestamp": 648503588154, "id": 12, "parentId": 1, "tags": {}, "startTime": 1751124633326, "traceId": "8fda2455562bee8b"}, {"name": "generate-required-server-files", "duration": 658, "timestamp": 648503589891, "id": 13, "parentId": 1, "tags": {}, "startTime": 1751124633328, "traceId": "8fda2455562bee8b"}, {"name": "create-entrypoints", "duration": 187381, "timestamp": 648503605357, "id": 16, "parentId": 1, "tags": {}, "startTime": 1751124633343, "traceId": "8fda2455562bee8b"}, {"name": "generate-webpack-config", "duration": 1000635, "timestamp": 648503792817, "id": 17, "parentId": 15, "tags": {}, "startTime": 1751124633531, "traceId": "8fda2455562bee8b"}, {"name": "next-trace-entrypoint-plugin", "duration": 3618, "timestamp": 648504934440, "id": 19, "parentId": 18, "tags": {}, "startTime": 1751124634672, "traceId": "8fda2455562bee8b"}, {"name": "add-entry", "duration": 594458, "timestamp": 648504947776, "id": 22, "parentId": 20, "tags": {"request": "next/dist/pages/_app"}, "startTime": 1751124634686, "traceId": "8fda2455562bee8b"}, {"name": "add-entry", "duration": 721389, "timestamp": 648504947887, "id": 27, "parentId": 20, "tags": {"request": "next/dist/pages/_document"}, "startTime": 1751124634686, "traceId": "8fda2455562bee8b"}, {"name": "add-entry", "duration": 727020, "timestamp": 648504947812, "id": 23, "parentId": 20, "tags": {"request": "next-route-loader?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=next%2Fdist%2Fpages%2F_error&absoluteAppPath=next%2Fdist%2Fpages%2F_app&absoluteDocumentPath=next%2Fdist%2Fpages%2F_document&middlewareConfigBase64=e30%3D!"}, "startTime": 1751124634686, "traceId": "8fda2455562bee8b"}, {"name": "add-entry", "duration": 739005, "timestamp": 648504947129, "id": 21, "parentId": 20, "tags": {"request": "next-app-loader?page=%2F_not-found%2Fpage&name=app%2F_not-found%2Fpage&pagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&appDir=C%3A%5CUsers%5CAgaran%5CDocuments%5Caugment-projects%5Cagent_framework%5Cpackages%5Cui%5Csrc%5Capp&appPaths=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751124634685, "traceId": "8fda2455562bee8b"}, {"name": "add-entry", "duration": 738321, "timestamp": 648504947832, "id": 24, "parentId": 20, "tags": {"request": "next-app-loader?page=%2Fflows%2F%5Bid%5D%2Fpage&name=app%2Fflows%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fflows%2F%5Bid%5D%2Fpage.tsx&appDir=C%3A%5CUsers%5CAgaran%5CDocuments%5Caugment-projects%5Cagent_framework%5Cpackages%5Cui%5Csrc%5Capp&appPaths=%2Fflows%2F%5Bid%5D%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751124634686, "traceId": "8fda2455562bee8b"}, {"name": "add-entry", "duration": 738306, "timestamp": 648504947852, "id": 25, "parentId": 20, "tags": {"request": "next-app-loader?page=%2Fflows%2Fpage&name=app%2Fflows%2Fpage&pagePath=private-next-app-dir%2Fflows%2Fpage.tsx&appDir=C%3A%5CUsers%5CAgaran%5CDocuments%5Caugment-projects%5Cagent_framework%5Cpackages%5Cui%5Csrc%5Capp&appPaths=%2Fflows%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751124634686, "traceId": "8fda2455562bee8b"}, {"name": "add-entry", "duration": 738293, "timestamp": 648504947869, "id": 26, "parentId": 20, "tags": {"request": "next-app-loader?page=%2Fpage&name=app%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CAgaran%5CDocuments%5Caugment-projects%5Cagent_framework%5Cpackages%5Cui%5Csrc%5Capp&appPaths=%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751124634686, "traceId": "8fda2455562bee8b"}, {"name": "make", "duration": 1717839, "timestamp": 648504946574, "id": 20, "parentId": 18, "tags": {}, "startTime": 1751124634684, "traceId": "8fda2455562bee8b"}, {"name": "get-entries", "duration": 1655, "timestamp": 648506666388, "id": 47, "parentId": 46, "tags": {}, "startTime": 1751124636404, "traceId": "8fda2455562bee8b"}, {"name": "node-file-trace-plugin", "duration": 103775, "timestamp": 648506672169, "id": 48, "parentId": 46, "tags": {"traceEntryCount": "10"}, "startTime": 1751124636410, "traceId": "8fda2455562bee8b"}, {"name": "collect-traced-files", "duration": 671, "timestamp": 648506775963, "id": 49, "parentId": 46, "tags": {}, "startTime": 1751124636514, "traceId": "8fda2455562bee8b"}, {"name": "finish-modules", "duration": 110554, "timestamp": 648506666095, "id": 46, "parentId": 19, "tags": {}, "startTime": 1751124636404, "traceId": "8fda2455562bee8b"}, {"name": "chunk-graph", "duration": 16007, "timestamp": 648506838503, "id": 51, "parentId": 50, "tags": {}, "startTime": 1751124636576, "traceId": "8fda2455562bee8b"}, {"name": "optimize-modules", "duration": 47, "timestamp": 648506854700, "id": 53, "parentId": 50, "tags": {}, "startTime": 1751124636592, "traceId": "8fda2455562bee8b"}, {"name": "optimize-chunks", "duration": 18916, "timestamp": 648506854885, "id": 54, "parentId": 50, "tags": {}, "startTime": 1751124636593, "traceId": "8fda2455562bee8b"}, {"name": "optimize-tree", "duration": 242, "timestamp": 648506873934, "id": 55, "parentId": 50, "tags": {}, "startTime": 1751124636612, "traceId": "8fda2455562bee8b"}, {"name": "optimize-chunk-modules", "duration": 41495, "timestamp": 648506874322, "id": 56, "parentId": 50, "tags": {}, "startTime": 1751124636612, "traceId": "8fda2455562bee8b"}, {"name": "optimize", "duration": 61399, "timestamp": 648506854620, "id": 52, "parentId": 50, "tags": {}, "startTime": 1751124636592, "traceId": "8fda2455562bee8b"}, {"name": "module-hash", "duration": 28306, "timestamp": 648506948595, "id": 57, "parentId": 50, "tags": {}, "startTime": 1751124636686, "traceId": "8fda2455562bee8b"}, {"name": "code-generation", "duration": 5263, "timestamp": 648506977001, "id": 58, "parentId": 50, "tags": {}, "startTime": 1751124636715, "traceId": "8fda2455562bee8b"}, {"name": "hash", "duration": 11793, "timestamp": 648506989694, "id": 59, "parentId": 50, "tags": {}, "startTime": 1751124636727, "traceId": "8fda2455562bee8b"}, {"name": "code-generation-jobs", "duration": 388, "timestamp": 648507001482, "id": 60, "parentId": 50, "tags": {}, "startTime": 1751124636739, "traceId": "8fda2455562bee8b"}, {"name": "module-assets", "duration": 445, "timestamp": 648507001799, "id": 61, "parentId": 50, "tags": {}, "startTime": 1751124636740, "traceId": "8fda2455562bee8b"}, {"name": "create-chunk-assets", "duration": 2417, "timestamp": 648507002270, "id": 62, "parentId": 50, "tags": {}, "startTime": 1751124636740, "traceId": "8fda2455562bee8b"}, {"name": "minify-js", "duration": 569, "timestamp": 648507019225, "id": 64, "parentId": 63, "tags": {"name": "../app/_not-found/page.js", "cache": "HIT"}, "startTime": 1751124636757, "traceId": "8fda2455562bee8b"}, {"name": "minify-js", "duration": 303, "timestamp": 648507019504, "id": 65, "parentId": 63, "tags": {"name": "../pages/_app.js", "cache": "HIT"}, "startTime": 1751124636757, "traceId": "8fda2455562bee8b"}, {"name": "minify-js", "duration": 286, "timestamp": 648507019523, "id": 66, "parentId": 63, "tags": {"name": "../pages/_error.js", "cache": "HIT"}, "startTime": 1751124636757, "traceId": "8fda2455562bee8b"}, {"name": "minify-js", "duration": 275, "timestamp": 648507019535, "id": 67, "parentId": 63, "tags": {"name": "../app/flows/[id]/page.js", "cache": "HIT"}, "startTime": 1751124636757, "traceId": "8fda2455562bee8b"}, {"name": "minify-js", "duration": 266, "timestamp": 648507019545, "id": 68, "parentId": 63, "tags": {"name": "../app/flows/page.js", "cache": "HIT"}, "startTime": 1751124636757, "traceId": "8fda2455562bee8b"}, {"name": "minify-js", "duration": 257, "timestamp": 648507019555, "id": 69, "parentId": 63, "tags": {"name": "../app/page.js", "cache": "HIT"}, "startTime": 1751124636757, "traceId": "8fda2455562bee8b"}, {"name": "minify-js", "duration": 248, "timestamp": 648507019565, "id": 70, "parentId": 63, "tags": {"name": "../pages/_document.js", "cache": "HIT"}, "startTime": 1751124636757, "traceId": "8fda2455562bee8b"}, {"name": "minify-js", "duration": 239, "timestamp": 648507019577, "id": 71, "parentId": 63, "tags": {"name": "../webpack-runtime.js", "cache": "HIT"}, "startTime": 1751124636757, "traceId": "8fda2455562bee8b"}, {"name": "minify-js", "duration": 229, "timestamp": 648507019588, "id": 72, "parentId": 63, "tags": {"name": "903.js", "cache": "HIT"}, "startTime": 1751124636757, "traceId": "8fda2455562bee8b"}, {"name": "minify-js", "duration": 104, "timestamp": 648507019714, "id": 73, "parentId": 63, "tags": {"name": "763.js", "cache": "HIT"}, "startTime": 1751124636758, "traceId": "8fda2455562bee8b"}, {"name": "minify-js", "duration": 56, "timestamp": 648507019763, "id": 74, "parentId": 63, "tags": {"name": "864.js", "cache": "HIT"}, "startTime": 1751124636758, "traceId": "8fda2455562bee8b"}, {"name": "minify-js", "duration": 41, "timestamp": 648507019779, "id": 75, "parentId": 63, "tags": {"name": "439.js", "cache": "HIT"}, "startTime": 1751124636758, "traceId": "8fda2455562bee8b"}, {"name": "minify-js", "duration": 34, "timestamp": 648507019787, "id": 76, "parentId": 63, "tags": {"name": "594.js", "cache": "HIT"}, "startTime": 1751124636758, "traceId": "8fda2455562bee8b"}, {"name": "terser-webpack-plugin-optimize", "duration": 9907, "timestamp": 648507009922, "id": 63, "parentId": 18, "tags": {"compilationName": "server", "swcMinify": true}, "startTime": 1751124636748, "traceId": "8fda2455562bee8b"}, {"name": "css-minimizer-plugin", "duration": 186, "timestamp": 648507019995, "id": 77, "parentId": 18, "tags": {}, "startTime": 1751124636758, "traceId": "8fda2455562bee8b"}, {"name": "create-trace-assets", "duration": 1222, "timestamp": 648507020447, "id": 78, "parentId": 19, "tags": {}, "startTime": 1751124636758, "traceId": "8fda2455562bee8b"}, {"name": "seal", "duration": 221002, "timestamp": 648506812977, "id": 50, "parentId": 18, "tags": {}, "startTime": 1751124636551, "traceId": "8fda2455562bee8b"}, {"name": "webpack-compilation", "duration": 2108942, "timestamp": 648504931814, "id": 18, "parentId": 15, "tags": {"name": "server"}, "startTime": 1751124634670, "traceId": "8fda2455562bee8b"}, {"name": "emit", "duration": 18363, "timestamp": 648507041469, "id": 79, "parentId": 15, "tags": {}, "startTime": 1751124636779, "traceId": "8fda2455562bee8b"}, {"name": "webpack-close", "duration": 2091, "timestamp": 648507061416, "id": 80, "parentId": 15, "tags": {"name": "server"}, "startTime": 1751124636799, "traceId": "8fda2455562bee8b"}, {"name": "webpack-generate-error-stats", "duration": 4324, "timestamp": 648507063628, "id": 81, "parentId": 80, "tags": {}, "startTime": 1751124636801, "traceId": "8fda2455562bee8b"}, {"name": "make", "duration": 315, "timestamp": 648507081496, "id": 83, "parentId": 82, "tags": {}, "startTime": 1751124636819, "traceId": "8fda2455562bee8b"}, {"name": "chunk-graph", "duration": 40, "timestamp": 648507082848, "id": 85, "parentId": 84, "tags": {}, "startTime": 1751124636821, "traceId": "8fda2455562bee8b"}, {"name": "optimize-modules", "duration": 13, "timestamp": 648507082968, "id": 87, "parentId": 84, "tags": {}, "startTime": 1751124636821, "traceId": "8fda2455562bee8b"}, {"name": "optimize-chunks", "duration": 117, "timestamp": 648507083070, "id": 88, "parentId": 84, "tags": {}, "startTime": 1751124636821, "traceId": "8fda2455562bee8b"}, {"name": "optimize-tree", "duration": 14, "timestamp": 648507083260, "id": 89, "parentId": 84, "tags": {}, "startTime": 1751124636821, "traceId": "8fda2455562bee8b"}, {"name": "optimize-chunk-modules", "duration": 80, "timestamp": 648507083378, "id": 90, "parentId": 84, "tags": {}, "startTime": 1751124636821, "traceId": "8fda2455562bee8b"}, {"name": "optimize", "duration": 599, "timestamp": 648507082917, "id": 86, "parentId": 84, "tags": {}, "startTime": 1751124636821, "traceId": "8fda2455562bee8b"}, {"name": "module-hash", "duration": 23, "timestamp": 648507083779, "id": 91, "parentId": 84, "tags": {}, "startTime": 1751124636822, "traceId": "8fda2455562bee8b"}, {"name": "code-generation", "duration": 17, "timestamp": 648507083819, "id": 92, "parentId": 84, "tags": {}, "startTime": 1751124636822, "traceId": "8fda2455562bee8b"}, {"name": "hash", "duration": 106, "timestamp": 648507083897, "id": 93, "parentId": 84, "tags": {}, "startTime": 1751124636822, "traceId": "8fda2455562bee8b"}, {"name": "code-generation-jobs", "duration": 64, "timestamp": 648507084002, "id": 94, "parentId": 84, "tags": {}, "startTime": 1751124636822, "traceId": "8fda2455562bee8b"}, {"name": "module-assets", "duration": 22, "timestamp": 648507084054, "id": 95, "parentId": 84, "tags": {}, "startTime": 1751124636822, "traceId": "8fda2455562bee8b"}, {"name": "create-chunk-assets", "duration": 22, "timestamp": 648507084084, "id": 96, "parentId": 84, "tags": {}, "startTime": 1751124636822, "traceId": "8fda2455562bee8b"}, {"name": "minify-js", "duration": 92, "timestamp": 648507198729, "id": 98, "parentId": 97, "tags": {"name": "interception-route-rewrite-manifest.js", "cache": "HIT"}, "startTime": 1751124636937, "traceId": "8fda2455562bee8b"}, {"name": "terser-webpack-plugin-optimize", "duration": 71517, "timestamp": 648507127321, "id": 97, "parentId": 82, "tags": {"compilationName": "edge-server", "swcMinify": true}, "startTime": 1751124636865, "traceId": "8fda2455562bee8b"}, {"name": "css-minimizer-plugin", "duration": 12, "timestamp": 648507198949, "id": 99, "parentId": 82, "tags": {}, "startTime": 1751124636937, "traceId": "8fda2455562bee8b"}, {"name": "seal", "duration": 118698, "timestamp": 648507082621, "id": 84, "parentId": 82, "tags": {}, "startTime": 1751124636820, "traceId": "8fda2455562bee8b"}, {"name": "webpack-compilation", "duration": 122188, "timestamp": 648507079284, "id": 82, "parentId": 15, "tags": {"name": "edge-server"}, "startTime": 1751124636817, "traceId": "8fda2455562bee8b"}, {"name": "emit", "duration": 1625, "timestamp": 648507201562, "id": 100, "parentId": 15, "tags": {}, "startTime": 1751124636939, "traceId": "8fda2455562bee8b"}, {"name": "webpack-close", "duration": 312, "timestamp": 648507203523, "id": 101, "parentId": 15, "tags": {"name": "edge-server"}, "startTime": 1751124636941, "traceId": "8fda2455562bee8b"}, {"name": "webpack-generate-error-stats", "duration": 718, "timestamp": 648507203846, "id": 102, "parentId": 101, "tags": {}, "startTime": 1751124636942, "traceId": "8fda2455562bee8b"}, {"name": "add-entry", "duration": 562517, "timestamp": 648507217498, "id": 108, "parentId": 104, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&page=%2F_not-found%2Fpage!"}, "startTime": 1751124636955, "traceId": "8fda2455562bee8b"}, {"name": "add-entry", "duration": 662721, "timestamp": 648507217525, "id": 109, "parentId": 104, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_app&page=%2F_app!"}, "startTime": 1751124636955, "traceId": "8fda2455562bee8b"}, {"name": "add-entry", "duration": 662739, "timestamp": 648507217539, "id": 111, "parentId": 104, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_error&page=%2F_error!"}, "startTime": 1751124636955, "traceId": "8fda2455562bee8b"}, {"name": "add-entry", "duration": 736873, "timestamp": 648507217565, "id": 115, "parentId": 104, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cpackages%5C%5Cui%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1751124636955, "traceId": "8fda2455562bee8b"}, {"name": "add-entry", "duration": 737012, "timestamp": 648507217468, "id": 107, "parentId": 104, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1751124636955, "traceId": "8fda2455562bee8b"}, {"name": "add-entry", "duration": 737156, "timestamp": 648507217328, "id": 106, "parentId": 104, "tags": {"request": "./../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/app-next.js"}, "startTime": 1751124636955, "traceId": "8fda2455562bee8b"}, {"name": "add-entry", "duration": 741132, "timestamp": 648507217543, "id": 112, "parentId": 104, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cnode_modules%5C%5C.pnpm%5C%5Creact-hot-toast%402.5.2_react-dom%4018.3.1_react%4018.3.1%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cpackages%5C%5Cui%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cpackages%5C%5Cui%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=false!"}, "startTime": 1751124636955, "traceId": "8fda2455562bee8b"}, {"name": "add-entry", "duration": 783603, "timestamp": 648507217244, "id": 105, "parentId": 104, "tags": {"request": "./../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/next.js"}, "startTime": 1751124636955, "traceId": "8fda2455562bee8b"}, {"name": "add-entry", "duration": 783335, "timestamp": 648507217533, "id": 110, "parentId": 104, "tags": {"request": "C:\\Users\\<USER>\\Documents\\augment-projects\\agent_framework\\node_modules\\.pnpm\\next@14.2.30_react-dom@18.3.1_react@18.3.1\\node_modules\\next\\dist\\client\\router.js"}, "startTime": 1751124636955, "traceId": "8fda2455562bee8b"}, {"name": "add-entry", "duration": 844116, "timestamp": 648507217560, "id": 114, "parentId": 104, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cpackages%5C%5Cui%5C%5Csrc%5C%5Capp%5C%5Cflows%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1751124636955, "traceId": "8fda2455562bee8b"}, {"name": "add-entry", "duration": 844163, "timestamp": 648507217548, "id": 113, "parentId": 104, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cpackages%5C%5Cui%5C%5Csrc%5C%5Capp%5C%5Cflows%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1751124636955, "traceId": "8fda2455562bee8b"}, {"name": "make", "duration": 845131, "timestamp": 648507216758, "id": 104, "parentId": 103, "tags": {}, "startTime": 1751124636955, "traceId": "8fda2455562bee8b"}, {"name": "chunk-graph", "duration": 12938, "timestamp": 648508108147, "id": 117, "parentId": 116, "tags": {}, "startTime": 1751124637846, "traceId": "8fda2455562bee8b"}, {"name": "optimize-modules", "duration": 21, "timestamp": 648508121182, "id": 119, "parentId": 116, "tags": {}, "startTime": 1751124637859, "traceId": "8fda2455562bee8b"}, {"name": "optimize-chunks", "duration": 28240, "timestamp": 648508125121, "id": 121, "parentId": 116, "tags": {}, "startTime": 1751124637863, "traceId": "8fda2455562bee8b"}, {"name": "optimize-tree", "duration": 19, "timestamp": 648508153428, "id": 122, "parentId": 116, "tags": {}, "startTime": 1751124637891, "traceId": "8fda2455562bee8b"}, {"name": "optimize-chunk-modules", "duration": 35348, "timestamp": 648508153482, "id": 123, "parentId": 116, "tags": {}, "startTime": 1751124637891, "traceId": "8fda2455562bee8b"}, {"name": "optimize", "duration": 67732, "timestamp": 648508121148, "id": 118, "parentId": 116, "tags": {}, "startTime": 1751124637859, "traceId": "8fda2455562bee8b"}, {"name": "module-hash", "duration": 28827, "timestamp": 648508208989, "id": 124, "parentId": 116, "tags": {}, "startTime": 1751124637947, "traceId": "8fda2455562bee8b"}, {"name": "code-generation", "duration": 4601, "timestamp": 648508237873, "id": 125, "parentId": 116, "tags": {}, "startTime": 1751124637976, "traceId": "8fda2455562bee8b"}, {"name": "hash", "duration": 11034, "timestamp": 648508248251, "id": 126, "parentId": 116, "tags": {}, "startTime": 1751124637986, "traceId": "8fda2455562bee8b"}, {"name": "code-generation-jobs", "duration": 230, "timestamp": 648508259282, "id": 127, "parentId": 116, "tags": {}, "startTime": 1751124637997, "traceId": "8fda2455562bee8b"}]