"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.formatTimestamp = formatTimestamp;
exports.formatExecutionTime = formatExecutionTime;
exports.truncateText = truncateText;
exports.capitalize = capitalize;
exports.camelToTitle = camelToTitle;
exports.generateNodeColor = generateNodeColor;
/**
 * Formats a timestamp to a human-readable string
 */
function formatTimestamp(timestamp) {
    return new Date(timestamp).toLocaleString();
}
/**
 * Formats execution time in milliseconds to a human-readable string
 */
function formatExecutionTime(ms) {
    if (ms < 1000) {
        return `${ms}ms`;
    }
    if (ms < 60000) {
        return `${(ms / 1000).toFixed(1)}s`;
    }
    return `${(ms / 60000).toFixed(1)}m`;
}
/**
 * Truncates text to a specified length with ellipsis
 */
function truncateText(text, maxLength) {
    if (text.length <= maxLength) {
        return text;
    }
    return text.substring(0, maxLength - 3) + '...';
}
/**
 * Capitalizes the first letter of a string
 */
function capitalize(str) {
    return str.charAt(0).toUpperCase() + str.slice(1);
}
/**
 * Converts camelCase to Title Case
 */
function camelToTitle(str) {
    return str
        .replace(/([A-Z])/g, ' $1')
        .replace(/^./, (str) => str.toUpperCase())
        .trim();
}
/**
 * Generates a random color for nodes
 */
function generateNodeColor() {
    const colors = [
        '#3B82F6', '#EF4444', '#10B981', '#F59E0B',
        '#8B5CF6', '#EC4899', '#06B6D4', '#84CC16'
    ];
    return colors[Math.floor(Math.random() * colors.length)];
}
//# sourceMappingURL=formatting.js.map