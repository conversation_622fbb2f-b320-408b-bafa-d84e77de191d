(()=>{var e={};e.id=978,e.ids=[978],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1143:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>i.a,__next_app__:()=>h,originalPathname:()=>d,pages:()=>u,routeModule:()=>f,tree:()=>c}),n(2828),n(2367),n(5999);var r=n(7188),o=n(8659),a=n(2067),i=n.n(a),s=n(9786),l={};for(let e in s)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>s[e]);n.d(t,l);let c=["",{children:["flows",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.bind(n,2828)),"C:\\Users\\<USER>\\Documents\\augment-projects\\agent_framework\\packages\\ui\\src\\app\\flows\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(n.bind(n,2367)),"C:\\Users\\<USER>\\Documents\\augment-projects\\agent_framework\\packages\\ui\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,5999,23)),"next/dist/client/components/not-found-error"]}],u=["C:\\Users\\<USER>\\Documents\\augment-projects\\agent_framework\\packages\\ui\\src\\app\\flows\\[id]\\page.tsx"],d="/flows/[id]/page",h={require:n,loadChunk:()=>Promise.resolve()},f=new r.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/flows/[id]/page",pathname:"/flows/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},294:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,3143,23)),Promise.resolve().then(n.t.bind(n,3627,23)),Promise.resolve().then(n.t.bind(n,8958,23)),Promise.resolve().then(n.t.bind(n,9501,23)),Promise.resolve().then(n.t.bind(n,543,23)),Promise.resolve().then(n.t.bind(n,8789,23))},1459:(e,t,n)=>{Promise.resolve().then(n.bind(n,6600)),Promise.resolve().then(n.bind(n,5682))},2528:(e,t,n)=>{Promise.resolve().then(n.bind(n,2247))},7756:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});let r=(0,n(3509).Z)("Bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]])},4440:(e,t,n)=>{"use strict";var r=n(6364),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=r.useState,i=r.useEffect,s=r.useLayoutEffect,l=r.useDebugValue;function c(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!o(e,n)}catch(e){return!0}}var u="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=a({inst:{value:n,getSnapshot:t}}),o=r[0].inst,u=r[1];return s(function(){o.value=n,o.getSnapshot=t,c(o)&&u({inst:o})},[e,n,t]),i(function(){return c(o)&&u({inst:o}),e(function(){c(o)&&u({inst:o})})},[e]),l(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:u},124:(e,t,n)=>{"use strict";var r=n(6364),o=n(6897),a="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=o.useSyncExternalStore,s=r.useRef,l=r.useEffect,c=r.useMemo,u=r.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,n,r,o){var d=s(null);if(null===d.current){var h={hasValue:!1,value:null};d.current=h}else h=d.current;var f=i(e,(d=c(function(){function e(e){if(!l){if(l=!0,i=e,e=r(e),void 0!==o&&h.hasValue){var t=h.value;if(o(t,e))return s=t}return s=e}if(t=s,a(i,e))return t;var n=r(e);return void 0!==o&&o(t,n)?(i=e,t):(i=e,s=n)}var i,s,l=!1,c=void 0===n?null:n;return[function(){return e(t())},null===c?void 0:function(){return e(c())}]},[t,n,r,o]))[0],d[1]);return l(function(){h.hasValue=!0,h.value=f},[f]),u(f),f}},6897:(e,t,n)=>{"use strict";e.exports=n(4440)},3368:(e,t,n)=>{"use strict";e.exports=n(124)},2247:(e,t,n)=>{"use strict";let r,o;n.r(t),n.d(t,{default:()=>sw});var a,i,s,l,c,u,d,h,f=n(7491),m=n(6364),p=n(1082);function g(e){if("string"==typeof e||"number"==typeof e)return""+e;let t="";if(Array.isArray(e))for(let n=0,r;n<e.length;n++)""!==(r=g(e[n]))&&(t+=(t&&" ")+r);else for(let n in e)e[n]&&(t+=(t&&" ")+n);return t}var y=n(3368);let x=e=>{let t;let n=new Set,r=(e,r)=>{let o="function"==typeof e?e(t):e;if(!Object.is(o,t)){let e=t;t=(null!=r?r:"object"!=typeof o||null===o)?o:Object.assign({},t,o),n.forEach(n=>n(t,e))}},o=()=>t,a={setState:r,getState:o,getInitialState:()=>i,subscribe:e=>(n.add(e),()=>n.delete(e)),destroy:()=>{console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}},i=t=e(r,o,a);return a},v=e=>e?x(e):x,{useDebugValue:b}=m,{useSyncExternalStoreWithSelector:w}=y,N=e=>e;function S(e,t=N,n){let r=w(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return b(r),r}let _=(e,t)=>{let n=v(e),r=(e,r=t)=>S(n,e,r);return Object.assign(r,n),r},E=(e,t)=>e?_(e,t):_;function j(e,t){if(Object.is(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;if(e instanceof Map&&t instanceof Map){if(e.size!==t.size)return!1;for(let[n,r]of e)if(!Object.is(r,t.get(n)))return!1;return!0}if(e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;for(let n of e)if(!t.has(n))return!1;return!0}let n=Object.keys(e);if(n.length!==Object.keys(t).length)return!1;for(let r of n)if(!Object.prototype.hasOwnProperty.call(t,r)||!Object.is(e[r],t[r]))return!1;return!0}var k={value:()=>{}};function C(){for(var e,t=0,n=arguments.length,r={};t<n;++t){if(!(e=arguments[t]+"")||e in r||/[\s.]/.test(e))throw Error("illegal type: "+e);r[e]=[]}return new M(r)}function M(e){this._=e}function A(e,t,n){for(var r=0,o=e.length;r<o;++r)if(e[r].name===t){e[r]=k,e=e.slice(0,r).concat(e.slice(r+1));break}return null!=n&&e.push({name:t,value:n}),e}function z(){}function I(e){return null==e?z:function(){return this.querySelector(e)}}function P(){return[]}function T(e){return null==e?P:function(){return this.querySelectorAll(e)}}function D(e){return function(){return this.matches(e)}}function R(e){return function(t){return t.matches(e)}}M.prototype=C.prototype={constructor:M,on:function(e,t){var n,r=this._,o=(e+"").trim().split(/^|\s+/).map(function(e){var t="",n=e.indexOf(".");if(n>=0&&(t=e.slice(n+1),e=e.slice(0,n)),e&&!r.hasOwnProperty(e))throw Error("unknown type: "+e);return{type:e,name:t}}),a=-1,i=o.length;if(arguments.length<2){for(;++a<i;)if((n=(e=o[a]).type)&&(n=function(e,t){for(var n,r=0,o=e.length;r<o;++r)if((n=e[r]).name===t)return n.value}(r[n],e.name)))return n;return}if(null!=t&&"function"!=typeof t)throw Error("invalid callback: "+t);for(;++a<i;)if(n=(e=o[a]).type)r[n]=A(r[n],e.name,t);else if(null==t)for(n in r)r[n]=A(r[n],e.name,null);return this},copy:function(){var e={},t=this._;for(var n in t)e[n]=t[n].slice();return new M(e)},call:function(e,t){if((n=arguments.length-2)>0)for(var n,r,o=Array(n),a=0;a<n;++a)o[a]=arguments[a+2];if(!this._.hasOwnProperty(e))throw Error("unknown type: "+e);for(r=this._[e],a=0,n=r.length;a<n;++a)r[a].value.apply(t,o)},apply:function(e,t,n){if(!this._.hasOwnProperty(e))throw Error("unknown type: "+e);for(var r=this._[e],o=0,a=r.length;o<a;++o)r[o].value.apply(t,n)}};var $=Array.prototype.find;function O(){return this.firstElementChild}var B=Array.prototype.filter;function L(){return Array.from(this.children)}function H(e){return Array(e.length)}function F(e,t){this.ownerDocument=e.ownerDocument,this.namespaceURI=e.namespaceURI,this._next=null,this._parent=e,this.__data__=t}function V(e,t,n,r,o,a){for(var i,s=0,l=t.length,c=a.length;s<c;++s)(i=t[s])?(i.__data__=a[s],r[s]=i):n[s]=new F(e,a[s]);for(;s<l;++s)(i=t[s])&&(o[s]=i)}function Z(e,t,n,r,o,a,i){var s,l,c,u=new Map,d=t.length,h=a.length,f=Array(d);for(s=0;s<d;++s)(l=t[s])&&(f[s]=c=i.call(l,l.__data__,s,t)+"",u.has(c)?o[s]=l:u.set(c,l));for(s=0;s<h;++s)c=i.call(e,a[s],s,a)+"",(l=u.get(c))?(r[s]=l,l.__data__=a[s],u.delete(c)):n[s]=new F(e,a[s]);for(s=0;s<d;++s)(l=t[s])&&u.get(f[s])===l&&(o[s]=l)}function Y(e){return e.__data__}function q(e,t){return e<t?-1:e>t?1:e>=t?0:NaN}F.prototype={constructor:F,appendChild:function(e){return this._parent.insertBefore(e,this._next)},insertBefore:function(e,t){return this._parent.insertBefore(e,t)},querySelector:function(e){return this._parent.querySelector(e)},querySelectorAll:function(e){return this._parent.querySelectorAll(e)}};var X="http://www.w3.org/1999/xhtml";let K={svg:"http://www.w3.org/2000/svg",xhtml:X,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function W(e){var t=e+="",n=t.indexOf(":");return n>=0&&"xmlns"!==(t=e.slice(0,n))&&(e=e.slice(n+1)),K.hasOwnProperty(t)?{space:K[t],local:e}:e}function U(e){return e.ownerDocument&&e.ownerDocument.defaultView||e.document&&e||e.defaultView}function G(e,t){return e.style.getPropertyValue(t)||U(e).getComputedStyle(e,null).getPropertyValue(t)}function Q(e){return e.trim().split(/^|\s+/)}function J(e){return e.classList||new ee(e)}function ee(e){this._node=e,this._names=Q(e.getAttribute("class")||"")}function et(e,t){for(var n=J(e),r=-1,o=t.length;++r<o;)n.add(t[r])}function en(e,t){for(var n=J(e),r=-1,o=t.length;++r<o;)n.remove(t[r])}function er(){this.textContent=""}function eo(){this.innerHTML=""}function ea(){this.nextSibling&&this.parentNode.appendChild(this)}function ei(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function es(e){var t=W(e);return(t.local?function(e){return function(){return this.ownerDocument.createElementNS(e.space,e.local)}}:function(e){return function(){var t=this.ownerDocument,n=this.namespaceURI;return n===X&&t.documentElement.namespaceURI===X?t.createElement(e):t.createElementNS(n,e)}})(t)}function el(){return null}function ec(){var e=this.parentNode;e&&e.removeChild(this)}function eu(){var e=this.cloneNode(!1),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function ed(){var e=this.cloneNode(!0),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function eh(e){return function(){var t=this.__on;if(t){for(var n,r=0,o=-1,a=t.length;r<a;++r)(n=t[r],e.type&&n.type!==e.type||n.name!==e.name)?t[++o]=n:this.removeEventListener(n.type,n.listener,n.options);++o?t.length=o:delete this.__on}}}function ef(e,t,n){return function(){var r,o=this.__on,a=function(e){t.call(this,e,this.__data__)};if(o){for(var i=0,s=o.length;i<s;++i)if((r=o[i]).type===e.type&&r.name===e.name){this.removeEventListener(r.type,r.listener,r.options),this.addEventListener(r.type,r.listener=a,r.options=n),r.value=t;return}}this.addEventListener(e.type,a,n),r={type:e.type,name:e.name,value:t,listener:a,options:n},o?o.push(r):this.__on=[r]}}function em(e,t,n){var r=U(e),o=r.CustomEvent;"function"==typeof o?o=new o(t,n):(o=r.document.createEvent("Event"),n?(o.initEvent(t,n.bubbles,n.cancelable),o.detail=n.detail):o.initEvent(t,!1,!1)),e.dispatchEvent(o)}ee.prototype={add:function(e){0>this._names.indexOf(e)&&(this._names.push(e),this._node.setAttribute("class",this._names.join(" ")))},remove:function(e){var t=this._names.indexOf(e);t>=0&&(this._names.splice(t,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(e){return this._names.indexOf(e)>=0}};var ep=[null];function eg(e,t){this._groups=e,this._parents=t}function ey(){return new eg([[document.documentElement]],ep)}function ex(e){return"string"==typeof e?new eg([[document.querySelector(e)]],[document.documentElement]):new eg([[e]],ep)}eg.prototype=ey.prototype={constructor:eg,select:function(e){"function"!=typeof e&&(e=I(e));for(var t=this._groups,n=t.length,r=Array(n),o=0;o<n;++o)for(var a,i,s=t[o],l=s.length,c=r[o]=Array(l),u=0;u<l;++u)(a=s[u])&&(i=e.call(a,a.__data__,u,s))&&("__data__"in a&&(i.__data__=a.__data__),c[u]=i);return new eg(r,this._parents)},selectAll:function(e){if("function"==typeof e){var t;t=e,e=function(){var e;return e=t.apply(this,arguments),null==e?[]:Array.isArray(e)?e:Array.from(e)}}else e=T(e);for(var n=this._groups,r=n.length,o=[],a=[],i=0;i<r;++i)for(var s,l=n[i],c=l.length,u=0;u<c;++u)(s=l[u])&&(o.push(e.call(s,s.__data__,u,l)),a.push(s));return new eg(o,a)},selectChild:function(e){var t;return this.select(null==e?O:(t="function"==typeof e?e:R(e),function(){return $.call(this.children,t)}))},selectChildren:function(e){var t;return this.selectAll(null==e?L:(t="function"==typeof e?e:R(e),function(){return B.call(this.children,t)}))},filter:function(e){"function"!=typeof e&&(e=D(e));for(var t=this._groups,n=t.length,r=Array(n),o=0;o<n;++o)for(var a,i=t[o],s=i.length,l=r[o]=[],c=0;c<s;++c)(a=i[c])&&e.call(a,a.__data__,c,i)&&l.push(a);return new eg(r,this._parents)},data:function(e,t){if(!arguments.length)return Array.from(this,Y);var n=t?Z:V,r=this._parents,o=this._groups;"function"!=typeof e&&(x=e,e=function(){return x});for(var a=o.length,i=Array(a),s=Array(a),l=Array(a),c=0;c<a;++c){var u=r[c],d=o[c],h=d.length,f="object"==typeof(y=e.call(u,u&&u.__data__,c,r))&&"length"in y?y:Array.from(y),m=f.length,p=s[c]=Array(m),g=i[c]=Array(m);n(u,d,p,g,l[c]=Array(h),f,t);for(var y,x,v,b,w=0,N=0;w<m;++w)if(v=p[w]){for(w>=N&&(N=w+1);!(b=g[N])&&++N<m;);v._next=b||null}}return(i=new eg(i,r))._enter=s,i._exit=l,i},enter:function(){return new eg(this._enter||this._groups.map(H),this._parents)},exit:function(){return new eg(this._exit||this._groups.map(H),this._parents)},join:function(e,t,n){var r=this.enter(),o=this,a=this.exit();return"function"==typeof e?(r=e(r))&&(r=r.selection()):r=r.append(e+""),null!=t&&(o=t(o))&&(o=o.selection()),null==n?a.remove():n(a),r&&o?r.merge(o).order():o},merge:function(e){for(var t=e.selection?e.selection():e,n=this._groups,r=t._groups,o=n.length,a=r.length,i=Math.min(o,a),s=Array(o),l=0;l<i;++l)for(var c,u=n[l],d=r[l],h=u.length,f=s[l]=Array(h),m=0;m<h;++m)(c=u[m]||d[m])&&(f[m]=c);for(;l<o;++l)s[l]=n[l];return new eg(s,this._parents)},selection:function(){return this},order:function(){for(var e=this._groups,t=-1,n=e.length;++t<n;)for(var r,o=e[t],a=o.length-1,i=o[a];--a>=0;)(r=o[a])&&(i&&4^r.compareDocumentPosition(i)&&i.parentNode.insertBefore(r,i),i=r);return this},sort:function(e){function t(t,n){return t&&n?e(t.__data__,n.__data__):!t-!n}e||(e=q);for(var n=this._groups,r=n.length,o=Array(r),a=0;a<r;++a){for(var i,s=n[a],l=s.length,c=o[a]=Array(l),u=0;u<l;++u)(i=s[u])&&(c[u]=i);c.sort(t)}return new eg(o,this._parents).order()},call:function(){var e=arguments[0];return arguments[0]=this,e.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var r=e[t],o=0,a=r.length;o<a;++o){var i=r[o];if(i)return i}return null},size:function(){let e=0;for(let t of this)++e;return e},empty:function(){return!this.node()},each:function(e){for(var t=this._groups,n=0,r=t.length;n<r;++n)for(var o,a=t[n],i=0,s=a.length;i<s;++i)(o=a[i])&&e.call(o,o.__data__,i,a);return this},attr:function(e,t){var n=W(e);if(arguments.length<2){var r=this.node();return n.local?r.getAttributeNS(n.space,n.local):r.getAttribute(n)}return this.each((null==t?n.local?function(e){return function(){this.removeAttributeNS(e.space,e.local)}}:function(e){return function(){this.removeAttribute(e)}}:"function"==typeof t?n.local?function(e,t){return function(){var n=t.apply(this,arguments);null==n?this.removeAttributeNS(e.space,e.local):this.setAttributeNS(e.space,e.local,n)}}:function(e,t){return function(){var n=t.apply(this,arguments);null==n?this.removeAttribute(e):this.setAttribute(e,n)}}:n.local?function(e,t){return function(){this.setAttributeNS(e.space,e.local,t)}}:function(e,t){return function(){this.setAttribute(e,t)}})(n,t))},style:function(e,t,n){return arguments.length>1?this.each((null==t?function(e){return function(){this.style.removeProperty(e)}}:"function"==typeof t?function(e,t,n){return function(){var r=t.apply(this,arguments);null==r?this.style.removeProperty(e):this.style.setProperty(e,r,n)}}:function(e,t,n){return function(){this.style.setProperty(e,t,n)}})(e,t,null==n?"":n)):G(this.node(),e)},property:function(e,t){return arguments.length>1?this.each((null==t?function(e){return function(){delete this[e]}}:"function"==typeof t?function(e,t){return function(){var n=t.apply(this,arguments);null==n?delete this[e]:this[e]=n}}:function(e,t){return function(){this[e]=t}})(e,t)):this.node()[e]},classed:function(e,t){var n=Q(e+"");if(arguments.length<2){for(var r=J(this.node()),o=-1,a=n.length;++o<a;)if(!r.contains(n[o]))return!1;return!0}return this.each(("function"==typeof t?function(e,t){return function(){(t.apply(this,arguments)?et:en)(this,e)}}:t?function(e){return function(){et(this,e)}}:function(e){return function(){en(this,e)}})(n,t))},text:function(e){return arguments.length?this.each(null==e?er:("function"==typeof e?function(e){return function(){var t=e.apply(this,arguments);this.textContent=null==t?"":t}}:function(e){return function(){this.textContent=e}})(e)):this.node().textContent},html:function(e){return arguments.length?this.each(null==e?eo:("function"==typeof e?function(e){return function(){var t=e.apply(this,arguments);this.innerHTML=null==t?"":t}}:function(e){return function(){this.innerHTML=e}})(e)):this.node().innerHTML},raise:function(){return this.each(ea)},lower:function(){return this.each(ei)},append:function(e){var t="function"==typeof e?e:es(e);return this.select(function(){return this.appendChild(t.apply(this,arguments))})},insert:function(e,t){var n="function"==typeof e?e:es(e),r=null==t?el:"function"==typeof t?t:I(t);return this.select(function(){return this.insertBefore(n.apply(this,arguments),r.apply(this,arguments)||null)})},remove:function(){return this.each(ec)},clone:function(e){return this.select(e?ed:eu)},datum:function(e){return arguments.length?this.property("__data__",e):this.node().__data__},on:function(e,t,n){var r,o,a=(e+"").trim().split(/^|\s+/).map(function(e){var t="",n=e.indexOf(".");return n>=0&&(t=e.slice(n+1),e=e.slice(0,n)),{type:e,name:t}}),i=a.length;if(arguments.length<2){var s=this.node().__on;if(s){for(var l,c=0,u=s.length;c<u;++c)for(r=0,l=s[c];r<i;++r)if((o=a[r]).type===l.type&&o.name===l.name)return l.value}return}for(r=0,s=t?ef:eh;r<i;++r)this.each(s(a[r],t,n));return this},dispatch:function(e,t){return this.each(("function"==typeof t?function(e,t){return function(){return em(this,e,t.apply(this,arguments))}}:function(e,t){return function(){return em(this,e,t)}})(e,t))},[Symbol.iterator]:function*(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var r,o=e[t],a=0,i=o.length;a<i;++a)(r=o[a])&&(yield r)}};let ev={passive:!1},eb={capture:!0,passive:!1};function ew(e){e.stopImmediatePropagation()}function eN(e){e.preventDefault(),e.stopImmediatePropagation()}function eS(e){var t=e.document.documentElement,n=ex(e).on("dragstart.drag",eN,eb);"onselectstart"in t?n.on("selectstart.drag",eN,eb):(t.__noselect=t.style.MozUserSelect,t.style.MozUserSelect="none")}function e_(e,t){var n=e.document.documentElement,r=ex(e).on("dragstart.drag",null);t&&(r.on("click.drag",eN,eb),setTimeout(function(){r.on("click.drag",null)},0)),"onselectstart"in n?r.on("selectstart.drag",null):(n.style.MozUserSelect=n.__noselect,delete n.__noselect)}function eE(e){return((e=Math.exp(e))+1/e)/2}let ej=function e(t,n,r){function o(e,o){var a,i,s=e[0],l=e[1],c=e[2],u=o[0],d=o[1],h=o[2],f=u-s,m=d-l,p=f*f+m*m;if(p<1e-12)i=Math.log(h/c)/t,a=function(e){return[s+e*f,l+e*m,c*Math.exp(t*e*i)]};else{var g=Math.sqrt(p),y=(h*h-c*c+r*p)/(2*c*n*g),x=(h*h-c*c-r*p)/(2*h*n*g),v=Math.log(Math.sqrt(y*y+1)-y);i=(Math.log(Math.sqrt(x*x+1)-x)-v)/t,a=function(e){var r,o,a=e*i,u=eE(v),d=c/(n*g)*(((r=Math.exp(2*(r=t*a+v)))-1)/(r+1)*u-((o=Math.exp(o=v))-1/o)/2);return[s+d*f,l+d*m,c*u/eE(t*a+v)]}}return a.duration=1e3*i*t/Math.SQRT2,a}return o.rho=function(t){var n=Math.max(.001,+t),r=n*n;return e(n,r,r*r)},o}(Math.SQRT2,2,4);function ek(e,t){if(e=function(e){let t;for(;t=e.sourceEvent;)e=t;return e}(e),void 0===t&&(t=e.currentTarget),t){var n=t.ownerSVGElement||t;if(n.createSVGPoint){var r=n.createSVGPoint();return r.x=e.clientX,r.y=e.clientY,[(r=r.matrixTransform(t.getScreenCTM().inverse())).x,r.y]}if(t.getBoundingClientRect){var o=t.getBoundingClientRect();return[e.clientX-o.left-t.clientLeft,e.clientY-o.top-t.clientTop]}}return[e.pageX,e.pageY]}var eC,eM,eA=0,ez=0,eI=0,eP=0,eT=0,eD=0,eR="object"==typeof performance&&performance.now?performance:Date,e$="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(e){setTimeout(e,17)};function eO(){return eT||(e$(eB),eT=eR.now()+eD)}function eB(){eT=0}function eL(){this._call=this._time=this._next=null}function eH(e,t,n){var r=new eL;return r.restart(e,t,n),r}function eF(){eT=(eP=eR.now())+eD,eA=ez=0;try{!function(){eO(),++eA;for(var e,t=eC;t;)(e=eT-t._time)>=0&&t._call.call(void 0,e),t=t._next;--eA}()}finally{eA=0,function(){for(var e,t,n=eC,r=1/0;n;)n._call?(r>n._time&&(r=n._time),e=n,n=n._next):(t=n._next,n._next=null,n=e?e._next=t:eC=t);eM=e,eZ(r)}(),eT=0}}function eV(){var e=eR.now(),t=e-eP;t>1e3&&(eD-=t,eP=e)}function eZ(e){!eA&&(ez&&(ez=clearTimeout(ez)),e-eT>24?(e<1/0&&(ez=setTimeout(eF,e-eR.now()-eD)),eI&&(eI=clearInterval(eI))):(eI||(eP=eR.now(),eI=setInterval(eV,1e3)),eA=1,e$(eF)))}function eY(e,t,n){var r=new eL;return t=null==t?0:+t,r.restart(n=>{r.stop(),e(n+t)},t,n),r}eL.prototype=eH.prototype={constructor:eL,restart:function(e,t,n){if("function"!=typeof e)throw TypeError("callback is not a function");n=(null==n?eO():+n)+(null==t?0:+t),this._next||eM===this||(eM?eM._next=this:eC=this,eM=this),this._call=e,this._time=n,eZ()},stop:function(){this._call&&(this._call=null,this._time=1/0,eZ())}};var eq=C("start","end","cancel","interrupt"),eX=[];function eK(e,t,n,r,o,a){var i=e.__transition;if(i){if(n in i)return}else e.__transition={};(function(e,t,n){var r,o=e.__transition;function a(l){var c,u,d,h;if(1!==n.state)return s();for(c in o)if((h=o[c]).name===n.name){if(3===h.state)return eY(a);4===h.state?(h.state=6,h.timer.stop(),h.on.call("interrupt",e,e.__data__,h.index,h.group),delete o[c]):+c<t&&(h.state=6,h.timer.stop(),h.on.call("cancel",e,e.__data__,h.index,h.group),delete o[c])}if(eY(function(){3===n.state&&(n.state=4,n.timer.restart(i,n.delay,n.time),i(l))}),n.state=2,n.on.call("start",e,e.__data__,n.index,n.group),2===n.state){for(c=0,n.state=3,r=Array(d=n.tween.length),u=-1;c<d;++c)(h=n.tween[c].value.call(e,e.__data__,n.index,n.group))&&(r[++u]=h);r.length=u+1}}function i(t){for(var o=t<n.duration?n.ease.call(null,t/n.duration):(n.timer.restart(s),n.state=5,1),a=-1,i=r.length;++a<i;)r[a].call(e,o);5===n.state&&(n.on.call("end",e,e.__data__,n.index,n.group),s())}function s(){for(var r in n.state=6,n.timer.stop(),delete o[t],o)return;delete e.__transition}o[t]=n,n.timer=eH(function(e){n.state=1,n.timer.restart(a,n.delay,n.time),n.delay<=e&&a(e-n.delay)},0,n.time)})(e,n,{name:t,index:r,group:o,on:eq,tween:eX,time:a.time,delay:a.delay,duration:a.duration,ease:a.ease,timer:null,state:0})}function eW(e,t){var n=eG(e,t);if(n.state>0)throw Error("too late; already scheduled");return n}function eU(e,t){var n=eG(e,t);if(n.state>3)throw Error("too late; already running");return n}function eG(e,t){var n=e.__transition;if(!n||!(n=n[t]))throw Error("transition not found");return n}function eQ(e,t){var n,r,o,a=e.__transition,i=!0;if(a){for(o in t=null==t?null:t+"",a){if((n=a[o]).name!==t){i=!1;continue}r=n.state>2&&n.state<5,n.state=6,n.timer.stop(),n.on.call(r?"interrupt":"cancel",e,e.__data__,n.index,n.group),delete a[o]}i&&delete e.__transition}}function eJ(e,t){return e=+e,t=+t,function(n){return e*(1-n)+t*n}}var e0=180/Math.PI,e1={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function e2(e,t,n,r,o,a){var i,s,l;return(i=Math.sqrt(e*e+t*t))&&(e/=i,t/=i),(l=e*n+t*r)&&(n-=e*l,r-=t*l),(s=Math.sqrt(n*n+r*r))&&(n/=s,r/=s,l/=s),e*r<t*n&&(e=-e,t=-t,l=-l,i=-i),{translateX:o,translateY:a,rotate:Math.atan2(t,e)*e0,skewX:Math.atan(l)*e0,scaleX:i,scaleY:s}}function e3(e,t,n,r){function o(e){return e.length?e.pop()+" ":""}return function(a,i){var s,l,c,u,d=[],h=[];return a=e(a),i=e(i),function(e,r,o,a,i,s){if(e!==o||r!==a){var l=i.push("translate(",null,t,null,n);s.push({i:l-4,x:eJ(e,o)},{i:l-2,x:eJ(r,a)})}else(o||a)&&i.push("translate("+o+t+a+n)}(a.translateX,a.translateY,i.translateX,i.translateY,d,h),(s=a.rotate)!==(l=i.rotate)?(s-l>180?l+=360:l-s>180&&(s+=360),h.push({i:d.push(o(d)+"rotate(",null,r)-2,x:eJ(s,l)})):l&&d.push(o(d)+"rotate("+l+r),(c=a.skewX)!==(u=i.skewX)?h.push({i:d.push(o(d)+"skewX(",null,r)-2,x:eJ(c,u)}):u&&d.push(o(d)+"skewX("+u+r),function(e,t,n,r,a,i){if(e!==n||t!==r){var s=a.push(o(a)+"scale(",null,",",null,")");i.push({i:s-4,x:eJ(e,n)},{i:s-2,x:eJ(t,r)})}else(1!==n||1!==r)&&a.push(o(a)+"scale("+n+","+r+")")}(a.scaleX,a.scaleY,i.scaleX,i.scaleY,d,h),a=i=null,function(e){for(var t,n=-1,r=h.length;++n<r;)d[(t=h[n]).i]=t.x(e);return d.join("")}}}var e4=e3(function(e){let t=new("function"==typeof DOMMatrix?DOMMatrix:WebKitCSSMatrix)(e+"");return t.isIdentity?e1:e2(t.a,t.b,t.c,t.d,t.e,t.f)},"px, ","px)","deg)"),e5=e3(function(e){return null==e?e1:(a||(a=document.createElementNS("http://www.w3.org/2000/svg","g")),a.setAttribute("transform",e),e=a.transform.baseVal.consolidate())?e2((e=e.matrix).a,e.b,e.c,e.d,e.e,e.f):e1},", ",")",")");function e6(e,t,n){var r=e._id;return e.each(function(){var e=eU(this,r);(e.value||(e.value={}))[t]=n.apply(this,arguments)}),function(e){return eG(e,r).value[t]}}function e7(e,t,n){e.prototype=t.prototype=n,n.constructor=e}function e8(e,t){var n=Object.create(e.prototype);for(var r in t)n[r]=t[r];return n}function e9(){}var te="\\s*([+-]?\\d+)\\s*",tt="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",tn="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",tr=/^#([0-9a-f]{3,8})$/,to=RegExp(`^rgb\\(${te},${te},${te}\\)$`),ta=RegExp(`^rgb\\(${tn},${tn},${tn}\\)$`),ti=RegExp(`^rgba\\(${te},${te},${te},${tt}\\)$`),ts=RegExp(`^rgba\\(${tn},${tn},${tn},${tt}\\)$`),tl=RegExp(`^hsl\\(${tt},${tn},${tn}\\)$`),tc=RegExp(`^hsla\\(${tt},${tn},${tn},${tt}\\)$`),tu={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function td(){return this.rgb().formatHex()}function th(){return this.rgb().formatRgb()}function tf(e){var t,n;return e=(e+"").trim().toLowerCase(),(t=tr.exec(e))?(n=t[1].length,t=parseInt(t[1],16),6===n?tm(t):3===n?new ty(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===n?tp(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===n?tp(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=to.exec(e))?new ty(t[1],t[2],t[3],1):(t=ta.exec(e))?new ty(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=ti.exec(e))?tp(t[1],t[2],t[3],t[4]):(t=ts.exec(e))?tp(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=tl.exec(e))?tS(t[1],t[2]/100,t[3]/100,1):(t=tc.exec(e))?tS(t[1],t[2]/100,t[3]/100,t[4]):tu.hasOwnProperty(e)?tm(tu[e]):"transparent"===e?new ty(NaN,NaN,NaN,0):null}function tm(e){return new ty(e>>16&255,e>>8&255,255&e,1)}function tp(e,t,n,r){return r<=0&&(e=t=n=NaN),new ty(e,t,n,r)}function tg(e,t,n,r){var o;return 1==arguments.length?((o=e)instanceof e9||(o=tf(o)),o)?new ty((o=o.rgb()).r,o.g,o.b,o.opacity):new ty:new ty(e,t,n,null==r?1:r)}function ty(e,t,n,r){this.r=+e,this.g=+t,this.b=+n,this.opacity=+r}function tx(){return`#${tN(this.r)}${tN(this.g)}${tN(this.b)}`}function tv(){let e=tb(this.opacity);return`${1===e?"rgb(":"rgba("}${tw(this.r)}, ${tw(this.g)}, ${tw(this.b)}${1===e?")":`, ${e})`}`}function tb(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function tw(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function tN(e){return((e=tw(e))<16?"0":"")+e.toString(16)}function tS(e,t,n,r){return r<=0?e=t=n=NaN:n<=0||n>=1?e=t=NaN:t<=0&&(e=NaN),new tE(e,t,n,r)}function t_(e){if(e instanceof tE)return new tE(e.h,e.s,e.l,e.opacity);if(e instanceof e9||(e=tf(e)),!e)return new tE;if(e instanceof tE)return e;var t=(e=e.rgb()).r/255,n=e.g/255,r=e.b/255,o=Math.min(t,n,r),a=Math.max(t,n,r),i=NaN,s=a-o,l=(a+o)/2;return s?(i=t===a?(n-r)/s+(n<r)*6:n===a?(r-t)/s+2:(t-n)/s+4,s/=l<.5?a+o:2-a-o,i*=60):s=l>0&&l<1?0:i,new tE(i,s,l,e.opacity)}function tE(e,t,n,r){this.h=+e,this.s=+t,this.l=+n,this.opacity=+r}function tj(e){return(e=(e||0)%360)<0?e+360:e}function tk(e){return Math.max(0,Math.min(1,e||0))}function tC(e,t,n){return(e<60?t+(n-t)*e/60:e<180?n:e<240?t+(n-t)*(240-e)/60:t)*255}function tM(e,t,n,r,o){var a=e*e,i=a*e;return((1-3*e+3*a-i)*t+(4-6*a+3*i)*n+(1+3*e+3*a-3*i)*r+i*o)/6}e7(e9,tf,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:td,formatHex:td,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return t_(this).formatHsl()},formatRgb:th,toString:th}),e7(ty,tg,e8(e9,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new ty(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new ty(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new ty(tw(this.r),tw(this.g),tw(this.b),tb(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:tx,formatHex:tx,formatHex8:function(){return`#${tN(this.r)}${tN(this.g)}${tN(this.b)}${tN((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:tv,toString:tv})),e7(tE,function(e,t,n,r){return 1==arguments.length?t_(e):new tE(e,t,n,null==r?1:r)},e8(e9,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new tE(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new tE(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,n=this.l,r=n+(n<.5?n:1-n)*t,o=2*n-r;return new ty(tC(e>=240?e-240:e+120,o,r),tC(e,o,r),tC(e<120?e+240:e-120,o,r),this.opacity)},clamp(){return new tE(tj(this.h),tk(this.s),tk(this.l),tb(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let e=tb(this.opacity);return`${1===e?"hsl(":"hsla("}${tj(this.h)}, ${100*tk(this.s)}%, ${100*tk(this.l)}%${1===e?")":`, ${e})`}`}}));let tA=e=>()=>e;function tz(e,t){var n=t-e;return n?function(t){return e+t*n}:tA(isNaN(e)?t:e)}let tI=function e(t){var n,r=1==(n=+(n=t))?tz:function(e,t){var r,o,a;return t-e?(r=e,o=t,r=Math.pow(r,a=n),o=Math.pow(o,a)-r,a=1/a,function(e){return Math.pow(r+e*o,a)}):tA(isNaN(e)?t:e)};function o(e,t){var n=r((e=tg(e)).r,(t=tg(t)).r),o=r(e.g,t.g),a=r(e.b,t.b),i=tz(e.opacity,t.opacity);return function(t){return e.r=n(t),e.g=o(t),e.b=a(t),e.opacity=i(t),e+""}}return o.gamma=e,o}(1);function tP(e){return function(t){var n,r,o=t.length,a=Array(o),i=Array(o),s=Array(o);for(n=0;n<o;++n)r=tg(t[n]),a[n]=r.r||0,i[n]=r.g||0,s[n]=r.b||0;return a=e(a),i=e(i),s=e(s),r.opacity=1,function(e){return r.r=a(e),r.g=i(e),r.b=s(e),r+""}}}tP(function(e){var t=e.length-1;return function(n){var r=n<=0?n=0:n>=1?(n=1,t-1):Math.floor(n*t),o=e[r],a=e[r+1],i=r>0?e[r-1]:2*o-a,s=r<t-1?e[r+2]:2*a-o;return tM((n-r/t)*t,i,o,a,s)}}),tP(function(e){var t=e.length;return function(n){var r=Math.floor(((n%=1)<0?++n:n)*t),o=e[(r+t-1)%t],a=e[r%t],i=e[(r+1)%t],s=e[(r+2)%t];return tM((n-r/t)*t,o,a,i,s)}});var tT=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,tD=RegExp(tT.source,"g");function tR(e,t){var n;return("number"==typeof t?eJ:t instanceof tf?tI:(n=tf(t))?(t=n,tI):function(e,t){var n,r,o,a,i,s=tT.lastIndex=tD.lastIndex=0,l=-1,c=[],u=[];for(e+="",t+="";(o=tT.exec(e))&&(a=tD.exec(t));)(i=a.index)>s&&(i=t.slice(s,i),c[l]?c[l]+=i:c[++l]=i),(o=o[0])===(a=a[0])?c[l]?c[l]+=a:c[++l]=a:(c[++l]=null,u.push({i:l,x:eJ(o,a)})),s=tD.lastIndex;return s<t.length&&(i=t.slice(s),c[l]?c[l]+=i:c[++l]=i),c.length<2?u[0]?(n=u[0].x,function(e){return n(e)+""}):(r=t,function(){return r}):(t=u.length,function(e){for(var n,r=0;r<t;++r)c[(n=u[r]).i]=n.x(e);return c.join("")})})(e,t)}var t$=ey.prototype.constructor;function tO(e){return function(){this.style.removeProperty(e)}}var tB=0;function tL(e,t,n,r){this._groups=e,this._parents=t,this._name=n,this._id=r}var tH=ey.prototype;tL.prototype=(function(e){return ey().transition(e)}).prototype={constructor:tL,select:function(e){var t=this._name,n=this._id;"function"!=typeof e&&(e=I(e));for(var r=this._groups,o=r.length,a=Array(o),i=0;i<o;++i)for(var s,l,c=r[i],u=c.length,d=a[i]=Array(u),h=0;h<u;++h)(s=c[h])&&(l=e.call(s,s.__data__,h,c))&&("__data__"in s&&(l.__data__=s.__data__),d[h]=l,eK(d[h],t,n,h,d,eG(s,n)));return new tL(a,this._parents,t,n)},selectAll:function(e){var t=this._name,n=this._id;"function"!=typeof e&&(e=T(e));for(var r=this._groups,o=r.length,a=[],i=[],s=0;s<o;++s)for(var l,c=r[s],u=c.length,d=0;d<u;++d)if(l=c[d]){for(var h,f=e.call(l,l.__data__,d,c),m=eG(l,n),p=0,g=f.length;p<g;++p)(h=f[p])&&eK(h,t,n,p,f,m);a.push(f),i.push(l)}return new tL(a,i,t,n)},selectChild:tH.selectChild,selectChildren:tH.selectChildren,filter:function(e){"function"!=typeof e&&(e=D(e));for(var t=this._groups,n=t.length,r=Array(n),o=0;o<n;++o)for(var a,i=t[o],s=i.length,l=r[o]=[],c=0;c<s;++c)(a=i[c])&&e.call(a,a.__data__,c,i)&&l.push(a);return new tL(r,this._parents,this._name,this._id)},merge:function(e){if(e._id!==this._id)throw Error();for(var t=this._groups,n=e._groups,r=t.length,o=n.length,a=Math.min(r,o),i=Array(r),s=0;s<a;++s)for(var l,c=t[s],u=n[s],d=c.length,h=i[s]=Array(d),f=0;f<d;++f)(l=c[f]||u[f])&&(h[f]=l);for(;s<r;++s)i[s]=t[s];return new tL(i,this._parents,this._name,this._id)},selection:function(){return new t$(this._groups,this._parents)},transition:function(){for(var e=this._name,t=this._id,n=++tB,r=this._groups,o=r.length,a=0;a<o;++a)for(var i,s=r[a],l=s.length,c=0;c<l;++c)if(i=s[c]){var u=eG(i,t);eK(i,e,n,c,s,{time:u.time+u.delay+u.duration,delay:0,duration:u.duration,ease:u.ease})}return new tL(r,this._parents,e,n)},call:tH.call,nodes:tH.nodes,node:tH.node,size:tH.size,empty:tH.empty,each:tH.each,on:function(e,t){var n,r,o,a=this._id;return arguments.length<2?eG(this.node(),a).on.on(e):this.each((o=(e+"").trim().split(/^|\s+/).every(function(e){var t=e.indexOf(".");return t>=0&&(e=e.slice(0,t)),!e||"start"===e})?eW:eU,function(){var i=o(this,a),s=i.on;s!==n&&(r=(n=s).copy()).on(e,t),i.on=r}))},attr:function(e,t){var n=W(e),r="transform"===n?e5:tR;return this.attrTween(e,"function"==typeof t?(n.local?function(e,t,n){var r,o,a;return function(){var i,s,l=n(this);return null==l?void this.removeAttributeNS(e.space,e.local):(i=this.getAttributeNS(e.space,e.local))===(s=l+"")?null:i===r&&s===o?a:(o=s,a=t(r=i,l))}}:function(e,t,n){var r,o,a;return function(){var i,s,l=n(this);return null==l?void this.removeAttribute(e):(i=this.getAttribute(e))===(s=l+"")?null:i===r&&s===o?a:(o=s,a=t(r=i,l))}})(n,r,e6(this,"attr."+e,t)):null==t?(n.local?function(e){return function(){this.removeAttributeNS(e.space,e.local)}}:function(e){return function(){this.removeAttribute(e)}})(n):(n.local?function(e,t,n){var r,o,a=n+"";return function(){var i=this.getAttributeNS(e.space,e.local);return i===a?null:i===r?o:o=t(r=i,n)}}:function(e,t,n){var r,o,a=n+"";return function(){var i=this.getAttribute(e);return i===a?null:i===r?o:o=t(r=i,n)}})(n,r,t))},attrTween:function(e,t){var n="attr."+e;if(arguments.length<2)return(n=this.tween(n))&&n._value;if(null==t)return this.tween(n,null);if("function"!=typeof t)throw Error();var r=W(e);return this.tween(n,(r.local?function(e,t){var n,r;function o(){var o=t.apply(this,arguments);return o!==r&&(n=(r=o)&&function(t){this.setAttributeNS(e.space,e.local,o.call(this,t))}),n}return o._value=t,o}:function(e,t){var n,r;function o(){var o=t.apply(this,arguments);return o!==r&&(n=(r=o)&&function(t){this.setAttribute(e,o.call(this,t))}),n}return o._value=t,o})(r,t))},style:function(e,t,n){var r,o,a,i,s,l,c,u,d,h,f,m,p,g,y,x,v,b,w,N,S,_="transform"==(e+="")?e4:tR;return null==t?this.styleTween(e,(r=e,function(){var e=G(this,r),t=(this.style.removeProperty(r),G(this,r));return e===t?null:e===o&&t===a?i:i=_(o=e,a=t)})).on("end.style."+e,tO(e)):"function"==typeof t?this.styleTween(e,(s=e,l=e6(this,"style."+e,t),function(){var e=G(this,s),t=l(this),n=t+"";return null==t&&(this.style.removeProperty(s),n=t=G(this,s)),e===n?null:e===c&&n===u?d:(u=n,d=_(c=e,t))})).each((h=this._id,v="end."+(x="style."+(f=e)),function(){var e=eU(this,h),t=e.on,n=null==e.value[x]?y||(y=tO(f)):void 0;(t!==m||g!==n)&&(p=(m=t).copy()).on(v,g=n),e.on=p})):this.styleTween(e,(b=e,S=t+"",function(){var e=G(this,b);return e===S?null:e===w?N:N=_(w=e,t)}),n).on("end.style."+e,null)},styleTween:function(e,t,n){var r="style."+(e+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(null==t)return this.tween(r,null);if("function"!=typeof t)throw Error();return this.tween(r,function(e,t,n){var r,o;function a(){var a=t.apply(this,arguments);return a!==o&&(r=(o=a)&&function(t){this.style.setProperty(e,a.call(this,t),n)}),r}return a._value=t,a}(e,t,null==n?"":n))},text:function(e){var t,n;return this.tween("text","function"==typeof e?(t=e6(this,"text",e),function(){var e=t(this);this.textContent=null==e?"":e}):(n=null==e?"":e+"",function(){this.textContent=n}))},textTween:function(e){var t="text";if(arguments.length<1)return(t=this.tween(t))&&t._value;if(null==e)return this.tween(t,null);if("function"!=typeof e)throw Error();return this.tween(t,function(e){var t,n;function r(){var r=e.apply(this,arguments);return r!==n&&(t=(n=r)&&function(e){this.textContent=r.call(this,e)}),t}return r._value=e,r}(e))},remove:function(){var e;return this.on("end.remove",(e=this._id,function(){var t=this.parentNode;for(var n in this.__transition)if(+n!==e)return;t&&t.removeChild(this)}))},tween:function(e,t){var n=this._id;if(e+="",arguments.length<2){for(var r,o=eG(this.node(),n).tween,a=0,i=o.length;a<i;++a)if((r=o[a]).name===e)return r.value;return null}return this.each((null==t?function(e,t){var n,r;return function(){var o=eU(this,e),a=o.tween;if(a!==n){r=n=a;for(var i=0,s=r.length;i<s;++i)if(r[i].name===t){(r=r.slice()).splice(i,1);break}}o.tween=r}}:function(e,t,n){var r,o;if("function"!=typeof n)throw Error();return function(){var a=eU(this,e),i=a.tween;if(i!==r){o=(r=i).slice();for(var s={name:t,value:n},l=0,c=o.length;l<c;++l)if(o[l].name===t){o[l]=s;break}l===c&&o.push(s)}a.tween=o}})(n,e,t))},delay:function(e){var t=this._id;return arguments.length?this.each(("function"==typeof e?function(e,t){return function(){eW(this,e).delay=+t.apply(this,arguments)}}:function(e,t){return t=+t,function(){eW(this,e).delay=t}})(t,e)):eG(this.node(),t).delay},duration:function(e){var t=this._id;return arguments.length?this.each(("function"==typeof e?function(e,t){return function(){eU(this,e).duration=+t.apply(this,arguments)}}:function(e,t){return t=+t,function(){eU(this,e).duration=t}})(t,e)):eG(this.node(),t).duration},ease:function(e){var t=this._id;return arguments.length?this.each(function(e,t){if("function"!=typeof t)throw Error();return function(){eU(this,e).ease=t}}(t,e)):eG(this.node(),t).ease},easeVarying:function(e){var t;if("function"!=typeof e)throw Error();return this.each((t=this._id,function(){var n=e.apply(this,arguments);if("function"!=typeof n)throw Error();eU(this,t).ease=n}))},end:function(){var e,t,n=this,r=n._id,o=n.size();return new Promise(function(a,i){var s={value:i},l={value:function(){0==--o&&a()}};n.each(function(){var n=eU(this,r),o=n.on;o!==e&&((t=(e=o).copy())._.cancel.push(s),t._.interrupt.push(s),t._.end.push(l)),n.on=t}),0===o&&a()})},[Symbol.iterator]:tH[Symbol.iterator]};var tF={time:null,delay:0,duration:250,ease:function(e){return((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2}};ey.prototype.interrupt=function(e){return this.each(function(){eQ(this,e)})},ey.prototype.transition=function(e){var t,n;e instanceof tL?(t=e._id,e=e._name):(t=++tB,(n=tF).time=eO(),e=null==e?null:e+"");for(var r=this._groups,o=r.length,a=0;a<o;++a)for(var i,s=r[a],l=s.length,c=0;c<l;++c)(i=s[c])&&eK(i,e,t,c,s,n||function(e,t){for(var n;!(n=e.__transition)||!(n=n[t]);)if(!(e=e.parentNode))throw Error(`transition ${t} not found`);return n}(i,t));return new tL(r,this._parents,e,t)};let tV=e=>()=>e;function tZ(e,{sourceEvent:t,target:n,transform:r,dispatch:o}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},target:{value:n,enumerable:!0,configurable:!0},transform:{value:r,enumerable:!0,configurable:!0},_:{value:o}})}function tY(e,t,n){this.k=e,this.x=t,this.y=n}tY.prototype={constructor:tY,scale:function(e){return 1===e?this:new tY(this.k*e,this.x,this.y)},translate:function(e,t){return 0===e&0===t?this:new tY(this.k,this.x+this.k*e,this.y+this.k*t)},apply:function(e){return[e[0]*this.k+this.x,e[1]*this.k+this.y]},applyX:function(e){return e*this.k+this.x},applyY:function(e){return e*this.k+this.y},invert:function(e){return[(e[0]-this.x)/this.k,(e[1]-this.y)/this.k]},invertX:function(e){return(e-this.x)/this.k},invertY:function(e){return(e-this.y)/this.k},rescaleX:function(e){return e.copy().domain(e.range().map(this.invertX,this).map(e.invert,e))},rescaleY:function(e){return e.copy().domain(e.range().map(this.invertY,this).map(e.invert,e))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var tq=new tY(1,0,0);function tX(e){e.stopImmediatePropagation()}function tK(e){e.preventDefault(),e.stopImmediatePropagation()}function tW(e){return(!e.ctrlKey||"wheel"===e.type)&&!e.button}function tU(){var e=this;return e instanceof SVGElement?(e=e.ownerSVGElement||e).hasAttribute("viewBox")?[[(e=e.viewBox.baseVal).x,e.y],[e.x+e.width,e.y+e.height]]:[[0,0],[e.width.baseVal.value,e.height.baseVal.value]]:[[0,0],[e.clientWidth,e.clientHeight]]}function tG(){return this.__zoom||tq}function tQ(e){return-e.deltaY*(1===e.deltaMode?.05:e.deltaMode?1:.002)*(e.ctrlKey?10:1)}function tJ(){return navigator.maxTouchPoints||"ontouchstart"in this}function t0(e,t,n){var r=e.invertX(t[0][0])-n[0][0],o=e.invertX(t[1][0])-n[1][0],a=e.invertY(t[0][1])-n[0][1],i=e.invertY(t[1][1])-n[1][1];return e.translate(o>r?(r+o)/2:Math.min(0,r)||Math.max(0,o),i>a?(a+i)/2:Math.min(0,a)||Math.max(0,i))}function t1(){var e,t,n,r=tW,o=tU,a=t0,i=tQ,s=tJ,l=[0,1/0],c=[[-1/0,-1/0],[1/0,1/0]],u=250,d=ej,h=C("start","zoom","end"),f=0,m=10;function p(e){e.property("__zoom",tG).on("wheel.zoom",N,{passive:!1}).on("mousedown.zoom",S).on("dblclick.zoom",_).filter(s).on("touchstart.zoom",E).on("touchmove.zoom",j).on("touchend.zoom touchcancel.zoom",k).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function g(e,t){return(t=Math.max(l[0],Math.min(l[1],t)))===e.k?e:new tY(t,e.x,e.y)}function y(e,t,n){var r=t[0]-n[0]*e.k,o=t[1]-n[1]*e.k;return r===e.x&&o===e.y?e:new tY(e.k,r,o)}function x(e){return[(+e[0][0]+ +e[1][0])/2,(+e[0][1]+ +e[1][1])/2]}function v(e,t,n,r){e.on("start.zoom",function(){b(this,arguments).event(r).start()}).on("interrupt.zoom end.zoom",function(){b(this,arguments).event(r).end()}).tween("zoom",function(){var e=arguments,a=b(this,e).event(r),i=o.apply(this,e),s=null==n?x(i):"function"==typeof n?n.apply(this,e):n,l=Math.max(i[1][0]-i[0][0],i[1][1]-i[0][1]),c=this.__zoom,u="function"==typeof t?t.apply(this,e):t,h=d(c.invert(s).concat(l/c.k),u.invert(s).concat(l/u.k));return function(e){if(1===e)e=u;else{var t=h(e),n=l/t[2];e=new tY(n,s[0]-t[0]*n,s[1]-t[1]*n)}a.zoom(null,e)}})}function b(e,t,n){return!n&&e.__zooming||new w(e,t)}function w(e,t){this.that=e,this.args=t,this.active=0,this.sourceEvent=null,this.extent=o.apply(e,t),this.taps=0}function N(e,...t){if(r.apply(this,arguments)){var n=b(this,t).event(e),o=this.__zoom,s=Math.max(l[0],Math.min(l[1],o.k*Math.pow(2,i.apply(this,arguments)))),u=ek(e);if(n.wheel)(n.mouse[0][0]!==u[0]||n.mouse[0][1]!==u[1])&&(n.mouse[1]=o.invert(n.mouse[0]=u)),clearTimeout(n.wheel);else{if(o.k===s)return;n.mouse=[u,o.invert(u)],eQ(this),n.start()}tK(e),n.wheel=setTimeout(function(){n.wheel=null,n.end()},150),n.zoom("mouse",a(y(g(o,s),n.mouse[0],n.mouse[1]),n.extent,c))}}function S(e,...t){if(!n&&r.apply(this,arguments)){var o=e.currentTarget,i=b(this,t,!0).event(e),s=ex(e.view).on("mousemove.zoom",function(e){if(tK(e),!i.moved){var t=e.clientX-u,n=e.clientY-d;i.moved=t*t+n*n>f}i.event(e).zoom("mouse",a(y(i.that.__zoom,i.mouse[0]=ek(e,o),i.mouse[1]),i.extent,c))},!0).on("mouseup.zoom",function(e){s.on("mousemove.zoom mouseup.zoom",null),e_(e.view,i.moved),tK(e),i.event(e).end()},!0),l=ek(e,o),u=e.clientX,d=e.clientY;eS(e.view),tX(e),i.mouse=[l,this.__zoom.invert(l)],eQ(this),i.start()}}function _(e,...t){if(r.apply(this,arguments)){var n=this.__zoom,i=ek(e.changedTouches?e.changedTouches[0]:e,this),s=n.invert(i),l=n.k*(e.shiftKey?.5:2),d=a(y(g(n,l),i,s),o.apply(this,t),c);tK(e),u>0?ex(this).transition().duration(u).call(v,d,i,e):ex(this).call(p.transform,d,i,e)}}function E(n,...o){if(r.apply(this,arguments)){var a,i,s,l,c=n.touches,u=c.length,d=b(this,o,n.changedTouches.length===u).event(n);for(tX(n),i=0;i<u;++i)l=[l=ek(s=c[i],this),this.__zoom.invert(l),s.identifier],d.touch0?d.touch1||d.touch0[2]===l[2]||(d.touch1=l,d.taps=0):(d.touch0=l,a=!0,d.taps=1+!!e);e&&(e=clearTimeout(e)),a&&(d.taps<2&&(t=l[0],e=setTimeout(function(){e=null},500)),eQ(this),d.start())}}function j(e,...t){if(this.__zooming){var n,r,o,i,s=b(this,t).event(e),l=e.changedTouches,u=l.length;for(tK(e),n=0;n<u;++n)o=ek(r=l[n],this),s.touch0&&s.touch0[2]===r.identifier?s.touch0[0]=o:s.touch1&&s.touch1[2]===r.identifier&&(s.touch1[0]=o);if(r=s.that.__zoom,s.touch1){var d=s.touch0[0],h=s.touch0[1],f=s.touch1[0],m=s.touch1[1],p=(p=f[0]-d[0])*p+(p=f[1]-d[1])*p,x=(x=m[0]-h[0])*x+(x=m[1]-h[1])*x;r=g(r,Math.sqrt(p/x)),o=[(d[0]+f[0])/2,(d[1]+f[1])/2],i=[(h[0]+m[0])/2,(h[1]+m[1])/2]}else{if(!s.touch0)return;o=s.touch0[0],i=s.touch0[1]}s.zoom("touch",a(y(r,o,i),s.extent,c))}}function k(e,...r){if(this.__zooming){var o,a,i=b(this,r).event(e),s=e.changedTouches,l=s.length;for(tX(e),n&&clearTimeout(n),n=setTimeout(function(){n=null},500),o=0;o<l;++o)a=s[o],i.touch0&&i.touch0[2]===a.identifier?delete i.touch0:i.touch1&&i.touch1[2]===a.identifier&&delete i.touch1;if(i.touch1&&!i.touch0&&(i.touch0=i.touch1,delete i.touch1),i.touch0)i.touch0[1]=this.__zoom.invert(i.touch0[0]);else if(i.end(),2===i.taps&&(a=ek(a,this),Math.hypot(t[0]-a[0],t[1]-a[1])<m)){var c=ex(this).on("dblclick.zoom");c&&c.apply(this,arguments)}}}return p.transform=function(e,t,n,r){var o=e.selection?e.selection():e;o.property("__zoom",tG),e!==o?v(e,t,n,r):o.interrupt().each(function(){b(this,arguments).event(r).start().zoom(null,"function"==typeof t?t.apply(this,arguments):t).end()})},p.scaleBy=function(e,t,n,r){p.scaleTo(e,function(){var e=this.__zoom.k,n="function"==typeof t?t.apply(this,arguments):t;return e*n},n,r)},p.scaleTo=function(e,t,n,r){p.transform(e,function(){var e=o.apply(this,arguments),r=this.__zoom,i=null==n?x(e):"function"==typeof n?n.apply(this,arguments):n,s=r.invert(i),l="function"==typeof t?t.apply(this,arguments):t;return a(y(g(r,l),i,s),e,c)},n,r)},p.translateBy=function(e,t,n,r){p.transform(e,function(){return a(this.__zoom.translate("function"==typeof t?t.apply(this,arguments):t,"function"==typeof n?n.apply(this,arguments):n),o.apply(this,arguments),c)},null,r)},p.translateTo=function(e,t,n,r,i){p.transform(e,function(){var e=o.apply(this,arguments),i=this.__zoom,s=null==r?x(e):"function"==typeof r?r.apply(this,arguments):r;return a(tq.translate(s[0],s[1]).scale(i.k).translate("function"==typeof t?-t.apply(this,arguments):-t,"function"==typeof n?-n.apply(this,arguments):-n),e,c)},r,i)},w.prototype={event:function(e){return e&&(this.sourceEvent=e),this},start:function(){return 1==++this.active&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(e,t){return this.mouse&&"mouse"!==e&&(this.mouse[1]=t.invert(this.mouse[0])),this.touch0&&"touch"!==e&&(this.touch0[1]=t.invert(this.touch0[0])),this.touch1&&"touch"!==e&&(this.touch1[1]=t.invert(this.touch1[0])),this.that.__zoom=t,this.emit("zoom"),this},end:function(){return 0==--this.active&&(delete this.that.__zooming,this.emit("end")),this},emit:function(e){var t=ex(this.that).datum();h.call(e,this.that,new tZ(e,{sourceEvent:this.sourceEvent,target:p,type:e,transform:this.that.__zoom,dispatch:h}),t)}},p.wheelDelta=function(e){return arguments.length?(i="function"==typeof e?e:tV(+e),p):i},p.filter=function(e){return arguments.length?(r="function"==typeof e?e:tV(!!e),p):r},p.touchable=function(e){return arguments.length?(s="function"==typeof e?e:tV(!!e),p):s},p.extent=function(e){return arguments.length?(o="function"==typeof e?e:tV([[+e[0][0],+e[0][1]],[+e[1][0],+e[1][1]]]),p):o},p.scaleExtent=function(e){return arguments.length?(l[0]=+e[0],l[1]=+e[1],p):[l[0],l[1]]},p.translateExtent=function(e){return arguments.length?(c[0][0]=+e[0][0],c[1][0]=+e[1][0],c[0][1]=+e[0][1],c[1][1]=+e[1][1],p):[[c[0][0],c[0][1]],[c[1][0],c[1][1]]]},p.constrain=function(e){return arguments.length?(a=e,p):a},p.duration=function(e){return arguments.length?(u=+e,p):u},p.interpolate=function(e){return arguments.length?(d=e,p):d},p.on=function(){var e=h.on.apply(h,arguments);return e===h?p:e},p.clickDistance=function(e){return arguments.length?(f=(e=+e)*e,p):Math.sqrt(f)},p.tapDistance=function(e){return arguments.length?(m=+e,p):m},p}tY.prototype;let t2=e=>()=>e;function t3(e,{sourceEvent:t,subject:n,target:r,identifier:o,active:a,x:i,y:s,dx:l,dy:c,dispatch:u}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},subject:{value:n,enumerable:!0,configurable:!0},target:{value:r,enumerable:!0,configurable:!0},identifier:{value:o,enumerable:!0,configurable:!0},active:{value:a,enumerable:!0,configurable:!0},x:{value:i,enumerable:!0,configurable:!0},y:{value:s,enumerable:!0,configurable:!0},dx:{value:l,enumerable:!0,configurable:!0},dy:{value:c,enumerable:!0,configurable:!0},_:{value:u}})}function t4(e){return!e.ctrlKey&&!e.button}function t5(){return this.parentNode}function t6(e,t){return null==t?{x:e.x,y:e.y}:t}function t7(){return navigator.maxTouchPoints||"ontouchstart"in this}t3.prototype.on=function(){var e=this._.on.apply(this._,arguments);return e===this._?this:e};var t8=n(1965);let t9=(0,m.createContext)(null),ne=t9.Provider,nt={error001:()=>"[React Flow]: Seems like you have not used zustand provider as an ancestor. Help: https://reactflow.dev/error#001",error003:e=>`Node type "${e}" not found. Using fallback type "default".`,error004:()=>"The React Flow parent container needs a width and a height to render the graph.",error005:()=>"Only child nodes can use a parent extent.",error006:()=>"Can't create edge. An edge needs a source and a target.",error009:e=>`Marker type "${e}" doesn't exist.`,error008:(e,t)=>`Couldn't create edge for ${e?"target":"source"} handle id: "${e?t.targetHandle:t.sourceHandle}", edge id: ${t.id}.`,error010:()=>"Handle: No node id found. Make sure to only use a Handle inside a custom Node.",error011:e=>`Edge type "${e}" not found. Using fallback type "default".`,error012:e=>`Node with id "${e}" does not exist, it may have been removed. This can happen when a node is deleted before the "onNodeClick" handler is called.`},nn=nt.error001();function nr(e,t){let n=(0,m.useContext)(t9);if(null===n)throw Error(nn);return S(n,e,t)}let no=()=>{let e=(0,m.useContext)(t9);if(null===e)throw Error(nn);return(0,m.useMemo)(()=>({getState:e.getState,setState:e.setState,subscribe:e.subscribe,destroy:e.destroy}),[e])},na=e=>e.userSelectionActive?"none":"all";function ni({position:e,children:t,className:n,style:r,...o}){let a=nr(na),i=`${e}`.split("-");return m.createElement("div",{className:g(["react-flow__panel",n,...i]),style:{...r,pointerEvents:a},...o},t)}function ns({proOptions:e,position:t="bottom-right"}){return e?.hideAttribution?null:m.createElement(ni,{position:t,className:"react-flow__attribution","data-message":"Please only hide this attribution when you are subscribed to React Flow Pro: https://reactflow.dev/pro"},m.createElement("a",{href:"https://reactflow.dev",target:"_blank",rel:"noopener noreferrer","aria-label":"React Flow attribution"},"React Flow"))}var nl=(0,m.memo)(({x:e,y:t,label:n,labelStyle:r={},labelShowBg:o=!0,labelBgStyle:a={},labelBgPadding:i=[2,4],labelBgBorderRadius:s=2,children:l,className:c,...u})=>{let d=(0,m.useRef)(null),[h,f]=(0,m.useState)({x:0,y:0,width:0,height:0}),p=g(["react-flow__edge-textwrapper",c]);return((0,m.useEffect)(()=>{if(d.current){let e=d.current.getBBox();f({x:e.x,y:e.y,width:e.width,height:e.height})}},[n]),void 0!==n&&n)?m.createElement("g",{transform:`translate(${e-h.width/2} ${t-h.height/2})`,className:p,visibility:h.width?"visible":"hidden",...u},o&&m.createElement("rect",{width:h.width+2*i[0],x:-i[0],y:-i[1],height:h.height+2*i[1],className:"react-flow__edge-textbg",style:a,rx:s,ry:s}),m.createElement("text",{className:"react-flow__edge-text",y:h.height/2,dy:"0.3em",ref:d,style:r},n),l):null});let nc=e=>({width:e.offsetWidth,height:e.offsetHeight}),nu=(e,t=0,n=1)=>Math.min(Math.max(e,t),n),nd=(e={x:0,y:0},t)=>({x:nu(e.x,t[0][0],t[1][0]),y:nu(e.y,t[0][1],t[1][1])}),nh=(e,t,n)=>e<t?nu(Math.abs(e-t),1,50)/50:e>n?-nu(Math.abs(e-n),1,50)/50:0,nf=(e,t)=>[20*nh(e.x,35,t.width-35),20*nh(e.y,35,t.height-35)],nm=e=>e.getRootNode?.()||window?.document,np=(e,t)=>({x:Math.min(e.x,t.x),y:Math.min(e.y,t.y),x2:Math.max(e.x2,t.x2),y2:Math.max(e.y2,t.y2)}),ng=({x:e,y:t,width:n,height:r})=>({x:e,y:t,x2:e+n,y2:t+r}),ny=({x:e,y:t,x2:n,y2:r})=>({x:e,y:t,width:n-e,height:r-t}),nx=e=>({...e.positionAbsolute||{x:0,y:0},width:e.width||0,height:e.height||0}),nv=(e,t)=>ny(np(ng(e),ng(t))),nb=(e,t)=>Math.ceil(Math.max(0,Math.min(e.x+e.width,t.x+t.width)-Math.max(e.x,t.x))*Math.max(0,Math.min(e.y+e.height,t.y+t.height)-Math.max(e.y,t.y))),nw=e=>nN(e.width)&&nN(e.height)&&nN(e.x)&&nN(e.y),nN=e=>!isNaN(e)&&isFinite(e),nS=Symbol.for("internals"),n_=["Enter"," ","Escape"],nE=(e,t)=>{},nj=e=>"nativeEvent"in e;function nk(e){let t=nj(e)?e.nativeEvent:e,n=t.composedPath?.()?.[0]||e.target;return["INPUT","SELECT","TEXTAREA"].includes(n?.nodeName)||n?.hasAttribute("contenteditable")||!!n?.closest(".nokey")}let nC=e=>"clientX"in e,nM=(e,t)=>{let n=nC(e),r=n?e.clientX:e.touches?.[0].clientX,o=n?e.clientY:e.touches?.[0].clientY;return{x:r-(t?.left??0),y:o-(t?.top??0)}},nA=()=>"undefined"!=typeof navigator&&navigator?.userAgent?.indexOf("Mac")>=0,nz=({id:e,path:t,labelX:n,labelY:r,label:o,labelStyle:a,labelShowBg:i,labelBgStyle:s,labelBgPadding:l,labelBgBorderRadius:c,style:u,markerEnd:d,markerStart:h,interactionWidth:f=20})=>m.createElement(m.Fragment,null,m.createElement("path",{id:e,style:u,d:t,fill:"none",className:"react-flow__edge-path",markerEnd:d,markerStart:h}),f&&m.createElement("path",{d:t,fill:"none",strokeOpacity:0,strokeWidth:f,className:"react-flow__edge-interaction"}),o&&nN(n)&&nN(r)?m.createElement(nl,{x:n,y:r,label:o,labelStyle:a,labelShowBg:i,labelBgStyle:s,labelBgPadding:l,labelBgBorderRadius:c}):null);function nI(e,t,n){return void 0===n?n:r=>{let o=t().edges.find(t=>t.id===e);o&&n(r,{...o})}}function nP({sourceX:e,sourceY:t,targetX:n,targetY:r}){let o=Math.abs(n-e)/2,a=Math.abs(r-t)/2;return[n<e?n+o:n-o,r<t?r+a:r-a,o,a]}function nT({sourceX:e,sourceY:t,targetX:n,targetY:r,sourceControlX:o,sourceControlY:a,targetControlX:i,targetControlY:s}){let l=.125*e+.375*o+.375*i+.125*n,c=.125*t+.375*a+.375*s+.125*r;return[l,c,Math.abs(l-e),Math.abs(c-t)]}function nD({pos:e,x1:t,y1:n,x2:r,y2:o}){return e===d.Left||e===d.Right?[.5*(t+r),n]:[t,.5*(n+o)]}function nR({sourceX:e,sourceY:t,sourcePosition:n=d.Bottom,targetX:r,targetY:o,targetPosition:a=d.Top}){let[i,s]=nD({pos:n,x1:e,y1:t,x2:r,y2:o}),[l,c]=nD({pos:a,x1:r,y1:o,x2:e,y2:t}),[u,h,f,m]=nT({sourceX:e,sourceY:t,targetX:r,targetY:o,sourceControlX:i,sourceControlY:s,targetControlX:l,targetControlY:c});return[`M${e},${t} C${i},${s} ${l},${c} ${r},${o}`,u,h,f,m]}nz.displayName="BaseEdge",function(e){e.Strict="strict",e.Loose="loose"}(i||(i={})),function(e){e.Free="free",e.Vertical="vertical",e.Horizontal="horizontal"}(s||(s={})),function(e){e.Partial="partial",e.Full="full"}(l||(l={})),function(e){e.Bezier="default",e.Straight="straight",e.Step="step",e.SmoothStep="smoothstep",e.SimpleBezier="simplebezier"}(c||(c={})),function(e){e.Arrow="arrow",e.ArrowClosed="arrowclosed"}(u||(u={})),function(e){e.Left="left",e.Top="top",e.Right="right",e.Bottom="bottom"}(d||(d={}));let n$=(0,m.memo)(({sourceX:e,sourceY:t,targetX:n,targetY:r,sourcePosition:o=d.Bottom,targetPosition:a=d.Top,label:i,labelStyle:s,labelShowBg:l,labelBgStyle:c,labelBgPadding:u,labelBgBorderRadius:h,style:f,markerEnd:p,markerStart:g,interactionWidth:y})=>{let[x,v,b]=nR({sourceX:e,sourceY:t,sourcePosition:o,targetX:n,targetY:r,targetPosition:a});return m.createElement(nz,{path:x,labelX:v,labelY:b,label:i,labelStyle:s,labelShowBg:l,labelBgStyle:c,labelBgPadding:u,labelBgBorderRadius:h,style:f,markerEnd:p,markerStart:g,interactionWidth:y})});n$.displayName="SimpleBezierEdge";let nO={[d.Left]:{x:-1,y:0},[d.Right]:{x:1,y:0},[d.Top]:{x:0,y:-1},[d.Bottom]:{x:0,y:1}},nB=({source:e,sourcePosition:t=d.Bottom,target:n})=>t===d.Left||t===d.Right?e.x<n.x?{x:1,y:0}:{x:-1,y:0}:e.y<n.y?{x:0,y:1}:{x:0,y:-1},nL=(e,t)=>Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2));function nH({sourceX:e,sourceY:t,sourcePosition:n=d.Bottom,targetX:r,targetY:o,targetPosition:a=d.Top,borderRadius:i=5,centerX:s,centerY:l,offset:c=20}){let[u,h,f,m,p]=function({source:e,sourcePosition:t=d.Bottom,target:n,targetPosition:r=d.Top,center:o,offset:a}){let i,s;let l=nO[t],c=nO[r],u={x:e.x+l.x*a,y:e.y+l.y*a},h={x:n.x+c.x*a,y:n.y+c.y*a},f=nB({source:u,sourcePosition:t,target:h}),m=0!==f.x?"x":"y",p=f[m],g=[],y={x:0,y:0},x={x:0,y:0},[v,b,w,N]=nP({sourceX:e.x,sourceY:e.y,targetX:n.x,targetY:n.y});if(l[m]*c[m]==-1){i=o.x??v,s=o.y??b;let e=[{x:i,y:u.y},{x:i,y:h.y}],t=[{x:u.x,y:s},{x:h.x,y:s}];g=l[m]===p?"x"===m?e:t:"x"===m?t:e}else{let o=[{x:u.x,y:h.y}],d=[{x:h.x,y:u.y}];if(g="x"===m?l.x===p?d:o:l.y===p?o:d,t===r){let t=Math.abs(e[m]-n[m]);if(t<=a){let r=Math.min(a-1,a-t);l[m]===p?y[m]=(u[m]>e[m]?-1:1)*r:x[m]=(h[m]>n[m]?-1:1)*r}}if(t!==r){let e="x"===m?"y":"x",t=l[m]===c[e],n=u[e]>h[e],r=u[e]<h[e];(1===l[m]&&(!t&&n||t&&r)||1!==l[m]&&(!t&&r||t&&n))&&(g="x"===m?o:d)}let f={x:u.x+y.x,y:u.y+y.y},v={x:h.x+x.x,y:h.y+x.y};Math.max(Math.abs(f.x-g[0].x),Math.abs(v.x-g[0].x))>=Math.max(Math.abs(f.y-g[0].y),Math.abs(v.y-g[0].y))?(i=(f.x+v.x)/2,s=g[0].y):(i=g[0].x,s=(f.y+v.y)/2)}return[[e,{x:u.x+y.x,y:u.y+y.y},...g,{x:h.x+x.x,y:h.y+x.y},n],i,s,w,N]}({source:{x:e,y:t},sourcePosition:n,target:{x:r,y:o},targetPosition:a,center:{x:s,y:l},offset:c});return[u.reduce((e,t,n)=>e+(n>0&&n<u.length-1?function(e,t,n,r){let o=Math.min(nL(e,t)/2,nL(t,n)/2,r),{x:a,y:i}=t;if(e.x===a&&a===n.x||e.y===i&&i===n.y)return`L${a} ${i}`;if(e.y===i){let t=e.x<n.x?-1:1,r=e.y<n.y?1:-1;return`L ${a+o*t},${i}Q ${a},${i} ${a},${i+o*r}`}let s=e.x<n.x?1:-1,l=e.y<n.y?-1:1;return`L ${a},${i+o*l}Q ${a},${i} ${a+o*s},${i}`}(u[n-1],t,u[n+1],i):`${0===n?"M":"L"}${t.x} ${t.y}`),""),h,f,m,p]}let nF=(0,m.memo)(({sourceX:e,sourceY:t,targetX:n,targetY:r,label:o,labelStyle:a,labelShowBg:i,labelBgStyle:s,labelBgPadding:l,labelBgBorderRadius:c,style:u,sourcePosition:h=d.Bottom,targetPosition:f=d.Top,markerEnd:p,markerStart:g,pathOptions:y,interactionWidth:x})=>{let[v,b,w]=nH({sourceX:e,sourceY:t,sourcePosition:h,targetX:n,targetY:r,targetPosition:f,borderRadius:y?.borderRadius,offset:y?.offset});return m.createElement(nz,{path:v,labelX:b,labelY:w,label:o,labelStyle:a,labelShowBg:i,labelBgStyle:s,labelBgPadding:l,labelBgBorderRadius:c,style:u,markerEnd:p,markerStart:g,interactionWidth:x})});nF.displayName="SmoothStepEdge";let nV=(0,m.memo)(e=>m.createElement(nF,{...e,pathOptions:(0,m.useMemo)(()=>({borderRadius:0,offset:e.pathOptions?.offset}),[e.pathOptions?.offset])}));nV.displayName="StepEdge";let nZ=(0,m.memo)(({sourceX:e,sourceY:t,targetX:n,targetY:r,label:o,labelStyle:a,labelShowBg:i,labelBgStyle:s,labelBgPadding:l,labelBgBorderRadius:c,style:u,markerEnd:d,markerStart:h,interactionWidth:f})=>{let[p,g,y]=function({sourceX:e,sourceY:t,targetX:n,targetY:r}){let[o,a,i,s]=nP({sourceX:e,sourceY:t,targetX:n,targetY:r});return[`M ${e},${t}L ${n},${r}`,o,a,i,s]}({sourceX:e,sourceY:t,targetX:n,targetY:r});return m.createElement(nz,{path:p,labelX:g,labelY:y,label:o,labelStyle:a,labelShowBg:i,labelBgStyle:s,labelBgPadding:l,labelBgBorderRadius:c,style:u,markerEnd:d,markerStart:h,interactionWidth:f})});function nY(e,t){return e>=0?.5*e:25*t*Math.sqrt(-e)}function nq({pos:e,x1:t,y1:n,x2:r,y2:o,c:a}){switch(e){case d.Left:return[t-nY(t-r,a),n];case d.Right:return[t+nY(r-t,a),n];case d.Top:return[t,n-nY(n-o,a)];case d.Bottom:return[t,n+nY(o-n,a)]}}function nX({sourceX:e,sourceY:t,sourcePosition:n=d.Bottom,targetX:r,targetY:o,targetPosition:a=d.Top,curvature:i=.25}){let[s,l]=nq({pos:n,x1:e,y1:t,x2:r,y2:o,c:i}),[c,u]=nq({pos:a,x1:r,y1:o,x2:e,y2:t,c:i}),[h,f,m,p]=nT({sourceX:e,sourceY:t,targetX:r,targetY:o,sourceControlX:s,sourceControlY:l,targetControlX:c,targetControlY:u});return[`M${e},${t} C${s},${l} ${c},${u} ${r},${o}`,h,f,m,p]}nZ.displayName="StraightEdge";let nK=(0,m.memo)(({sourceX:e,sourceY:t,targetX:n,targetY:r,sourcePosition:o=d.Bottom,targetPosition:a=d.Top,label:i,labelStyle:s,labelShowBg:l,labelBgStyle:c,labelBgPadding:u,labelBgBorderRadius:h,style:f,markerEnd:p,markerStart:g,pathOptions:y,interactionWidth:x})=>{let[v,b,w]=nX({sourceX:e,sourceY:t,sourcePosition:o,targetX:n,targetY:r,targetPosition:a,curvature:y?.curvature});return m.createElement(nz,{path:v,labelX:b,labelY:w,label:i,labelStyle:s,labelShowBg:l,labelBgStyle:c,labelBgPadding:u,labelBgBorderRadius:h,style:f,markerEnd:p,markerStart:g,interactionWidth:x})});nK.displayName="BezierEdge";let nW=(0,m.createContext)(null),nU=nW.Provider;nW.Consumer;let nG=()=>(0,m.useContext)(nW),nQ=e=>"id"in e&&"source"in e&&"target"in e,nJ=({source:e,sourceHandle:t,target:n,targetHandle:r})=>`reactflow__edge-${e}${t||""}-${n}${r||""}`,n0=(e,t)=>{if(void 0===e)return"";if("string"==typeof e)return e;let n=t?`${t}__`:"";return`${n}${Object.keys(e).sort().map(t=>`${t}=${e[t]}`).join("&")}`},n1=(e,t)=>t.some(t=>t.source===e.source&&t.target===e.target&&(t.sourceHandle===e.sourceHandle||!t.sourceHandle&&!e.sourceHandle)&&(t.targetHandle===e.targetHandle||!t.targetHandle&&!e.targetHandle)),n2=(e,t)=>{let n;return e.source&&e.target?n1(n=nQ(e)?{...e}:{...e,id:nJ(e)},t)?t:t.concat(n):(nE("006",nt.error006()),t)},n3=({x:e,y:t},[n,r,o],a,[i,s])=>{let l={x:(e-n)/o,y:(t-r)/o};return a?{x:i*Math.round(l.x/i),y:s*Math.round(l.y/s)}:l},n4=({x:e,y:t},[n,r,o])=>({x:e*o+n,y:t*o+r}),n5=(e,t=[0,0])=>{if(!e)return{x:0,y:0,positionAbsolute:{x:0,y:0}};let n=(e.width??0)*t[0],r=(e.height??0)*t[1],o={x:e.position.x-n,y:e.position.y-r};return{...o,positionAbsolute:e.positionAbsolute?{x:e.positionAbsolute.x-n,y:e.positionAbsolute.y-r}:o}},n6=(e,t=[0,0])=>0===e.length?{x:0,y:0,width:0,height:0}:ny(e.reduce((e,n)=>{let{x:r,y:o}=n5(n,t).positionAbsolute;return np(e,ng({x:r,y:o,width:n.width||0,height:n.height||0}))},{x:1/0,y:1/0,x2:-1/0,y2:-1/0})),n7=(e,t,[n,r,o]=[0,0,1],a=!1,i=!1,s=[0,0])=>{let l={x:(t.x-n)/o,y:(t.y-r)/o,width:t.width/o,height:t.height/o},c=[];return e.forEach(e=>{let{width:t,height:n,selectable:r=!0,hidden:o=!1}=e;if(i&&!r||o)return!1;let{positionAbsolute:u}=n5(e,s),d=nb(l,{x:u.x,y:u.y,width:t||0,height:n||0}),h=void 0===t||void 0===n||null===t||null===n,f=(t||0)*(n||0);(h||a&&d>0||d>=f||e.dragging)&&c.push(e)}),c},n8=(e,t)=>{let n=e.map(e=>e.id);return t.filter(e=>n.includes(e.source)||n.includes(e.target))},n9=(e,t,n,r,o,a=.1)=>{let i=nu(Math.min(t/(e.width*(1+a)),n/(e.height*(1+a))),r,o);return{x:t/2-(e.x+e.width/2)*i,y:n/2-(e.y+e.height/2)*i,zoom:i}},re=(e,t=0)=>e.transition().duration(t);function rt(e,t,n,r){return(t[n]||[]).reduce((t,o)=>(`${e.id}-${o.id}-${n}`!==r&&t.push({id:o.id||null,type:n,nodeId:e.id,x:(e.positionAbsolute?.x??0)+o.x+o.width/2,y:(e.positionAbsolute?.y??0)+o.y+o.height/2}),t),[])}let rn={source:null,target:null,sourceHandle:null,targetHandle:null},rr=()=>({handleDomNode:null,isValid:!1,connection:rn,endHandle:null});function ro(e,t,n,r,o,a,s){let l="target"===o,c=s.querySelector(`.react-flow__handle[data-id="${e?.nodeId}-${e?.id}-${e?.type}"]`),u={...rr(),handleDomNode:c};if(c){let e=ra(void 0,c),o=c.getAttribute("data-nodeid"),s=c.getAttribute("data-handleid"),d=c.classList.contains("connectable"),h=c.classList.contains("connectableend"),f={source:l?o:n,sourceHandle:l?s:r,target:l?n:o,targetHandle:l?r:s};u.connection=f,d&&h&&(t===i.Strict?l&&"source"===e||!l&&"target"===e:o!==n||s!==r)&&(u.endHandle={nodeId:o,handleId:s,type:e},u.isValid=a(f))}return u}function ra(e,t){return e||(t?.classList.contains("target")?"target":t?.classList.contains("source")?"source":null)}function ri(e){e?.classList.remove("valid","connecting","react-flow__handle-valid","react-flow__handle-connecting")}function rs({event:e,handleId:t,nodeId:n,onConnect:r,isTarget:o,getState:a,setState:i,isValidConnection:s,edgeUpdaterType:l,onReconnectEnd:c}){let u,d;let h=nm(e.target),{connectionMode:f,domNode:m,autoPanOnConnect:p,connectionRadius:g,onConnectStart:y,panBy:x,getNodes:v,cancelConnection:b}=a(),w=0,{x:N,y:S}=nM(e),_=ra(l,h?.elementFromPoint(N,S)),E=m?.getBoundingClientRect();if(!E||!_)return;let j=nM(e,E),k=!1,C=null,M=!1,A=null,z=function({nodes:e,nodeId:t,handleId:n,handleType:r}){return e.reduce((e,o)=>{if(o[nS]){let{handleBounds:a}=o[nS],i=[],s=[];a&&(i=rt(o,a,"source",`${t}-${n}-${r}`),s=rt(o,a,"target",`${t}-${n}-${r}`)),e.push(...i,...s)}return e},[])}({nodes:v(),nodeId:n,handleId:t,handleType:_}),I=()=>{if(!p)return;let[e,t]=nf(j,E);x({x:e,y:t}),w=requestAnimationFrame(I)};function P(e){var r,l;let c;let{transform:m}=a();j=nM(e,E);let{handle:p,validHandleResult:y}=function(e,t,n,r,o,a){let{x:i,y:s}=nM(e),l=t.elementsFromPoint(i,s).find(e=>e.classList.contains("react-flow__handle"));if(l){let e=l.getAttribute("data-nodeid");if(e){let t=ra(void 0,l),r=l.getAttribute("data-handleid"),i=a({nodeId:e,id:r,type:t});if(i){let a=o.find(n=>n.nodeId===e&&n.type===t&&n.id===r);return{handle:{id:r,type:t,nodeId:e,x:a?.x||n.x,y:a?.y||n.y},validHandleResult:i}}}}let c=[],u=1/0;if(o.forEach(e=>{let t=Math.sqrt((e.x-n.x)**2+(e.y-n.y)**2);if(t<=r){let n=a(e);t<=u&&(t<u?c=[{handle:e,validHandleResult:n}]:t===u&&c.push({handle:e,validHandleResult:n}),u=t)}}),!c.length)return{handle:null,validHandleResult:rr()};if(1===c.length)return c[0];let d=c.some(({validHandleResult:e})=>e.isValid),h=c.some(({handle:e})=>"target"===e.type);return c.find(({handle:e,validHandleResult:t})=>h?"target"===e.type:!d||t.isValid)||c[0]}(e,h,n3(j,m,!1,[1,1]),g,z,e=>ro(e,f,n,t,o?"target":"source",s,h));if(u=p,k||(I(),k=!0),A=y.handleDomNode,C=y.connection,M=y.isValid,i({connectionPosition:u&&M?n4({x:u.x,y:u.y},m):j,connectionStatus:(r=!!u,c=null,(l=M)?c="valid":r&&!l&&(c="invalid"),c),connectionEndHandle:y.endHandle}),!u&&!M&&!A)return ri(d);C.source!==C.target&&A&&(ri(d),d=A,A.classList.add("connecting","react-flow__handle-connecting"),A.classList.toggle("valid",M),A.classList.toggle("react-flow__handle-valid",M))}function T(e){(u||A)&&C&&M&&r?.(C),a().onConnectEnd?.(e),l&&c?.(e),ri(d),b(),cancelAnimationFrame(w),k=!1,M=!1,C=null,A=null,h.removeEventListener("mousemove",P),h.removeEventListener("mouseup",T),h.removeEventListener("touchmove",P),h.removeEventListener("touchend",T)}i({connectionPosition:j,connectionStatus:null,connectionNodeId:n,connectionHandleId:t,connectionHandleType:_,connectionStartHandle:{nodeId:n,handleId:t,type:_},connectionEndHandle:null}),y?.(e,{nodeId:n,handleId:t,handleType:_}),h.addEventListener("mousemove",P),h.addEventListener("mouseup",T),h.addEventListener("touchmove",P),h.addEventListener("touchend",T)}let rl=()=>!0,rc=e=>({connectionStartHandle:e.connectionStartHandle,connectOnClick:e.connectOnClick,noPanClassName:e.noPanClassName}),ru=(e,t,n)=>r=>{let{connectionStartHandle:o,connectionEndHandle:a,connectionClickStartHandle:i}=r;return{connecting:o?.nodeId===e&&o?.handleId===t&&o?.type===n||a?.nodeId===e&&a?.handleId===t&&a?.type===n,clickConnecting:i?.nodeId===e&&i?.handleId===t&&i?.type===n}},rd=(0,m.forwardRef)(({type:e="source",position:t=d.Top,isValidConnection:n,isConnectable:r=!0,isConnectableStart:o=!0,isConnectableEnd:a=!0,id:i,onConnect:s,children:l,className:c,onMouseDown:u,onTouchStart:h,...f},p)=>{let y=i||null,x="target"===e,v=no(),b=nG(),{connectOnClick:w,noPanClassName:N}=nr(rc,j),{connecting:S,clickConnecting:_}=nr(ru(b,y,e),j);b||v.getState().onError?.("010",nt.error010());let E=e=>{let{defaultEdgeOptions:t,onConnect:n,hasDefaultEdges:r}=v.getState(),o={...t,...e};if(r){let{edges:e,setEdges:t}=v.getState();t(n2(o,e))}n?.(o),s?.(o)},k=e=>{if(!b)return;let t=nC(e);o&&(t&&0===e.button||!t)&&rs({event:e,handleId:y,nodeId:b,onConnect:E,isTarget:x,getState:v.getState,setState:v.setState,isValidConnection:n||v.getState().isValidConnection||rl}),t?u?.(e):h?.(e)};return m.createElement("div",{"data-handleid":y,"data-nodeid":b,"data-handlepos":t,"data-id":`${b}-${y}-${e}`,className:g(["react-flow__handle",`react-flow__handle-${t}`,"nodrag",N,c,{source:!x,target:x,connectable:r,connectablestart:o,connectableend:a,connecting:_,connectionindicator:r&&(o&&!S||a&&S)}]),onMouseDown:k,onTouchStart:k,onClick:w?t=>{let{onClickConnectStart:r,onClickConnectEnd:a,connectionClickStartHandle:i,connectionMode:s,isValidConnection:l}=v.getState();if(!b||!i&&!o)return;if(!i){r?.(t,{nodeId:b,handleId:y,handleType:e}),v.setState({connectionClickStartHandle:{nodeId:b,type:e,handleId:y}});return}let c=nm(t.target),u=n||l||rl,{connection:d,isValid:h}=ro({nodeId:b,id:y,type:e},s,i.nodeId,i.handleId||null,i.type,u,c);h&&E(d),a?.(t),v.setState({connectionClickStartHandle:null})}:void 0,ref:p,...f},l)});rd.displayName="Handle";var rh=(0,m.memo)(rd);let rf=({data:e,isConnectable:t,targetPosition:n=d.Top,sourcePosition:r=d.Bottom})=>m.createElement(m.Fragment,null,m.createElement(rh,{type:"target",position:n,isConnectable:t}),e?.label,m.createElement(rh,{type:"source",position:r,isConnectable:t}));rf.displayName="DefaultNode";var rm=(0,m.memo)(rf);let rp=({data:e,isConnectable:t,sourcePosition:n=d.Bottom})=>m.createElement(m.Fragment,null,e?.label,m.createElement(rh,{type:"source",position:n,isConnectable:t}));rp.displayName="InputNode";var rg=(0,m.memo)(rp);let ry=({data:e,isConnectable:t,targetPosition:n=d.Top})=>m.createElement(m.Fragment,null,m.createElement(rh,{type:"target",position:n,isConnectable:t}),e?.label);ry.displayName="OutputNode";var rx=(0,m.memo)(ry);let rv=()=>null;rv.displayName="GroupNode";let rb=e=>({selectedNodes:e.getNodes().filter(e=>e.selected),selectedEdges:e.edges.filter(e=>e.selected).map(e=>({...e}))}),rw=e=>e.id;function rN(e,t){return j(e.selectedNodes.map(rw),t.selectedNodes.map(rw))&&j(e.selectedEdges.map(rw),t.selectedEdges.map(rw))}let rS=(0,m.memo)(({onSelectionChange:e})=>{let t=no(),{selectedNodes:n,selectedEdges:r}=nr(rb,rN);return(0,m.useEffect)(()=>{let o={nodes:n,edges:r};e?.(o),t.getState().onSelectionChange.forEach(e=>e(o))},[n,r,e]),null});rS.displayName="SelectionListener";let r_=e=>!!e.onSelectionChange;function rE({onSelectionChange:e}){let t=nr(r_);return e||t?m.createElement(rS,{onSelectionChange:e}):null}let rj=e=>({setNodes:e.setNodes,setEdges:e.setEdges,setDefaultNodesAndEdges:e.setDefaultNodesAndEdges,setMinZoom:e.setMinZoom,setMaxZoom:e.setMaxZoom,setTranslateExtent:e.setTranslateExtent,setNodeExtent:e.setNodeExtent,reset:e.reset});function rk(e,t){(0,m.useEffect)(()=>{void 0!==e&&t(e)},[e])}function rC(e,t,n){(0,m.useEffect)(()=>{void 0!==t&&n({[e]:t})},[t])}let rM=({nodes:e,edges:t,defaultNodes:n,defaultEdges:r,onConnect:o,onConnectStart:a,onConnectEnd:i,onClickConnectStart:s,onClickConnectEnd:l,nodesDraggable:c,nodesConnectable:u,nodesFocusable:d,edgesFocusable:h,edgesUpdatable:f,elevateNodesOnSelect:p,minZoom:g,maxZoom:y,nodeExtent:x,onNodesChange:v,onEdgesChange:b,elementsSelectable:w,connectionMode:N,snapGrid:S,snapToGrid:_,translateExtent:E,connectOnClick:k,defaultEdgeOptions:C,fitView:M,fitViewOptions:A,onNodesDelete:z,onEdgesDelete:I,onNodeDrag:P,onNodeDragStart:T,onNodeDragStop:D,onSelectionDrag:R,onSelectionDragStart:$,onSelectionDragStop:O,noPanClassName:B,nodeOrigin:L,rfId:H,autoPanOnConnect:F,autoPanOnNodeDrag:V,onError:Z,connectionRadius:Y,isValidConnection:q,nodeDragThreshold:X})=>{let{setNodes:K,setEdges:W,setDefaultNodesAndEdges:U,setMinZoom:G,setMaxZoom:Q,setTranslateExtent:J,setNodeExtent:ee,reset:et}=nr(rj,j),en=no();return(0,m.useEffect)(()=>(U(n,r?.map(e=>({...e,...C}))),()=>{et()}),[]),rC("defaultEdgeOptions",C,en.setState),rC("connectionMode",N,en.setState),rC("onConnect",o,en.setState),rC("onConnectStart",a,en.setState),rC("onConnectEnd",i,en.setState),rC("onClickConnectStart",s,en.setState),rC("onClickConnectEnd",l,en.setState),rC("nodesDraggable",c,en.setState),rC("nodesConnectable",u,en.setState),rC("nodesFocusable",d,en.setState),rC("edgesFocusable",h,en.setState),rC("edgesUpdatable",f,en.setState),rC("elementsSelectable",w,en.setState),rC("elevateNodesOnSelect",p,en.setState),rC("snapToGrid",_,en.setState),rC("snapGrid",S,en.setState),rC("onNodesChange",v,en.setState),rC("onEdgesChange",b,en.setState),rC("connectOnClick",k,en.setState),rC("fitViewOnInit",M,en.setState),rC("fitViewOnInitOptions",A,en.setState),rC("onNodesDelete",z,en.setState),rC("onEdgesDelete",I,en.setState),rC("onNodeDrag",P,en.setState),rC("onNodeDragStart",T,en.setState),rC("onNodeDragStop",D,en.setState),rC("onSelectionDrag",R,en.setState),rC("onSelectionDragStart",$,en.setState),rC("onSelectionDragStop",O,en.setState),rC("noPanClassName",B,en.setState),rC("nodeOrigin",L,en.setState),rC("rfId",H,en.setState),rC("autoPanOnConnect",F,en.setState),rC("autoPanOnNodeDrag",V,en.setState),rC("onError",Z,en.setState),rC("connectionRadius",Y,en.setState),rC("isValidConnection",q,en.setState),rC("nodeDragThreshold",X,en.setState),rk(e,K),rk(t,W),rk(g,G),rk(y,Q),rk(E,J),rk(x,ee),null},rA={display:"none"},rz={position:"absolute",width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0px, 0px, 0px, 0px)",clipPath:"inset(100%)"},rI="react-flow__node-desc",rP="react-flow__edge-desc",rT=e=>e.ariaLiveMessage;function rD({rfId:e}){let t=nr(rT);return m.createElement("div",{id:`react-flow__aria-live-${e}`,"aria-live":"assertive","aria-atomic":"true",style:rz},t)}function rR({rfId:e,disableKeyboardA11y:t}){return m.createElement(m.Fragment,null,m.createElement("div",{id:`${rI}-${e}`,style:rA},"Press enter or space to select a node.",!t&&"You can then use the arrow keys to move the node around."," Press delete to remove it and escape to cancel."," "),m.createElement("div",{id:`${rP}-${e}`,style:rA},"Press enter or space to select an edge. You can then press delete to remove it or escape to cancel."),!t&&m.createElement(rD,{rfId:e}))}var r$=(e=null,t={actInsideInputWithModifier:!0})=>{let[n,r]=(0,m.useState)(!1),o=(0,m.useRef)(!1),a=(0,m.useRef)(new Set([])),[i,s]=(0,m.useMemo)(()=>{if(null!==e){let t=(Array.isArray(e)?e:[e]).filter(e=>"string"==typeof e).map(e=>e.split("+")),n=t.reduce((e,t)=>e.concat(...t),[]);return[t,n]}return[[],[]]},[e]);return(0,m.useEffect)(()=>{let n="undefined"!=typeof document?document:null,l=t?.target||n;if(null!==e){let e=e=>{if(o.current=e.ctrlKey||e.metaKey||e.shiftKey,(!o.current||o.current&&!t.actInsideInputWithModifier)&&nk(e))return!1;let n=rB(e.code,s);a.current.add(e[n]),rO(i,a.current,!1)&&(e.preventDefault(),r(!0))},n=e=>{if((!o.current||o.current&&!t.actInsideInputWithModifier)&&nk(e))return!1;let n=rB(e.code,s);rO(i,a.current,!0)?(r(!1),a.current.clear()):a.current.delete(e[n]),"Meta"===e.key&&a.current.clear(),o.current=!1},c=()=>{a.current.clear(),r(!1)};return l?.addEventListener("keydown",e),l?.addEventListener("keyup",n),window.addEventListener("blur",c),()=>{l?.removeEventListener("keydown",e),l?.removeEventListener("keyup",n),window.removeEventListener("blur",c)}}},[e,r]),n};function rO(e,t,n){return e.filter(e=>n||e.length===t.size).some(e=>e.every(e=>t.has(e)))}function rB(e,t){return t.includes(e)?"code":"key"}function rL(e,t,n){e.forEach(r=>{let o=r.parentNode||r.parentId;if(o&&!e.has(o))throw Error(`Parent node ${o} not found`);if(o||n?.[r.id]){let{x:o,y:a,z:i}=function e(t,n,r,o){let a=t.parentNode||t.parentId;if(!a)return r;let i=n.get(a),s=n5(i,o);return e(i,n,{x:(r.x??0)+s.x,y:(r.y??0)+s.y,z:(i[nS]?.z??0)>(r.z??0)?i[nS]?.z??0:r.z??0},o)}(r,e,{...r.position,z:r[nS]?.z??0},t);r.positionAbsolute={x:o,y:a},r[nS].z=i,n?.[r.id]&&(r[nS].isParent=!0)}})}function rH(e,t,n,r){let o=new Map,a={},i=r?1e3:0;return e.forEach(e=>{let n=(nN(e.zIndex)?e.zIndex:0)+(e.selected?i:0),r=t.get(e.id),s={...e,positionAbsolute:{x:e.position.x,y:e.position.y}},l=e.parentNode||e.parentId;l&&(a[l]=!0),Object.defineProperty(s,nS,{enumerable:!1,value:{handleBounds:r?.type&&r?.type!==e.type?void 0:r?.[nS]?.handleBounds,z:n}}),o.set(e.id,s)}),rL(o,n,a),o}function rF(e,t={}){let{getNodes:n,width:r,height:o,minZoom:a,maxZoom:i,d3Zoom:s,d3Selection:l,fitViewOnInitDone:c,fitViewOnInit:u,nodeOrigin:d}=e(),h=t.initial&&!c&&u;if(s&&l&&(h||!t.initial)){let e=n().filter(e=>{let n=t.includeHiddenNodes?e.width&&e.height:!e.hidden;return t.nodes?.length?n&&t.nodes.some(t=>t.id===e.id):n}),c=e.every(e=>e.width&&e.height);if(e.length>0&&c){let{x:n,y:c,zoom:u}=n9(n6(e,d),r,o,t.minZoom??a,t.maxZoom??i,t.padding??.1),h=tq.translate(n,c).scale(u);return"number"==typeof t.duration&&t.duration>0?s.transform(re(l,t.duration),h):s.transform(l,h),!0}}return!1}function rV({changedNodes:e,changedEdges:t,get:n,set:r}){let{nodeInternals:o,edges:a,onNodesChange:i,onEdgesChange:s,hasDefaultNodes:l,hasDefaultEdges:c}=n();e?.length&&(l&&r({nodeInternals:(e.forEach(e=>{let t=o.get(e.id);t&&o.set(t.id,{...t,[nS]:t[nS],selected:e.selected})}),new Map(o))}),i?.(e)),t?.length&&(c&&r({edges:a.map(e=>{let n=t.find(t=>t.id===e.id);return n&&(e.selected=n.selected),e})}),s?.(t))}let rZ=()=>{},rY={zoomIn:rZ,zoomOut:rZ,zoomTo:rZ,getZoom:()=>1,setViewport:rZ,getViewport:()=>({x:0,y:0,zoom:1}),fitView:()=>!1,setCenter:rZ,fitBounds:rZ,project:e=>e,screenToFlowPosition:e=>e,flowToScreenPosition:e=>e,viewportInitialized:!1},rq=e=>({d3Zoom:e.d3Zoom,d3Selection:e.d3Selection}),rX=()=>{let e=no(),{d3Zoom:t,d3Selection:n}=nr(rq,j);return(0,m.useMemo)(()=>n&&t?{zoomIn:e=>t.scaleBy(re(n,e?.duration),1.2),zoomOut:e=>t.scaleBy(re(n,e?.duration),1/1.2),zoomTo:(e,r)=>t.scaleTo(re(n,r?.duration),e),getZoom:()=>e.getState().transform[2],setViewport:(r,o)=>{let[a,i,s]=e.getState().transform,l=tq.translate(r.x??a,r.y??i).scale(r.zoom??s);t.transform(re(n,o?.duration),l)},getViewport:()=>{let[t,n,r]=e.getState().transform;return{x:t,y:n,zoom:r}},fitView:t=>rF(e.getState,t),setCenter:(r,o,a)=>{let{width:i,height:s,maxZoom:l}=e.getState(),c=void 0!==a?.zoom?a.zoom:l,u=i/2-r*c,d=s/2-o*c,h=tq.translate(u,d).scale(c);t.transform(re(n,a?.duration),h)},fitBounds:(r,o)=>{let{width:a,height:i,minZoom:s,maxZoom:l}=e.getState(),{x:c,y:u,zoom:d}=n9(r,a,i,s,l,o?.padding??.1),h=tq.translate(c,u).scale(d);t.transform(re(n,o?.duration),h)},project:t=>{let{transform:n,snapToGrid:r,snapGrid:o}=e.getState();return console.warn("[DEPRECATED] `project` is deprecated. Instead use `screenToFlowPosition`. There is no need to subtract the react flow bounds anymore! https://reactflow.dev/api-reference/types/react-flow-instance#screen-to-flow-position"),n3(t,n,r,o)},screenToFlowPosition:t=>{let{transform:n,snapToGrid:r,snapGrid:o,domNode:a}=e.getState();if(!a)return t;let{x:i,y:s}=a.getBoundingClientRect();return n3({x:t.x-i,y:t.y-s},n,r,o)},flowToScreenPosition:t=>{let{transform:n,domNode:r}=e.getState();if(!r)return t;let{x:o,y:a}=r.getBoundingClientRect(),i=n4(t,n);return{x:i.x+o,y:i.y+a}},viewportInitialized:!0}:rY,[t,n])};function rK(){let e=rX(),t=no(),n=(0,m.useCallback)(()=>t.getState().getNodes().map(e=>({...e})),[]),r=(0,m.useCallback)(e=>t.getState().nodeInternals.get(e),[]),o=(0,m.useCallback)(()=>{let{edges:e=[]}=t.getState();return e.map(e=>({...e}))},[]),a=(0,m.useCallback)(e=>{let{edges:n=[]}=t.getState();return n.find(t=>t.id===e)},[]),i=(0,m.useCallback)(e=>{let{getNodes:n,setNodes:r,hasDefaultNodes:o,onNodesChange:a}=t.getState(),i=n(),s="function"==typeof e?e(i):e;o?r(s):a&&a(0===s.length?i.map(e=>({type:"remove",id:e.id})):s.map(e=>({item:e,type:"reset"})))},[]),s=(0,m.useCallback)(e=>{let{edges:n=[],setEdges:r,hasDefaultEdges:o,onEdgesChange:a}=t.getState(),i="function"==typeof e?e(n):e;o?r(i):a&&a(0===i.length?n.map(e=>({type:"remove",id:e.id})):i.map(e=>({item:e,type:"reset"})))},[]),l=(0,m.useCallback)(e=>{let n=Array.isArray(e)?e:[e],{getNodes:r,setNodes:o,hasDefaultNodes:a,onNodesChange:i}=t.getState();a?o([...r(),...n]):i&&i(n.map(e=>({item:e,type:"add"})))},[]),c=(0,m.useCallback)(e=>{let n=Array.isArray(e)?e:[e],{edges:r=[],setEdges:o,hasDefaultEdges:a,onEdgesChange:i}=t.getState();a?o([...r,...n]):i&&i(n.map(e=>({item:e,type:"add"})))},[]),u=(0,m.useCallback)(()=>{let{getNodes:e,edges:n=[],transform:r}=t.getState(),[o,a,i]=r;return{nodes:e().map(e=>({...e})),edges:n.map(e=>({...e})),viewport:{x:o,y:a,zoom:i}}},[]),d=(0,m.useCallback)(({nodes:e,edges:n})=>{let{nodeInternals:r,getNodes:o,edges:a,hasDefaultNodes:i,hasDefaultEdges:s,onNodesDelete:l,onEdgesDelete:c,onNodesChange:u,onEdgesChange:d}=t.getState(),h=(e||[]).map(e=>e.id),f=(n||[]).map(e=>e.id),m=o().reduce((e,t)=>{let n=t.parentNode||t.parentId,r=!h.includes(t.id)&&n&&e.find(e=>e.id===n);return("boolean"!=typeof t.deletable||t.deletable)&&(h.includes(t.id)||r)&&e.push(t),e},[]),p=a.filter(e=>"boolean"!=typeof e.deletable||e.deletable),g=p.filter(e=>f.includes(e.id));if(m||g){let e=[...g,...n8(m,p)],n=e.reduce((e,t)=>(e.includes(t.id)||e.push(t.id),e),[]);(s||i)&&(s&&t.setState({edges:a.filter(e=>!n.includes(e.id))}),i&&(m.forEach(e=>{r.delete(e.id)}),t.setState({nodeInternals:new Map(r)}))),n.length>0&&(c?.(e),d&&d(n.map(e=>({id:e,type:"remove"})))),m.length>0&&(l?.(m),u&&u(m.map(e=>({id:e.id,type:"remove"}))))}},[]),h=(0,m.useCallback)(e=>{let n=nw(e),r=n?null:t.getState().nodeInternals.get(e.id);return n||r?[n?e:nx(r),r,n]:[null,null,n]},[]),f=(0,m.useCallback)((e,n=!0,r)=>{let[o,a,i]=h(e);return o?(r||t.getState().getNodes()).filter(e=>{if(!i&&(e.id===a.id||!e.positionAbsolute))return!1;let t=nb(nx(e),o);return n&&t>0||t>=o.width*o.height}):[]},[]),p=(0,m.useCallback)((e,t,n=!0)=>{let[r]=h(e);if(!r)return!1;let o=nb(r,t);return n&&o>0||o>=r.width*r.height},[]);return(0,m.useMemo)(()=>({...e,getNodes:n,getNode:r,getEdges:o,getEdge:a,setNodes:i,setEdges:s,addNodes:l,addEdges:c,toObject:u,deleteElements:d,getIntersectingNodes:f,isNodeIntersecting:p}),[e,n,r,o,a,i,s,l,c,u,d,f,p])}let rW={actInsideInputWithModifier:!1};var rU=({deleteKeyCode:e,multiSelectionKeyCode:t})=>{let n=no(),{deleteElements:r}=rK(),o=r$(e,rW),a=r$(t);(0,m.useEffect)(()=>{if(o){let{edges:e,getNodes:t}=n.getState();r({nodes:t().filter(e=>e.selected),edges:e.filter(e=>e.selected)}),n.setState({nodesSelectionActive:!1})}},[o]),(0,m.useEffect)(()=>{n.setState({multiSelectionActive:a})},[a])};let rG={position:"absolute",width:"100%",height:"100%",top:0,left:0},rQ=(e,t)=>e.x!==t.x||e.y!==t.y||e.zoom!==t.k,rJ=e=>({x:e.x,y:e.y,zoom:e.k}),r0=(e,t)=>e.target.closest(`.${t}`),r1=(e,t)=>2===t&&Array.isArray(e)&&e.includes(2),r2=e=>{let t=e.ctrlKey&&nA()?10:1;return-e.deltaY*(1===e.deltaMode?.05:e.deltaMode?1:.002)*t},r3=e=>({d3Zoom:e.d3Zoom,d3Selection:e.d3Selection,d3ZoomHandler:e.d3ZoomHandler,userSelectionActive:e.userSelectionActive}),r4=({onMove:e,onMoveStart:t,onMoveEnd:n,onPaneContextMenu:r,zoomOnScroll:o=!0,zoomOnPinch:a=!0,panOnScroll:i=!1,panOnScrollSpeed:l=.5,panOnScrollMode:c=s.Free,zoomOnDoubleClick:u=!0,elementsSelectable:d,panOnDrag:h=!0,defaultViewport:f,translateExtent:p,minZoom:g,maxZoom:y,zoomActivationKeyCode:x,preventScrolling:v=!0,children:b,noWheelClassName:w,noPanClassName:N})=>{let S=(0,m.useRef)(),_=no(),E=(0,m.useRef)(!1),k=(0,m.useRef)(!1),C=(0,m.useRef)(null),M=(0,m.useRef)({x:0,y:0,zoom:0}),{d3Zoom:A,d3Selection:z,d3ZoomHandler:I,userSelectionActive:P}=nr(r3,j),T=r$(x),D=(0,m.useRef)(0),R=(0,m.useRef)(!1),$=(0,m.useRef)();return function(e){let t=no();(0,m.useEffect)(()=>{let n;let r=()=>{if(!e.current)return;let n=nc(e.current);(0===n.height||0===n.width)&&t.getState().onError?.("004",nt.error004()),t.setState({width:n.width||500,height:n.height||500})};return r(),window.addEventListener("resize",r),e.current&&(n=new ResizeObserver(()=>r())).observe(e.current),()=>{window.removeEventListener("resize",r),n&&e.current&&n.unobserve(e.current)}},[])}(C),(0,m.useEffect)(()=>{if(C.current){let e=C.current.getBoundingClientRect(),t=t1().scaleExtent([g,y]).translateExtent(p),n=ex(C.current).call(t),r=tq.translate(f.x,f.y).scale(nu(f.zoom,g,y)),o=[[0,0],[e.width,e.height]],a=t.constrain()(r,o,p);t.transform(n,a),t.wheelDelta(r2),_.setState({d3Zoom:t,d3Selection:n,d3ZoomHandler:n.on("wheel.zoom"),transform:[a.x,a.y,a.k],domNode:C.current.closest(".react-flow")})}},[]),(0,m.useEffect)(()=>{z&&A&&(!i||T||P?void 0!==I&&z.on("wheel.zoom",function(e,t){if(!v&&"wheel"===e.type&&!e.ctrlKey||r0(e,w))return null;e.preventDefault(),I.call(this,e,t)},{passive:!1}):z.on("wheel.zoom",r=>{if(r0(r,w))return!1;r.preventDefault(),r.stopImmediatePropagation();let o=z.property("__zoom").k||1;if(r.ctrlKey&&a){let e=ek(r),t=r2(r);A.scaleTo(z,o*Math.pow(2,t),e,r);return}let i=1===r.deltaMode?20:1,u=c===s.Vertical?0:r.deltaX*i,d=c===s.Horizontal?0:r.deltaY*i;!nA()&&r.shiftKey&&c!==s.Vertical&&(u=r.deltaY*i,d=0),A.translateBy(z,-(u/o)*l,-(d/o)*l,{internal:!0});let h=rJ(z.property("__zoom")),{onViewportChangeStart:f,onViewportChange:m,onViewportChangeEnd:p}=_.getState();clearTimeout($.current),R.current||(R.current=!0,t?.(r,h),f?.(h)),R.current&&(e?.(r,h),m?.(h),$.current=setTimeout(()=>{n?.(r,h),p?.(h),R.current=!1},150))},{passive:!1}))},[P,i,c,z,A,I,T,a,v,w,t,e,n]),(0,m.useEffect)(()=>{A&&A.on("start",e=>{if(!e.sourceEvent||e.sourceEvent.internal)return null;D.current=e.sourceEvent?.button;let{onViewportChangeStart:n}=_.getState(),r=rJ(e.transform);E.current=!0,M.current=r,e.sourceEvent?.type==="mousedown"&&_.setState({paneDragging:!0}),n?.(r),t?.(e.sourceEvent,r)})},[A,t]),(0,m.useEffect)(()=>{A&&(P&&!E.current?A.on("zoom",null):P||A.on("zoom",t=>{let{onViewportChange:n}=_.getState();if(_.setState({transform:[t.transform.x,t.transform.y,t.transform.k]}),k.current=!!(r&&r1(h,D.current??0)),(e||n)&&!t.sourceEvent?.internal){let r=rJ(t.transform);n?.(r),e?.(t.sourceEvent,r)}}))},[P,A,e,h,r]),(0,m.useEffect)(()=>{A&&A.on("end",e=>{if(!e.sourceEvent||e.sourceEvent.internal)return null;let{onViewportChangeEnd:t}=_.getState();if(E.current=!1,_.setState({paneDragging:!1}),r&&r1(h,D.current??0)&&!k.current&&r(e.sourceEvent),k.current=!1,(n||t)&&rQ(M.current,e.transform)){let r=rJ(e.transform);M.current=r,clearTimeout(S.current),S.current=setTimeout(()=>{t?.(r),n?.(e.sourceEvent,r)},i?150:0)}})},[A,i,h,n,r]),(0,m.useEffect)(()=>{A&&A.filter(e=>{let t=T||o,n=a&&e.ctrlKey;if((!0===h||Array.isArray(h)&&h.includes(1))&&1===e.button&&"mousedown"===e.type&&(r0(e,"react-flow__node")||r0(e,"react-flow__edge")))return!0;if(!h&&!t&&!i&&!u&&!a||P||!u&&"dblclick"===e.type||r0(e,w)&&"wheel"===e.type||r0(e,N)&&("wheel"!==e.type||i&&"wheel"===e.type&&!T)||!a&&e.ctrlKey&&"wheel"===e.type||!t&&!i&&!n&&"wheel"===e.type||!h&&("mousedown"===e.type||"touchstart"===e.type)||Array.isArray(h)&&!h.includes(e.button)&&"mousedown"===e.type)return!1;let r=Array.isArray(h)&&h.includes(e.button)||!e.button||e.button<=1;return(!e.ctrlKey||"wheel"===e.type)&&r})},[P,A,o,a,i,u,h,d,T]),m.createElement("div",{className:"react-flow__renderer",ref:C,style:rG},b)},r5=e=>({userSelectionActive:e.userSelectionActive,userSelectionRect:e.userSelectionRect});function r6(){let{userSelectionActive:e,userSelectionRect:t}=nr(r5,j);return e&&t?m.createElement("div",{className:"react-flow__selection react-flow__container",style:{width:t.width,height:t.height,transform:`translate(${t.x}px, ${t.y}px)`}}):null}function r7(e,t){let n=t.parentNode||t.parentId,r=e.find(e=>e.id===n);if(r){let e=t.position.x+t.width-r.width,n=t.position.y+t.height-r.height;if(e>0||n>0||t.position.x<0||t.position.y<0){if(r.style={...r.style},r.style.width=r.style.width??r.width,r.style.height=r.style.height??r.height,e>0&&(r.style.width+=e),n>0&&(r.style.height+=n),t.position.x<0){let e=Math.abs(t.position.x);r.position.x=r.position.x-e,r.style.width+=e,t.position.x=0}if(t.position.y<0){let e=Math.abs(t.position.y);r.position.y=r.position.y-e,r.style.height+=e,t.position.y=0}r.width=r.style.width,r.height=r.style.height}}}function r8(e,t){if(e.some(e=>"reset"===e.type))return e.filter(e=>"reset"===e.type).map(e=>e.item);let n=e.filter(e=>"add"===e.type).map(e=>e.item);return t.reduce((t,n)=>{let r=e.filter(e=>e.id===n.id);if(0===r.length)return t.push(n),t;let o={...n};for(let e of r)if(e)switch(e.type){case"select":o.selected=e.selected;break;case"position":void 0!==e.position&&(o.position=e.position),void 0!==e.positionAbsolute&&(o.positionAbsolute=e.positionAbsolute),void 0!==e.dragging&&(o.dragging=e.dragging),o.expandParent&&r7(t,o);break;case"dimensions":void 0!==e.dimensions&&(o.width=e.dimensions.width,o.height=e.dimensions.height),void 0!==e.updateStyle&&(o.style={...o.style||{},...e.dimensions}),"boolean"==typeof e.resizing&&(o.resizing=e.resizing),o.expandParent&&r7(t,o);break;case"remove":return t}return t.push(o),t},n)}function r9(e,t){return r8(e,t)}function oe(e,t){return r8(e,t)}let ot=(e,t)=>({id:e,type:"select",selected:t});function on(e,t){return e.reduce((e,n)=>{let r=t.includes(n.id);return!n.selected&&r?(n.selected=!0,e.push(ot(n.id,!0))):n.selected&&!r&&(n.selected=!1,e.push(ot(n.id,!1))),e},[])}let or=(e,t)=>n=>{n.target===t.current&&e?.(n)},oo=e=>({userSelectionActive:e.userSelectionActive,elementsSelectable:e.elementsSelectable,dragging:e.paneDragging}),oa=(0,m.memo)(({isSelecting:e,selectionMode:t=l.Full,panOnDrag:n,onSelectionStart:r,onSelectionEnd:o,onPaneClick:a,onPaneContextMenu:i,onPaneScroll:s,onPaneMouseEnter:c,onPaneMouseMove:u,onPaneMouseLeave:d,children:h})=>{let f=(0,m.useRef)(null),p=no(),y=(0,m.useRef)(0),x=(0,m.useRef)(0),v=(0,m.useRef)(),{userSelectionActive:b,elementsSelectable:w,dragging:N}=nr(oo,j),S=()=>{p.setState({userSelectionActive:!1,userSelectionRect:null}),y.current=0,x.current=0},_=e=>{a?.(e),p.getState().resetSelectedElements(),p.setState({nodesSelectionActive:!1})},E=w&&(e||b);return m.createElement("div",{className:g(["react-flow__pane",{dragging:N,selection:e}]),onClick:E?void 0:or(_,f),onContextMenu:or(e=>{if(Array.isArray(n)&&n?.includes(2)){e.preventDefault();return}i?.(e)},f),onWheel:or(s?e=>s(e):void 0,f),onMouseEnter:E?void 0:c,onMouseDown:E?t=>{let{resetSelectedElements:n,domNode:o}=p.getState();if(v.current=o?.getBoundingClientRect(),!w||!e||0!==t.button||t.target!==f.current||!v.current)return;let{x:a,y:i}=nM(t,v.current);n(),p.setState({userSelectionRect:{width:0,height:0,startX:a,startY:i,x:a,y:i}}),r?.(t)}:void 0,onMouseMove:E?n=>{let{userSelectionRect:r,nodeInternals:o,edges:a,transform:i,onNodesChange:s,onEdgesChange:c,nodeOrigin:u,getNodes:d}=p.getState();if(!e||!v.current||!r)return;p.setState({userSelectionActive:!0,nodesSelectionActive:!1});let h=nM(n,v.current),f=r.startX??0,m=r.startY??0,g={...r,x:h.x<f?h.x:f,y:h.y<m?h.y:m,width:Math.abs(h.x-f),height:Math.abs(h.y-m)},b=d(),w=n7(o,g,i,t===l.Partial,!0,u),N=n8(w,a).map(e=>e.id),S=w.map(e=>e.id);if(y.current!==S.length){y.current=S.length;let e=on(b,S);e.length&&s?.(e)}if(x.current!==N.length){x.current=N.length;let e=on(a,N);e.length&&c?.(e)}p.setState({userSelectionRect:g})}:u,onMouseUp:E?e=>{if(0!==e.button)return;let{userSelectionRect:t}=p.getState();!b&&t&&e.target===f.current&&_?.(e),p.setState({nodesSelectionActive:y.current>0}),S(),o?.(e)}:void 0,onMouseLeave:E?e=>{b&&(p.setState({nodesSelectionActive:y.current>0}),o?.(e)),S()}:d,ref:f,style:rG},h,m.createElement(r6,null))});function oi(e,t,n){let r=e;do{if(r?.matches(t))return!0;if(r===n.current)break;r=r.parentElement}while(r);return!1}function os(e,t,n,r,o=[0,0],a){var i;let s=(i=e.extent||r)&&"parent"!==i?[i[0],[i[1][0]-(e.width||0),i[1][1]-(e.height||0)]]:i,l=s,c=e.parentNode||e.parentId;if("parent"!==e.extent||e.expandParent){if(e.extent&&c&&"parent"!==e.extent){let{x:t,y:r}=n5(n.get(c),o).positionAbsolute;l=[[e.extent[0][0]+t,e.extent[0][1]+r],[e.extent[1][0]+t,e.extent[1][1]+r]]}}else if(c&&e.width&&e.height){let t=n.get(c),{x:r,y:a}=n5(t,o).positionAbsolute;l=t&&nN(r)&&nN(a)&&nN(t.width)&&nN(t.height)?[[r+e.width*o[0],a+e.height*o[1]],[r+t.width-e.width+e.width*o[0],a+t.height-e.height+e.height*o[1]]]:l}else a?.("005",nt.error005()),l=s;let u={x:0,y:0};c&&(u=n5(n.get(c),o).positionAbsolute);let d=l&&"parent"!==l?nd(t,l):t;return{position:{x:d.x-u.x,y:d.y-u.y},positionAbsolute:d}}function ol({nodeId:e,dragItems:t,nodeInternals:n}){let r=t.map(e=>({...n.get(e.id),position:e.position,positionAbsolute:e.positionAbsolute}));return[e?r.find(t=>t.id===e):r[0],r]}oa.displayName="Pane";let oc=(e,t,n,r)=>{let o=t.querySelectorAll(e);if(!o||!o.length)return null;let a=Array.from(o),i=t.getBoundingClientRect(),s={x:i.width*r[0],y:i.height*r[1]};return a.map(e=>{let t=e.getBoundingClientRect();return{id:e.getAttribute("data-handleid"),position:e.getAttribute("data-handlepos"),x:(t.left-i.left-s.x)/n,y:(t.top-i.top-s.y)/n,...nc(e)}})};function ou(e,t,n){return void 0===n?n:r=>{let o=t().nodeInternals.get(e);o&&n(r,{...o})}}function od({id:e,store:t,unselect:n=!1,nodeRef:r}){let{addSelectedNodes:o,unselectNodesAndEdges:a,multiSelectionActive:i,nodeInternals:s,onError:l}=t.getState(),c=s.get(e);if(!c){l?.("012",nt.error012(e));return}t.setState({nodesSelectionActive:!1}),c.selected?(n||c.selected&&i)&&(a({nodes:[c],edges:[]}),requestAnimationFrame(()=>r?.current?.blur())):o([e])}function oh(e){return(t,n,r)=>e?.(t,r)}function of({nodeRef:e,disabled:t=!1,noDragClassName:n,handleSelector:r,nodeId:o,isSelectable:a,selectNodesOnDrag:i}){let s=no(),[l,c]=(0,m.useState)(!1),u=(0,m.useRef)([]),d=(0,m.useRef)({x:null,y:null}),h=(0,m.useRef)(0),f=(0,m.useRef)(null),p=(0,m.useRef)({x:0,y:0}),g=(0,m.useRef)(null),y=(0,m.useRef)(!1),x=(0,m.useRef)(!1),v=(0,m.useRef)(!1),b=function(){let e=no();return(0,m.useCallback)(({sourceEvent:t})=>{let{transform:n,snapGrid:r,snapToGrid:o}=e.getState(),a=t.touches?t.touches[0].clientX:t.clientX,i=t.touches?t.touches[0].clientY:t.clientY,s={x:(a-n[0])/n[2],y:(i-n[1])/n[2]};return{xSnapped:o?r[0]*Math.round(s.x/r[0]):s.x,ySnapped:o?r[1]*Math.round(s.y/r[1]):s.y,...s}},[])}();return(0,m.useEffect)(()=>{if(e?.current){let l=ex(e.current),m=({x:e,y:t})=>{let{nodeInternals:n,onNodeDrag:r,onSelectionDrag:a,updateNodePositions:i,nodeExtent:l,snapGrid:h,snapToGrid:f,nodeOrigin:m,onError:p}=s.getState();d.current={x:e,y:t};let y=!1,x={x:0,y:0,x2:0,y2:0};if(u.current.length>1&&l&&(x=ng(n6(u.current,m))),u.current=u.current.map(r=>{let o={x:e-r.distance.x,y:t-r.distance.y};f&&(o.x=h[0]*Math.round(o.x/h[0]),o.y=h[1]*Math.round(o.y/h[1]));let a=[[l[0][0],l[0][1]],[l[1][0],l[1][1]]];u.current.length>1&&l&&!r.extent&&(a[0][0]=r.positionAbsolute.x-x.x+l[0][0],a[1][0]=r.positionAbsolute.x+(r.width??0)-x.x2+l[1][0],a[0][1]=r.positionAbsolute.y-x.y+l[0][1],a[1][1]=r.positionAbsolute.y+(r.height??0)-x.y2+l[1][1]);let i=os(r,o,n,a,m,p);return y=y||r.position.x!==i.position.x||r.position.y!==i.position.y,r.position=i.position,r.positionAbsolute=i.positionAbsolute,r}),!y)return;i(u.current,!0,!0),c(!0);let v=o?r:oh(a);if(v&&g.current){let[e,t]=ol({nodeId:o,dragItems:u.current,nodeInternals:n});v(g.current,e,t)}},w=()=>{if(!f.current)return;let[e,t]=nf(p.current,f.current);if(0!==e||0!==t){let{transform:n,panBy:r}=s.getState();d.current.x=(d.current.x??0)-e/n[2],d.current.y=(d.current.y??0)-t/n[2],r({x:e,y:t})&&m(d.current)}h.current=requestAnimationFrame(w)},N=t=>{let{nodeInternals:n,multiSelectionActive:r,nodesDraggable:l,unselectNodesAndEdges:c,onNodeDragStart:h,onSelectionDragStart:f}=s.getState();x.current=!0;let m=o?h:oh(f);i&&a||r||!o||n.get(o)?.selected||c(),o&&a&&i&&od({id:o,store:s,nodeRef:e});let p=b(t);if(d.current=p,u.current=Array.from(n.values()).filter(e=>(e.selected||e.id===o)&&(!e.parentNode||e.parentId||!function e(t,n){let r=t.parentNode||t.parentId;if(!r)return!1;let o=n.get(r);return!!o&&(!!o.selected||e(o,n))}(e,n))&&(e.draggable||l&&void 0===e.draggable)).map(e=>({id:e.id,position:e.position||{x:0,y:0},positionAbsolute:e.positionAbsolute||{x:0,y:0},distance:{x:p.x-(e.positionAbsolute?.x??0),y:p.y-(e.positionAbsolute?.y??0)},delta:{x:0,y:0},extent:e.extent,parentNode:e.parentNode||e.parentId,parentId:e.parentNode||e.parentId,width:e.width,height:e.height,expandParent:e.expandParent})),m&&u.current){let[e,r]=ol({nodeId:o,dragItems:u.current,nodeInternals:n});m(t.sourceEvent,e,r)}};if(t)l.on(".drag",null);else{let t=(function(){var e,t,n,r,o=t4,a=t5,i=t6,s=t7,l={},c=C("start","drag","end"),u=0,d=0;function h(e){e.on("mousedown.drag",f).filter(s).on("touchstart.drag",g).on("touchmove.drag",y,ev).on("touchend.drag touchcancel.drag",x).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function f(i,s){if(!r&&o.call(this,i,s)){var l=v(this,a.call(this,i,s),i,s,"mouse");l&&(ex(i.view).on("mousemove.drag",m,eb).on("mouseup.drag",p,eb),eS(i.view),ew(i),n=!1,e=i.clientX,t=i.clientY,l("start",i))}}function m(r){if(eN(r),!n){var o=r.clientX-e,a=r.clientY-t;n=o*o+a*a>d}l.mouse("drag",r)}function p(e){ex(e.view).on("mousemove.drag mouseup.drag",null),e_(e.view,n),eN(e),l.mouse("end",e)}function g(e,t){if(o.call(this,e,t)){var n,r,i=e.changedTouches,s=a.call(this,e,t),l=i.length;for(n=0;n<l;++n)(r=v(this,s,e,t,i[n].identifier,i[n]))&&(ew(e),r("start",e,i[n]))}}function y(e){var t,n,r=e.changedTouches,o=r.length;for(t=0;t<o;++t)(n=l[r[t].identifier])&&(eN(e),n("drag",e,r[t]))}function x(e){var t,n,o=e.changedTouches,a=o.length;for(r&&clearTimeout(r),r=setTimeout(function(){r=null},500),t=0;t<a;++t)(n=l[o[t].identifier])&&(ew(e),n("end",e,o[t]))}function v(e,t,n,r,o,a){var s,d,f,m=c.copy(),p=ek(a||n,t);if(null!=(f=i.call(e,new t3("beforestart",{sourceEvent:n,target:h,identifier:o,active:u,x:p[0],y:p[1],dx:0,dy:0,dispatch:m}),r)))return s=f.x-p[0]||0,d=f.y-p[1]||0,function n(a,i,c){var g,y=p;switch(a){case"start":l[o]=n,g=u++;break;case"end":delete l[o],--u;case"drag":p=ek(c||i,t),g=u}m.call(a,e,new t3(a,{sourceEvent:i,subject:f,target:h,identifier:o,active:g,x:p[0]+s,y:p[1]+d,dx:p[0]-y[0],dy:p[1]-y[1],dispatch:m}),r)}}return h.filter=function(e){return arguments.length?(o="function"==typeof e?e:t2(!!e),h):o},h.container=function(e){return arguments.length?(a="function"==typeof e?e:t2(e),h):a},h.subject=function(e){return arguments.length?(i="function"==typeof e?e:t2(e),h):i},h.touchable=function(e){return arguments.length?(s="function"==typeof e?e:t2(!!e),h):s},h.on=function(){var e=c.on.apply(c,arguments);return e===c?h:e},h.clickDistance=function(e){return arguments.length?(d=(e=+e)*e,h):Math.sqrt(d)},h})().on("start",e=>{let{domNode:t,nodeDragThreshold:n}=s.getState();0===n&&N(e),v.current=!1;let r=b(e);d.current=r,f.current=t?.getBoundingClientRect()||null,p.current=nM(e.sourceEvent,f.current)}).on("drag",e=>{let t=b(e),{autoPanOnNodeDrag:n,nodeDragThreshold:r}=s.getState();if("touchmove"===e.sourceEvent.type&&e.sourceEvent.touches.length>1&&(v.current=!0),!v.current){if(!y.current&&x.current&&n&&(y.current=!0,w()),!x.current){let n=t.xSnapped-(d?.current?.x??0),o=t.ySnapped-(d?.current?.y??0);Math.sqrt(n*n+o*o)>r&&N(e)}(d.current.x!==t.xSnapped||d.current.y!==t.ySnapped)&&u.current&&x.current&&(g.current=e.sourceEvent,p.current=nM(e.sourceEvent,f.current),m(t))}}).on("end",e=>{if(x.current&&!v.current&&(c(!1),y.current=!1,x.current=!1,cancelAnimationFrame(h.current),u.current)){let{updateNodePositions:t,nodeInternals:n,onNodeDragStop:r,onSelectionDragStop:a}=s.getState(),i=o?r:oh(a);if(t(u.current,!1,!1),i){let[t,r]=ol({nodeId:o,dragItems:u.current,nodeInternals:n});i(e.sourceEvent,t,r)}}}).filter(t=>{let o=t.target;return!t.button&&(!n||!oi(o,`.${n}`,e))&&(!r||oi(o,r,e))});return l.call(t),()=>{l.on(".drag",null)}}}},[e,t,n,r,a,s,o,i,b]),l}function om(){let e=no();return(0,m.useCallback)(t=>{let{nodeInternals:n,nodeExtent:r,updateNodePositions:o,getNodes:a,snapToGrid:i,snapGrid:s,onError:l,nodesDraggable:c}=e.getState(),u=a().filter(e=>e.selected&&(e.draggable||c&&void 0===e.draggable)),d=i?s[0]:5,h=i?s[1]:5,f=t.isShiftPressed?4:1,m=t.x*d*f,p=t.y*h*f;o(u.map(e=>{if(e.positionAbsolute){let t={x:e.positionAbsolute.x+m,y:e.positionAbsolute.y+p};i&&(t.x=s[0]*Math.round(t.x/s[0]),t.y=s[1]*Math.round(t.y/s[1]));let{positionAbsolute:o,position:a}=os(e,t,n,r,void 0,l);e.position=a,e.positionAbsolute=o}return e}),!0,!1)},[])}let op={ArrowUp:{x:0,y:-1},ArrowDown:{x:0,y:1},ArrowLeft:{x:-1,y:0},ArrowRight:{x:1,y:0}};var og=e=>{let t=({id:t,type:n,data:r,xPos:o,yPos:a,xPosOrigin:i,yPosOrigin:s,selected:l,onClick:c,onMouseEnter:u,onMouseMove:d,onMouseLeave:h,onContextMenu:f,onDoubleClick:p,style:y,className:x,isDraggable:v,isSelectable:b,isConnectable:w,isFocusable:N,selectNodesOnDrag:S,sourcePosition:_,targetPosition:E,hidden:j,resizeObserver:k,dragHandle:C,zIndex:M,isParent:A,noDragClassName:z,noPanClassName:I,initialized:P,disableKeyboardA11y:T,ariaLabel:D,rfId:R,hasHandleBounds:$})=>{let O=no(),B=(0,m.useRef)(null),L=(0,m.useRef)(null),H=(0,m.useRef)(_),F=(0,m.useRef)(E),V=(0,m.useRef)(n),Z=b||v||c||u||d||h,Y=om(),q=ou(t,O.getState,u),X=ou(t,O.getState,d),K=ou(t,O.getState,h),W=ou(t,O.getState,f),U=ou(t,O.getState,p);(0,m.useEffect)(()=>()=>{L.current&&(k?.unobserve(L.current),L.current=null)},[]),(0,m.useEffect)(()=>{if(B.current&&!j){let e=B.current;P&&$&&L.current===e||(L.current&&k?.unobserve(L.current),k?.observe(e),L.current=e)}},[j,P,$]),(0,m.useEffect)(()=>{let e=V.current!==n,r=H.current!==_,o=F.current!==E;B.current&&(e||r||o)&&(e&&(V.current=n),r&&(H.current=_),o&&(F.current=E),O.getState().updateNodeDimensions([{id:t,nodeElement:B.current,forceUpdate:!0}]))},[t,n,_,E]);let G=of({nodeRef:B,disabled:j||!v,noDragClassName:z,handleSelector:C,nodeId:t,isSelectable:b,selectNodesOnDrag:S});return j?null:m.createElement("div",{className:g(["react-flow__node",`react-flow__node-${n}`,{[I]:v},x,{selected:l,selectable:b,parent:A,dragging:G}]),ref:B,style:{zIndex:M,transform:`translate(${i}px,${s}px)`,pointerEvents:Z?"all":"none",visibility:P?"visible":"hidden",...y},"data-id":t,"data-testid":`rf__node-${t}`,onMouseEnter:q,onMouseMove:X,onMouseLeave:K,onContextMenu:W,onClick:e=>{let{nodeDragThreshold:n}=O.getState();if(b&&(!S||!v||n>0)&&od({id:t,store:O,nodeRef:B}),c){let n=O.getState().nodeInternals.get(t);n&&c(e,{...n})}},onDoubleClick:U,onKeyDown:N?e=>{!nk(e)&&!T&&(n_.includes(e.key)&&b?od({id:t,store:O,unselect:"Escape"===e.key,nodeRef:B}):v&&l&&Object.prototype.hasOwnProperty.call(op,e.key)&&(O.setState({ariaLiveMessage:`Moved selected node ${e.key.replace("Arrow","").toLowerCase()}. New position, x: ${~~o}, y: ${~~a}`}),Y({x:op[e.key].x,y:op[e.key].y,isShiftPressed:e.shiftKey})))}:void 0,tabIndex:N?0:void 0,role:N?"button":void 0,"aria-describedby":T?void 0:`${rI}-${R}`,"aria-label":D},m.createElement(nU,{value:t},m.createElement(e,{id:t,data:r,type:n,xPos:o,yPos:a,selected:l,isConnectable:w,sourcePosition:_,targetPosition:E,dragging:G,dragHandle:C,zIndex:M})))};return t.displayName="NodeWrapper",(0,m.memo)(t)};let oy=e=>({...n6(e.getNodes().filter(e=>e.selected),e.nodeOrigin),transformString:`translate(${e.transform[0]}px,${e.transform[1]}px) scale(${e.transform[2]})`,userSelectionActive:e.userSelectionActive});var ox=(0,m.memo)(function({onSelectionContextMenu:e,noPanClassName:t,disableKeyboardA11y:n}){let r=no(),{width:o,height:a,x:i,y:s,transformString:l,userSelectionActive:c}=nr(oy,j),u=om(),d=(0,m.useRef)(null);return((0,m.useEffect)(()=>{n||d.current?.focus({preventScroll:!0})},[n]),of({nodeRef:d}),!c&&o&&a)?m.createElement("div",{className:g(["react-flow__nodesselection","react-flow__container",t]),style:{transform:l}},m.createElement("div",{ref:d,className:"react-flow__nodesselection-rect",onContextMenu:e?t=>{e(t,r.getState().getNodes().filter(e=>e.selected))}:void 0,tabIndex:n?void 0:-1,onKeyDown:n?void 0:e=>{Object.prototype.hasOwnProperty.call(op,e.key)&&u({x:op[e.key].x,y:op[e.key].y,isShiftPressed:e.shiftKey})},style:{width:o,height:a,top:s,left:i}})):null});let ov=e=>e.nodesSelectionActive,ob=({children:e,onPaneClick:t,onPaneMouseEnter:n,onPaneMouseMove:r,onPaneMouseLeave:o,onPaneContextMenu:a,onPaneScroll:i,deleteKeyCode:s,onMove:l,onMoveStart:c,onMoveEnd:u,selectionKeyCode:d,selectionOnDrag:h,selectionMode:f,onSelectionStart:p,onSelectionEnd:g,multiSelectionKeyCode:y,panActivationKeyCode:x,zoomActivationKeyCode:v,elementsSelectable:b,zoomOnScroll:w,zoomOnPinch:N,panOnScroll:S,panOnScrollSpeed:_,panOnScrollMode:E,zoomOnDoubleClick:j,panOnDrag:k,defaultViewport:C,translateExtent:M,minZoom:A,maxZoom:z,preventScrolling:I,onSelectionContextMenu:P,noWheelClassName:T,noPanClassName:D,disableKeyboardA11y:R})=>{let $=nr(ov),O=r$(d),B=r$(x),L=B||k,H=B||S,F=O||h&&!0!==L;return rU({deleteKeyCode:s,multiSelectionKeyCode:y}),m.createElement(r4,{onMove:l,onMoveStart:c,onMoveEnd:u,onPaneContextMenu:a,elementsSelectable:b,zoomOnScroll:w,zoomOnPinch:N,panOnScroll:H,panOnScrollSpeed:_,panOnScrollMode:E,zoomOnDoubleClick:j,panOnDrag:!O&&L,defaultViewport:C,translateExtent:M,minZoom:A,maxZoom:z,zoomActivationKeyCode:v,preventScrolling:I,noWheelClassName:T,noPanClassName:D},m.createElement(oa,{onSelectionStart:p,onSelectionEnd:g,onPaneClick:t,onPaneMouseEnter:n,onPaneMouseMove:r,onPaneMouseLeave:o,onPaneContextMenu:a,onPaneScroll:i,panOnDrag:L,isSelecting:!!F,selectionMode:f},e,$&&m.createElement(ox,{onSelectionContextMenu:P,noPanClassName:D,disableKeyboardA11y:R})))};ob.displayName="FlowRenderer";var ow=(0,m.memo)(ob);function oN(e){let t={input:og(e.input||rg),default:og(e.default||rm),output:og(e.output||rx),group:og(e.group||rv)},n=Object.keys(e).filter(e=>!["input","default","output","group"].includes(e)).reduce((t,n)=>(t[n]=og(e[n]||rm),t),{});return{...t,...n}}let oS=({x:e,y:t,width:n,height:r,origin:o})=>!n||!r||o[0]<0||o[1]<0||o[0]>1||o[1]>1?{x:e,y:t}:{x:e-n*o[0],y:t-r*o[1]},o_=e=>({nodesDraggable:e.nodesDraggable,nodesConnectable:e.nodesConnectable,nodesFocusable:e.nodesFocusable,elementsSelectable:e.elementsSelectable,updateNodeDimensions:e.updateNodeDimensions,onError:e.onError}),oE=e=>{let{nodesDraggable:t,nodesConnectable:n,nodesFocusable:r,elementsSelectable:o,updateNodeDimensions:a,onError:i}=nr(o_,j),s=function(e){return nr((0,m.useCallback)(t=>e?n7(t.nodeInternals,{x:0,y:0,width:t.width,height:t.height},t.transform,!0):t.getNodes(),[e]))}(e.onlyRenderVisibleElements),l=(0,m.useRef)(),c=(0,m.useMemo)(()=>{if("undefined"==typeof ResizeObserver)return null;let e=new ResizeObserver(e=>{a(e.map(e=>({id:e.target.getAttribute("data-id"),nodeElement:e.target,forceUpdate:!0})))});return l.current=e,e},[]);return(0,m.useEffect)(()=>()=>{l?.current?.disconnect()},[]),m.createElement("div",{className:"react-flow__nodes",style:rG},s.map(a=>{let s=a.type||"default";e.nodeTypes[s]||(i?.("003",nt.error003(s)),s="default");let l=e.nodeTypes[s]||e.nodeTypes.default,u=!!(a.draggable||t&&void 0===a.draggable),h=!!(a.selectable||o&&void 0===a.selectable),f=!!(a.connectable||n&&void 0===a.connectable),p=!!(a.focusable||r&&void 0===a.focusable),g=e.nodeExtent?nd(a.positionAbsolute,e.nodeExtent):a.positionAbsolute,y=g?.x??0,x=g?.y??0,v=oS({x:y,y:x,width:a.width??0,height:a.height??0,origin:e.nodeOrigin});return m.createElement(l,{key:a.id,id:a.id,className:a.className,style:a.style,type:s,data:a.data,sourcePosition:a.sourcePosition||d.Bottom,targetPosition:a.targetPosition||d.Top,hidden:a.hidden,xPos:y,yPos:x,xPosOrigin:v.x,yPosOrigin:v.y,selectNodesOnDrag:e.selectNodesOnDrag,onClick:e.onNodeClick,onMouseEnter:e.onNodeMouseEnter,onMouseMove:e.onNodeMouseMove,onMouseLeave:e.onNodeMouseLeave,onContextMenu:e.onNodeContextMenu,onDoubleClick:e.onNodeDoubleClick,selected:!!a.selected,isDraggable:u,isSelectable:h,isConnectable:f,isFocusable:p,resizeObserver:c,dragHandle:a.dragHandle,zIndex:a[nS]?.z??0,isParent:!!a[nS]?.isParent,noDragClassName:e.noDragClassName,noPanClassName:e.noPanClassName,initialized:!!a.width&&!!a.height,rfId:e.rfId,disableKeyboardA11y:e.disableKeyboardA11y,ariaLabel:a.ariaLabel,hasHandleBounds:!!a[nS]?.handleBounds})}))};oE.displayName="NodeRenderer";var oj=(0,m.memo)(oE);let ok=(e,t,n)=>n===d.Left?e-t:n===d.Right?e+t:e,oC=(e,t,n)=>n===d.Top?e-t:n===d.Bottom?e+t:e,oM="react-flow__edgeupdater",oA=({position:e,centerX:t,centerY:n,radius:r=10,onMouseDown:o,onMouseEnter:a,onMouseOut:i,type:s})=>m.createElement("circle",{onMouseDown:o,onMouseEnter:a,onMouseOut:i,className:g([oM,`${oM}-${s}`]),cx:ok(t,r,e),cy:oC(n,r,e),r:r,stroke:"transparent",fill:"transparent"}),oz=()=>!0;var oI=e=>{let t=({id:t,className:n,type:r,data:o,onClick:a,onEdgeDoubleClick:i,selected:s,animated:l,label:c,labelStyle:u,labelShowBg:d,labelBgStyle:h,labelBgPadding:f,labelBgBorderRadius:p,style:y,source:x,target:v,sourceX:b,sourceY:w,targetX:N,targetY:S,sourcePosition:_,targetPosition:E,elementsSelectable:j,hidden:k,sourceHandleId:C,targetHandleId:M,onContextMenu:A,onMouseEnter:z,onMouseMove:I,onMouseLeave:P,reconnectRadius:T,onReconnect:D,onReconnectStart:R,onReconnectEnd:$,markerEnd:O,markerStart:B,rfId:L,ariaLabel:H,isFocusable:F,isReconnectable:V,pathOptions:Z,interactionWidth:Y,disableKeyboardA11y:q})=>{let X=(0,m.useRef)(null),[K,W]=(0,m.useState)(!1),[U,G]=(0,m.useState)(!1),Q=no(),J=(0,m.useMemo)(()=>`url('#${n0(B,L)}')`,[B,L]),ee=(0,m.useMemo)(()=>`url('#${n0(O,L)}')`,[O,L]);if(k)return null;let et=nI(t,Q.getState,i),en=nI(t,Q.getState,A),er=nI(t,Q.getState,z),eo=nI(t,Q.getState,I),ea=nI(t,Q.getState,P),ei=(e,n)=>{if(0!==e.button)return;let{edges:r,isValidConnection:o}=Q.getState(),a=n?v:x,i=(n?M:C)||null,s=n?"target":"source",l=o||oz,c=r.find(e=>e.id===t);G(!0),R?.(e,c,s),rs({event:e,handleId:i,nodeId:a,onConnect:e=>D?.(c,e),isTarget:n,getState:Q.getState,setState:Q.setState,isValidConnection:l,edgeUpdaterType:s,onReconnectEnd:e=>{G(!1),$?.(e,c,s)}})},es=()=>W(!0),el=()=>W(!1);return m.createElement("g",{className:g(["react-flow__edge",`react-flow__edge-${r}`,n,{selected:s,animated:l,inactive:!j&&!a,updating:K}]),onClick:e=>{let{edges:n,addSelectedEdges:r,unselectNodesAndEdges:o,multiSelectionActive:i}=Q.getState(),s=n.find(e=>e.id===t);s&&(j&&(Q.setState({nodesSelectionActive:!1}),s.selected&&i?(o({nodes:[],edges:[s]}),X.current?.blur()):r([t])),a&&a(e,s))},onDoubleClick:et,onContextMenu:en,onMouseEnter:er,onMouseMove:eo,onMouseLeave:ea,onKeyDown:F?e=>{if(!q&&n_.includes(e.key)&&j){let{unselectNodesAndEdges:n,addSelectedEdges:r,edges:o}=Q.getState();"Escape"===e.key?(X.current?.blur(),n({edges:[o.find(e=>e.id===t)]})):r([t])}}:void 0,tabIndex:F?0:void 0,role:F?"button":"img","data-testid":`rf__edge-${t}`,"aria-label":null===H?void 0:H||`Edge from ${x} to ${v}`,"aria-describedby":F?`${rP}-${L}`:void 0,ref:X},!U&&m.createElement(e,{id:t,source:x,target:v,selected:s,animated:l,label:c,labelStyle:u,labelShowBg:d,labelBgStyle:h,labelBgPadding:f,labelBgBorderRadius:p,data:o,style:y,sourceX:b,sourceY:w,targetX:N,targetY:S,sourcePosition:_,targetPosition:E,sourceHandleId:C,targetHandleId:M,markerStart:J,markerEnd:ee,pathOptions:Z,interactionWidth:Y}),V&&m.createElement(m.Fragment,null,("source"===V||!0===V)&&m.createElement(oA,{position:_,centerX:b,centerY:w,radius:T,onMouseDown:e=>ei(e,!0),onMouseEnter:es,onMouseOut:el,type:"source"}),("target"===V||!0===V)&&m.createElement(oA,{position:E,centerX:N,centerY:S,radius:T,onMouseDown:e=>ei(e,!1),onMouseEnter:es,onMouseOut:el,type:"target"})))};return t.displayName="EdgeWrapper",(0,m.memo)(t)};function oP(e){let t={default:oI(e.default||nK),straight:oI(e.bezier||nZ),step:oI(e.step||nV),smoothstep:oI(e.step||nF),simplebezier:oI(e.simplebezier||n$)},n=Object.keys(e).filter(e=>!["default","bezier"].includes(e)).reduce((t,n)=>(t[n]=oI(e[n]||nK),t),{});return{...t,...n}}function oT(e,t,n=null){let r=(n?.x||0)+t.x,o=(n?.y||0)+t.y,a=n?.width||t.width,i=n?.height||t.height;switch(e){case d.Top:return{x:r+a/2,y:o};case d.Right:return{x:r+a,y:o+i/2};case d.Bottom:return{x:r+a/2,y:o+i};case d.Left:return{x:r,y:o+i/2}}}function oD(e,t){return e?1!==e.length&&t?t&&e.find(e=>e.id===t)||null:e[0]:null}let oR=(e,t,n,r,o,a)=>{let i=oT(n,e,t),s=oT(a,r,o);return{sourceX:i.x,sourceY:i.y,targetX:s.x,targetY:s.y}};function o$(e){let t=e?.[nS]?.handleBounds||null,n=t&&e?.width&&e?.height&&void 0!==e?.positionAbsolute?.x&&void 0!==e?.positionAbsolute?.y;return[{x:e?.positionAbsolute?.x||0,y:e?.positionAbsolute?.y||0,width:e?.width||0,height:e?.height||0},t,!!n]}let oO=[{level:0,isMaxLevel:!0,edges:[]}],oB={[u.Arrow]:({color:e="none",strokeWidth:t=1})=>m.createElement("polyline",{style:{stroke:e,strokeWidth:t},strokeLinecap:"round",strokeLinejoin:"round",fill:"none",points:"-5,-4 0,0 -5,4"}),[u.ArrowClosed]:({color:e="none",strokeWidth:t=1})=>m.createElement("polyline",{style:{stroke:e,fill:e,strokeWidth:t},strokeLinecap:"round",strokeLinejoin:"round",points:"-5,-4 0,0 -5,4 -5,-4"})},oL=({id:e,type:t,color:n,width:r=12.5,height:o=12.5,markerUnits:a="strokeWidth",strokeWidth:i,orient:s="auto-start-reverse"})=>{let l=function(e){let t=no();return(0,m.useMemo)(()=>Object.prototype.hasOwnProperty.call(oB,e)?oB[e]:(t.getState().onError?.("009",nt.error009(e)),null),[e])}(t);return l?m.createElement("marker",{className:"react-flow__arrowhead",id:e,markerWidth:`${r}`,markerHeight:`${o}`,viewBox:"-10 -10 20 20",markerUnits:a,orient:s,refX:"0",refY:"0"},m.createElement(l,{color:n,strokeWidth:i})):null},oH=({defaultColor:e,rfId:t})=>n=>{let r=[];return n.edges.reduce((n,o)=>([o.markerStart,o.markerEnd].forEach(o=>{if(o&&"object"==typeof o){let a=n0(o,t);r.includes(a)||(n.push({id:a,color:o.color||e,...o}),r.push(a))}}),n),[]).sort((e,t)=>e.id.localeCompare(t.id))},oF=({defaultColor:e,rfId:t})=>{let n=nr((0,m.useCallback)(oH({defaultColor:e,rfId:t}),[e,t]),(e,t)=>!(e.length!==t.length||e.some((e,n)=>e.id!==t[n].id)));return m.createElement("defs",null,n.map(e=>m.createElement(oL,{id:e.id,key:e.id,type:e.type,color:e.color,width:e.width,height:e.height,markerUnits:e.markerUnits,strokeWidth:e.strokeWidth,orient:e.orient})))};oF.displayName="MarkerDefinitions";var oV=(0,m.memo)(oF);let oZ=e=>({nodesConnectable:e.nodesConnectable,edgesFocusable:e.edgesFocusable,edgesUpdatable:e.edgesUpdatable,elementsSelectable:e.elementsSelectable,width:e.width,height:e.height,connectionMode:e.connectionMode,nodeInternals:e.nodeInternals,onError:e.onError}),oY=({defaultMarkerColor:e,onlyRenderVisibleElements:t,elevateEdgesOnSelect:n,rfId:r,edgeTypes:o,noPanClassName:a,onEdgeContextMenu:s,onEdgeMouseEnter:l,onEdgeMouseMove:c,onEdgeMouseLeave:u,onEdgeClick:h,onEdgeDoubleClick:f,onReconnect:p,onReconnectStart:y,onReconnectEnd:x,reconnectRadius:v,children:b,disableKeyboardA11y:w})=>{let{edgesFocusable:N,edgesUpdatable:S,elementsSelectable:_,width:E,height:k,connectionMode:C,nodeInternals:M,onError:A}=nr(oZ,j),z=function(e,t,n){return function(e,t,n=!1){let r=-1,o=Object.entries(e.reduce((e,o)=>{let a=nN(o.zIndex),i=a?o.zIndex:0;if(n){let e=t.get(o.target),n=t.get(o.source),r=o.selected||e?.selected||n?.selected,s=Math.max(n?.[nS]?.z||0,e?.[nS]?.z||0,1e3);i=(a?o.zIndex:0)+(r?s:0)}return e[i]?e[i].push(o):e[i]=[o],r=i>r?i:r,e},{})).map(([e,t])=>{let n=+e;return{edges:t,level:n,isMaxLevel:n===r}});return 0===o.length?oO:o}(nr((0,m.useCallback)(n=>e?n.edges.filter(e=>{let r=t.get(e.source),o=t.get(e.target);return r?.width&&r?.height&&o?.width&&o?.height&&function({sourcePos:e,targetPos:t,sourceWidth:n,sourceHeight:r,targetWidth:o,targetHeight:a,width:i,height:s,transform:l}){let c={x:Math.min(e.x,t.x),y:Math.min(e.y,t.y),x2:Math.max(e.x+n,t.x+o),y2:Math.max(e.y+r,t.y+a)};c.x===c.x2&&(c.x2+=1),c.y===c.y2&&(c.y2+=1);let u=ng({x:(0-l[0])/l[2],y:(0-l[1])/l[2],width:i/l[2],height:s/l[2]});return Math.ceil(Math.max(0,Math.min(u.x2,c.x2)-Math.max(u.x,c.x))*Math.max(0,Math.min(u.y2,c.y2)-Math.max(u.y,c.y)))>0}({sourcePos:r.positionAbsolute||{x:0,y:0},targetPos:o.positionAbsolute||{x:0,y:0},sourceWidth:r.width,sourceHeight:r.height,targetWidth:o.width,targetHeight:o.height,width:n.width,height:n.height,transform:n.transform})}):n.edges,[e,t])),t,n)}(t,M,n);return E?m.createElement(m.Fragment,null,z.map(({level:t,edges:n,isMaxLevel:b})=>m.createElement("svg",{key:t,style:{zIndex:t},width:E,height:k,className:"react-flow__edges react-flow__container"},b&&m.createElement(oV,{defaultColor:e,rfId:r}),m.createElement("g",null,n.map(e=>{let[t,n,b]=o$(M.get(e.source)),[E,j,k]=o$(M.get(e.target));if(!b||!k)return null;let z=e.type||"default";o[z]||(A?.("011",nt.error011(z)),z="default");let I=o[z]||o.default,P=C===i.Strict?j.target:(j.target??[]).concat(j.source??[]),T=oD(n.source,e.sourceHandle),D=oD(P,e.targetHandle),R=T?.position||d.Bottom,$=D?.position||d.Top,O=!!(e.focusable||N&&void 0===e.focusable),B=e.reconnectable||e.updatable;if(!T||!D)return A?.("008",nt.error008(T,e)),null;let{sourceX:L,sourceY:H,targetX:F,targetY:V}=oR(t,T,R,E,D,$);return m.createElement(I,{key:e.id,id:e.id,className:g([e.className,a]),type:z,data:e.data,selected:!!e.selected,animated:!!e.animated,hidden:!!e.hidden,label:e.label,labelStyle:e.labelStyle,labelShowBg:e.labelShowBg,labelBgStyle:e.labelBgStyle,labelBgPadding:e.labelBgPadding,labelBgBorderRadius:e.labelBgBorderRadius,style:e.style,source:e.source,target:e.target,sourceHandleId:e.sourceHandle,targetHandleId:e.targetHandle,markerEnd:e.markerEnd,markerStart:e.markerStart,sourceX:L,sourceY:H,targetX:F,targetY:V,sourcePosition:R,targetPosition:$,elementsSelectable:_,onContextMenu:s,onMouseEnter:l,onMouseMove:c,onMouseLeave:u,onClick:h,onEdgeDoubleClick:f,onReconnect:p,onReconnectStart:y,onReconnectEnd:x,reconnectRadius:v,rfId:r,ariaLabel:e.ariaLabel,isFocusable:O,isReconnectable:void 0!==p&&(B||S&&void 0===B),pathOptions:"pathOptions"in e?e.pathOptions:void 0,interactionWidth:e.interactionWidth,disableKeyboardA11y:w})})))),b):null};oY.displayName="EdgeRenderer";var oq=(0,m.memo)(oY);let oX=e=>`translate(${e.transform[0]}px,${e.transform[1]}px) scale(${e.transform[2]})`;function oK({children:e}){let t=nr(oX);return m.createElement("div",{className:"react-flow__viewport react-flow__container",style:{transform:t}},e)}let oW={[d.Left]:d.Right,[d.Right]:d.Left,[d.Top]:d.Bottom,[d.Bottom]:d.Top},oU=({nodeId:e,handleType:t,style:n,type:r=c.Bezier,CustomComponent:o,connectionStatus:a})=>{let{fromNode:s,handleId:l,toX:u,toY:d,connectionMode:h}=nr((0,m.useCallback)(t=>({fromNode:t.nodeInternals.get(e),handleId:t.connectionHandleId,toX:(t.connectionPosition.x-t.transform[0])/t.transform[2],toY:(t.connectionPosition.y-t.transform[1])/t.transform[2],connectionMode:t.connectionMode}),[e]),j),f=s?.[nS]?.handleBounds,p=f?.[t];if(h===i.Loose&&(p=p||f?.["source"===t?"target":"source"]),!s||!p)return null;let g=l?p.find(e=>e.id===l):p[0],y=g?g.x+g.width/2:(s.width??0)/2,x=g?g.y+g.height/2:s.height??0,v=(s.positionAbsolute?.x??0)+y,b=(s.positionAbsolute?.y??0)+x,w=g?.position,N=w?oW[w]:null;if(!w||!N)return null;if(o)return m.createElement(o,{connectionLineType:r,connectionLineStyle:n,fromNode:s,fromHandle:g,fromX:v,fromY:b,toX:u,toY:d,fromPosition:w,toPosition:N,connectionStatus:a});let S="",_={sourceX:v,sourceY:b,sourcePosition:w,targetX:u,targetY:d,targetPosition:N};return r===c.Bezier?[S]=nX(_):r===c.Step?[S]=nH({..._,borderRadius:0}):r===c.SmoothStep?[S]=nH(_):r===c.SimpleBezier?[S]=nR(_):S=`M${v},${b} ${u},${d}`,m.createElement("path",{d:S,fill:"none",className:"react-flow__connection-path",style:n})};oU.displayName="ConnectionLine";let oG=e=>({nodeId:e.connectionNodeId,handleType:e.connectionHandleType,nodesConnectable:e.nodesConnectable,connectionStatus:e.connectionStatus,width:e.width,height:e.height});function oQ({containerStyle:e,style:t,type:n,component:r}){let{nodeId:o,handleType:a,nodesConnectable:i,width:s,height:l,connectionStatus:c}=nr(oG,j);return o&&a&&s&&i?m.createElement("svg",{style:e,width:s,height:l,className:"react-flow__edges react-flow__connectionline react-flow__container"},m.createElement("g",{className:g(["react-flow__connection",c])},m.createElement(oU,{nodeId:o,handleType:a,style:t,type:n,CustomComponent:r,connectionStatus:c}))):null}function oJ(e,t){return(0,m.useRef)(null),no(),(0,m.useMemo)(()=>t(e),[e])}let o0=({nodeTypes:e,edgeTypes:t,onMove:n,onMoveStart:r,onMoveEnd:o,onInit:a,onNodeClick:i,onEdgeClick:s,onNodeDoubleClick:l,onEdgeDoubleClick:c,onNodeMouseEnter:u,onNodeMouseMove:d,onNodeMouseLeave:h,onNodeContextMenu:f,onSelectionContextMenu:p,onSelectionStart:g,onSelectionEnd:y,connectionLineType:x,connectionLineStyle:v,connectionLineComponent:b,connectionLineContainerStyle:w,selectionKeyCode:N,selectionOnDrag:S,selectionMode:_,multiSelectionKeyCode:E,panActivationKeyCode:j,zoomActivationKeyCode:k,deleteKeyCode:C,onlyRenderVisibleElements:M,elementsSelectable:A,selectNodesOnDrag:z,defaultViewport:I,translateExtent:P,minZoom:T,maxZoom:D,preventScrolling:R,defaultMarkerColor:$,zoomOnScroll:O,zoomOnPinch:B,panOnScroll:L,panOnScrollSpeed:H,panOnScrollMode:F,zoomOnDoubleClick:V,panOnDrag:Z,onPaneClick:Y,onPaneMouseEnter:q,onPaneMouseMove:X,onPaneMouseLeave:K,onPaneScroll:W,onPaneContextMenu:U,onEdgeContextMenu:G,onEdgeMouseEnter:Q,onEdgeMouseMove:J,onEdgeMouseLeave:ee,onReconnect:et,onReconnectStart:en,onReconnectEnd:er,reconnectRadius:eo,noDragClassName:ea,noWheelClassName:ei,noPanClassName:es,elevateEdgesOnSelect:el,disableKeyboardA11y:ec,nodeOrigin:eu,nodeExtent:ed,rfId:eh})=>{let ef=oJ(e,oN),em=oJ(t,oP);return function(e){let t=rK(),n=(0,m.useRef)(!1);(0,m.useEffect)(()=>{!n.current&&t.viewportInitialized&&e&&(setTimeout(()=>e(t),1),n.current=!0)},[e,t.viewportInitialized])}(a),m.createElement(ow,{onPaneClick:Y,onPaneMouseEnter:q,onPaneMouseMove:X,onPaneMouseLeave:K,onPaneContextMenu:U,onPaneScroll:W,deleteKeyCode:C,selectionKeyCode:N,selectionOnDrag:S,selectionMode:_,onSelectionStart:g,onSelectionEnd:y,multiSelectionKeyCode:E,panActivationKeyCode:j,zoomActivationKeyCode:k,elementsSelectable:A,onMove:n,onMoveStart:r,onMoveEnd:o,zoomOnScroll:O,zoomOnPinch:B,zoomOnDoubleClick:V,panOnScroll:L,panOnScrollSpeed:H,panOnScrollMode:F,panOnDrag:Z,defaultViewport:I,translateExtent:P,minZoom:T,maxZoom:D,onSelectionContextMenu:p,preventScrolling:R,noDragClassName:ea,noWheelClassName:ei,noPanClassName:es,disableKeyboardA11y:ec},m.createElement(oK,null,m.createElement(oq,{edgeTypes:em,onEdgeClick:s,onEdgeDoubleClick:c,onlyRenderVisibleElements:M,onEdgeContextMenu:G,onEdgeMouseEnter:Q,onEdgeMouseMove:J,onEdgeMouseLeave:ee,onReconnect:et,onReconnectStart:en,onReconnectEnd:er,reconnectRadius:eo,defaultMarkerColor:$,noPanClassName:es,elevateEdgesOnSelect:!!el,disableKeyboardA11y:ec,rfId:eh},m.createElement(oQ,{style:v,type:x,component:b,containerStyle:w})),m.createElement("div",{className:"react-flow__edgelabel-renderer"}),m.createElement(oj,{nodeTypes:ef,onNodeClick:i,onNodeDoubleClick:l,onNodeMouseEnter:u,onNodeMouseMove:d,onNodeMouseLeave:h,onNodeContextMenu:f,selectNodesOnDrag:z,onlyRenderVisibleElements:M,noPanClassName:es,noDragClassName:ea,disableKeyboardA11y:ec,nodeOrigin:eu,nodeExtent:ed,rfId:eh})))};o0.displayName="GraphView";var o1=(0,m.memo)(o0);let o2=[[Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY],[Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY]],o3={rfId:"1",width:0,height:0,transform:[0,0,1],nodeInternals:new Map,edges:[],onNodesChange:null,onEdgesChange:null,hasDefaultNodes:!1,hasDefaultEdges:!1,d3Zoom:null,d3Selection:null,d3ZoomHandler:void 0,minZoom:.5,maxZoom:2,translateExtent:o2,nodeExtent:o2,nodesSelectionActive:!1,userSelectionActive:!1,userSelectionRect:null,connectionNodeId:null,connectionHandleId:null,connectionHandleType:"source",connectionPosition:{x:0,y:0},connectionStatus:null,connectionMode:i.Strict,domNode:null,paneDragging:!1,noPanClassName:"nopan",nodeOrigin:[0,0],nodeDragThreshold:0,snapGrid:[15,15],snapToGrid:!1,nodesDraggable:!0,nodesConnectable:!0,nodesFocusable:!0,edgesFocusable:!0,edgesUpdatable:!0,elementsSelectable:!0,elevateNodesOnSelect:!0,fitViewOnInit:!1,fitViewOnInitDone:!1,fitViewOnInitOptions:void 0,onSelectionChange:[],multiSelectionActive:!1,connectionStartHandle:null,connectionEndHandle:null,connectionClickStartHandle:null,connectOnClick:!0,ariaLiveMessage:"",autoPanOnConnect:!0,autoPanOnNodeDrag:!0,connectionRadius:20,onError:nE,isValidConnection:void 0},o4=()=>E((e,t)=>({...o3,setNodes:n=>{let{nodeInternals:r,nodeOrigin:o,elevateNodesOnSelect:a}=t();e({nodeInternals:rH(n,r,o,a)})},getNodes:()=>Array.from(t().nodeInternals.values()),setEdges:n=>{let{defaultEdgeOptions:r={}}=t();e({edges:n.map(e=>({...r,...e}))})},setDefaultNodesAndEdges:(n,r)=>{let o=void 0!==n,a=void 0!==r;e({nodeInternals:o?rH(n,new Map,t().nodeOrigin,t().elevateNodesOnSelect):new Map,edges:a?r:[],hasDefaultNodes:o,hasDefaultEdges:a})},updateNodeDimensions:n=>{let{onNodesChange:r,nodeInternals:o,fitViewOnInit:a,fitViewOnInitDone:i,fitViewOnInitOptions:s,domNode:l,nodeOrigin:c}=t(),u=l?.querySelector(".react-flow__viewport");if(!u)return;let d=window.getComputedStyle(u),{m22:h}=new window.DOMMatrixReadOnly(d.transform),f=n.reduce((e,t)=>{let n=o.get(t.id);if(n?.hidden)o.set(n.id,{...n,[nS]:{...n[nS],handleBounds:void 0}});else if(n){let r=nc(t.nodeElement);r.width&&r.height&&(n.width!==r.width||n.height!==r.height||t.forceUpdate)&&(o.set(n.id,{...n,[nS]:{...n[nS],handleBounds:{source:oc(".source",t.nodeElement,h,c),target:oc(".target",t.nodeElement,h,c)}},...r}),e.push({id:n.id,type:"dimensions",dimensions:r}))}return e},[]);rL(o,c);let m=i||a&&!i&&rF(t,{initial:!0,...s});e({nodeInternals:new Map(o),fitViewOnInitDone:m}),f?.length>0&&r?.(f)},updateNodePositions:(e,n=!0,r=!1)=>{let{triggerNodeChanges:o}=t();o(e.map(e=>{let t={id:e.id,type:"position",dragging:r};return n&&(t.positionAbsolute=e.positionAbsolute,t.position=e.position),t}))},triggerNodeChanges:n=>{let{onNodesChange:r,nodeInternals:o,hasDefaultNodes:a,nodeOrigin:i,getNodes:s,elevateNodesOnSelect:l}=t();n?.length&&(a&&e({nodeInternals:rH(r9(n,s()),o,i,l)}),r?.(n))},addSelectedNodes:n=>{let r;let{multiSelectionActive:o,edges:a,getNodes:i}=t(),s=null;o?r=n.map(e=>ot(e,!0)):(r=on(i(),n),s=on(a,[])),rV({changedNodes:r,changedEdges:s,get:t,set:e})},addSelectedEdges:n=>{let r;let{multiSelectionActive:o,edges:a,getNodes:i}=t(),s=null;o?r=n.map(e=>ot(e,!0)):(r=on(a,n),s=on(i(),[])),rV({changedNodes:s,changedEdges:r,get:t,set:e})},unselectNodesAndEdges:({nodes:n,edges:r}={})=>{let{edges:o,getNodes:a}=t();rV({changedNodes:(n||a()).map(e=>(e.selected=!1,ot(e.id,!1))),changedEdges:(r||o).map(e=>ot(e.id,!1)),get:t,set:e})},setMinZoom:n=>{let{d3Zoom:r,maxZoom:o}=t();r?.scaleExtent([n,o]),e({minZoom:n})},setMaxZoom:n=>{let{d3Zoom:r,minZoom:o}=t();r?.scaleExtent([o,n]),e({maxZoom:n})},setTranslateExtent:n=>{t().d3Zoom?.translateExtent(n),e({translateExtent:n})},resetSelectedElements:()=>{let{edges:n,getNodes:r}=t();rV({changedNodes:r().filter(e=>e.selected).map(e=>ot(e.id,!1)),changedEdges:n.filter(e=>e.selected).map(e=>ot(e.id,!1)),get:t,set:e})},setNodeExtent:n=>{let{nodeInternals:r}=t();r.forEach(e=>{e.positionAbsolute=nd(e.position,n)}),e({nodeExtent:n,nodeInternals:new Map(r)})},panBy:e=>{let{transform:n,width:r,height:o,d3Zoom:a,d3Selection:i,translateExtent:s}=t();if(!a||!i||!e.x&&!e.y)return!1;let l=tq.translate(n[0]+e.x,n[1]+e.y).scale(n[2]),c=a?.constrain()(l,[[0,0],[r,o]],s);return a.transform(i,c),n[0]!==c.x||n[1]!==c.y||n[2]!==c.k},cancelConnection:()=>e({connectionNodeId:o3.connectionNodeId,connectionHandleId:o3.connectionHandleId,connectionHandleType:o3.connectionHandleType,connectionStatus:o3.connectionStatus,connectionStartHandle:o3.connectionStartHandle,connectionEndHandle:o3.connectionEndHandle}),reset:()=>e({...o3})}),Object.is),o5=({children:e})=>{let t=(0,m.useRef)(null);return t.current||(t.current=o4()),m.createElement(ne,{value:t.current},e)};o5.displayName="ReactFlowProvider";let o6=({children:e})=>(0,m.useContext)(t9)?m.createElement(m.Fragment,null,e):m.createElement(o5,null,e);o6.displayName="ReactFlowWrapper";let o7={input:rg,default:rm,output:rx,group:rv},o8={default:nK,straight:nZ,step:nV,smoothstep:nF,simplebezier:n$},o9=[0,0],ae=[15,15],at={x:0,y:0,zoom:1},an={width:"100%",height:"100%",overflow:"hidden",position:"relative",zIndex:0},ar=(0,m.forwardRef)(({nodes:e,edges:t,defaultNodes:n,defaultEdges:r,className:o,nodeTypes:a=o7,edgeTypes:u=o8,onNodeClick:d,onEdgeClick:h,onInit:f,onMove:p,onMoveStart:y,onMoveEnd:x,onConnect:v,onConnectStart:b,onConnectEnd:w,onClickConnectStart:N,onClickConnectEnd:S,onNodeMouseEnter:_,onNodeMouseMove:E,onNodeMouseLeave:j,onNodeContextMenu:k,onNodeDoubleClick:C,onNodeDragStart:M,onNodeDrag:A,onNodeDragStop:z,onNodesDelete:I,onEdgesDelete:P,onSelectionChange:T,onSelectionDragStart:D,onSelectionDrag:R,onSelectionDragStop:$,onSelectionContextMenu:O,onSelectionStart:B,onSelectionEnd:L,connectionMode:H=i.Strict,connectionLineType:F=c.Bezier,connectionLineStyle:V,connectionLineComponent:Z,connectionLineContainerStyle:Y,deleteKeyCode:q="Backspace",selectionKeyCode:X="Shift",selectionOnDrag:K=!1,selectionMode:W=l.Full,panActivationKeyCode:U="Space",multiSelectionKeyCode:G=nA()?"Meta":"Control",zoomActivationKeyCode:Q=nA()?"Meta":"Control",snapToGrid:J=!1,snapGrid:ee=ae,onlyRenderVisibleElements:et=!1,selectNodesOnDrag:en=!0,nodesDraggable:er,nodesConnectable:eo,nodesFocusable:ea,nodeOrigin:ei=o9,edgesFocusable:es,edgesUpdatable:el,elementsSelectable:ec,defaultViewport:eu=at,minZoom:ed=.5,maxZoom:eh=2,translateExtent:ef=o2,preventScrolling:em=!0,nodeExtent:ep,defaultMarkerColor:eg="#b1b1b7",zoomOnScroll:ey=!0,zoomOnPinch:ex=!0,panOnScroll:ev=!1,panOnScrollSpeed:eb=.5,panOnScrollMode:ew=s.Free,zoomOnDoubleClick:eN=!0,panOnDrag:eS=!0,onPaneClick:e_,onPaneMouseEnter:eE,onPaneMouseMove:ej,onPaneMouseLeave:ek,onPaneScroll:eC,onPaneContextMenu:eM,children:eA,onEdgeContextMenu:ez,onEdgeDoubleClick:eI,onEdgeMouseEnter:eP,onEdgeMouseMove:eT,onEdgeMouseLeave:eD,onEdgeUpdate:eR,onEdgeUpdateStart:e$,onEdgeUpdateEnd:eO,onReconnect:eB,onReconnectStart:eL,onReconnectEnd:eH,reconnectRadius:eF=10,edgeUpdaterRadius:eV=10,onNodesChange:eZ,onEdgesChange:eY,noDragClassName:eq="nodrag",noWheelClassName:eX="nowheel",noPanClassName:eK="nopan",fitView:eW=!1,fitViewOptions:eU,connectOnClick:eG=!0,attributionPosition:eQ,proOptions:eJ,defaultEdgeOptions:e0,elevateNodesOnSelect:e1=!0,elevateEdgesOnSelect:e2=!1,disableKeyboardA11y:e3=!1,autoPanOnConnect:e4=!0,autoPanOnNodeDrag:e5=!0,connectionRadius:e6=20,isValidConnection:e7,onError:e8,style:e9,id:te,nodeDragThreshold:tt,...tn},tr)=>{let to=te||"1";return m.createElement("div",{...tn,style:{...e9,...an},ref:tr,className:g(["react-flow",o]),"data-testid":"rf__wrapper",id:te},m.createElement(o6,null,m.createElement(o1,{onInit:f,onMove:p,onMoveStart:y,onMoveEnd:x,onNodeClick:d,onEdgeClick:h,onNodeMouseEnter:_,onNodeMouseMove:E,onNodeMouseLeave:j,onNodeContextMenu:k,onNodeDoubleClick:C,nodeTypes:a,edgeTypes:u,connectionLineType:F,connectionLineStyle:V,connectionLineComponent:Z,connectionLineContainerStyle:Y,selectionKeyCode:X,selectionOnDrag:K,selectionMode:W,deleteKeyCode:q,multiSelectionKeyCode:G,panActivationKeyCode:U,zoomActivationKeyCode:Q,onlyRenderVisibleElements:et,selectNodesOnDrag:en,defaultViewport:eu,translateExtent:ef,minZoom:ed,maxZoom:eh,preventScrolling:em,zoomOnScroll:ey,zoomOnPinch:ex,zoomOnDoubleClick:eN,panOnScroll:ev,panOnScrollSpeed:eb,panOnScrollMode:ew,panOnDrag:eS,onPaneClick:e_,onPaneMouseEnter:eE,onPaneMouseMove:ej,onPaneMouseLeave:ek,onPaneScroll:eC,onPaneContextMenu:eM,onSelectionContextMenu:O,onSelectionStart:B,onSelectionEnd:L,onEdgeContextMenu:ez,onEdgeDoubleClick:eI,onEdgeMouseEnter:eP,onEdgeMouseMove:eT,onEdgeMouseLeave:eD,onReconnect:eB??eR,onReconnectStart:eL??e$,onReconnectEnd:eH??eO,reconnectRadius:eF??eV,defaultMarkerColor:eg,noDragClassName:eq,noWheelClassName:eX,noPanClassName:eK,elevateEdgesOnSelect:e2,rfId:to,disableKeyboardA11y:e3,nodeOrigin:ei,nodeExtent:ep}),m.createElement(rM,{nodes:e,edges:t,defaultNodes:n,defaultEdges:r,onConnect:v,onConnectStart:b,onConnectEnd:w,onClickConnectStart:N,onClickConnectEnd:S,nodesDraggable:er,nodesConnectable:eo,nodesFocusable:ea,edgesFocusable:es,edgesUpdatable:el,elementsSelectable:ec,elevateNodesOnSelect:e1,minZoom:ed,maxZoom:eh,nodeExtent:ep,onNodesChange:eZ,onEdgesChange:eY,snapToGrid:J,snapGrid:ee,connectionMode:H,translateExtent:ef,connectOnClick:eG,defaultEdgeOptions:e0,fitView:eW,fitViewOptions:eU,onNodesDelete:I,onEdgesDelete:P,onNodeDragStart:M,onNodeDrag:A,onNodeDragStop:z,onSelectionDrag:R,onSelectionDragStart:D,onSelectionDragStop:$,noPanClassName:eK,nodeOrigin:ei,rfId:to,autoPanOnConnect:e4,autoPanOnNodeDrag:e5,onError:e8,connectionRadius:e6,isValidConnection:e7,nodeDragThreshold:tt}),m.createElement(rE,{onSelectionChange:T}),eA,m.createElement(ns,{proOptions:eJ,position:eQ}),m.createElement(rR,{rfId:to,disableKeyboardA11y:e3})))});ar.displayName="ReactFlow";let ao=e=>e.domNode?.querySelector(".react-flow__edgelabel-renderer");function aa({children:e}){let t=nr(ao);return t?(0,t8.createPortal)(e,t):null}function ai(e){return t=>{let[n,r]=(0,m.useState)(t),o=(0,m.useCallback)(t=>r(n=>e(t,n)),[]);return[n,r,o]}}function as({color:e,dimensions:t,lineWidth:n}){return m.createElement("path",{stroke:e,strokeWidth:n,d:`M${t[0]/2} 0 V${t[1]} M0 ${t[1]/2} H${t[0]}`})}function al({color:e,radius:t}){return m.createElement("circle",{cx:t,cy:t,r:t,fill:e})}ai(r9),ai(oe),!function(e){e.Lines="lines",e.Dots="dots",e.Cross="cross"}(h||(h={}));let ac={[h.Dots]:"#91919a",[h.Lines]:"#eee",[h.Cross]:"#e2e2e2"},au={[h.Dots]:1,[h.Lines]:1,[h.Cross]:6},ad=e=>({transform:e.transform,patternId:`pattern-${e.rfId}`});function ah({id:e,variant:t=h.Dots,gap:n=20,size:r,lineWidth:o=1,offset:a=2,color:i,style:s,className:l}){let c=(0,m.useRef)(null),{transform:u,patternId:d}=nr(ad,j),f=i||ac[t],p=r||au[t],y=t===h.Dots,x=t===h.Cross,v=Array.isArray(n)?n:[n,n],b=[v[0]*u[2]||1,v[1]*u[2]||1],w=p*u[2],N=x?[w,w]:b,S=y?[w/a,w/a]:[N[0]/a,N[1]/a];return m.createElement("svg",{className:g(["react-flow__background",l]),style:{...s,position:"absolute",width:"100%",height:"100%",top:0,left:0},ref:c,"data-testid":"rf__background"},m.createElement("pattern",{id:d+e,x:u[0]%b[0],y:u[1]%b[1],width:b[0],height:b[1],patternUnits:"userSpaceOnUse",patternTransform:`translate(-${S[0]},-${S[1]})`},y?m.createElement(al,{color:f,radius:w/a}):m.createElement(as,{dimensions:N,color:f,lineWidth:o})),m.createElement("rect",{x:"0",y:"0",width:"100%",height:"100%",fill:`url(#${d+e})`}))}ah.displayName="Background";var af=(0,m.memo)(ah);function am(){return m.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32"},m.createElement("path",{d:"M32 18.133H18.133V32h-4.266V18.133H0v-4.266h13.867V0h4.266v13.867H32z"}))}function ap(){return m.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 5"},m.createElement("path",{d:"M0 0h32v4.2H0z"}))}function ag(){return m.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 30"},m.createElement("path",{d:"M3.692 4.63c0-.53.4-.938.939-.938h5.215V0H4.708C2.13 0 0 2.054 0 4.63v5.216h3.692V4.631zM27.354 0h-5.2v3.692h5.17c.53 0 .984.4.984.939v5.215H32V4.631A4.624 4.624 0 0027.354 0zm.954 24.83c0 .532-.4.94-.939.94h-5.215v3.768h5.215c2.577 0 4.631-2.13 4.631-4.707v-5.139h-3.692v5.139zm-23.677.94c-.531 0-.939-.4-.939-.94v-5.138H0v5.139c0 2.577 2.13 4.707 4.708 4.707h5.138V25.77H4.631z"}))}function ay(){return m.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32"},m.createElement("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0 8 0 4.571 3.429 4.571 7.619v3.048H3.048A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047zm4.724-13.866H7.467V7.619c0-2.59 2.133-4.724 4.723-4.724 2.591 0 4.724 2.133 4.724 4.724v3.048z"}))}function ax(){return m.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32"},m.createElement("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0c-4.114 1.828-1.37 2.133.305 2.438 1.676.305 4.42 2.59 4.42 5.181v3.048H3.047A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047z"}))}let av=({children:e,className:t,...n})=>m.createElement("button",{type:"button",className:g(["react-flow__controls-button",t]),...n},e);av.displayName="ControlButton";let ab=e=>({isInteractive:e.nodesDraggable||e.nodesConnectable||e.elementsSelectable,minZoomReached:e.transform[2]<=e.minZoom,maxZoomReached:e.transform[2]>=e.maxZoom}),aw=({style:e,showZoom:t=!0,showFitView:n=!0,showInteractive:r=!0,fitViewOptions:o,onZoomIn:a,onZoomOut:i,onFitView:s,onInteractiveChange:l,className:c,children:u,position:d="bottom-left"})=>{let h=no(),[f,p]=(0,m.useState)(!1),{isInteractive:y,minZoomReached:x,maxZoomReached:v}=nr(ab,j),{zoomIn:b,zoomOut:w,fitView:N}=rK();return((0,m.useEffect)(()=>{p(!0)},[]),f)?m.createElement(ni,{className:g(["react-flow__controls",c]),position:d,style:e,"data-testid":"rf__controls"},t&&m.createElement(m.Fragment,null,m.createElement(av,{onClick:()=>{b(),a?.()},className:"react-flow__controls-zoomin",title:"zoom in","aria-label":"zoom in",disabled:v},m.createElement(am,null)),m.createElement(av,{onClick:()=>{w(),i?.()},className:"react-flow__controls-zoomout",title:"zoom out","aria-label":"zoom out",disabled:x},m.createElement(ap,null))),n&&m.createElement(av,{className:"react-flow__controls-fitview",onClick:()=>{N(o),s?.()},title:"fit view","aria-label":"fit view"},m.createElement(ag,null)),r&&m.createElement(av,{className:"react-flow__controls-interactive",onClick:()=>{h.setState({nodesDraggable:!y,nodesConnectable:!y,elementsSelectable:!y}),l?.(!y)},title:"toggle interactivity","aria-label":"toggle interactivity"},y?m.createElement(ax,null):m.createElement(ay,null)),u):null};aw.displayName="Controls";var aN=(0,m.memo)(aw);let aS=({id:e,x:t,y:n,width:r,height:o,style:a,color:i,strokeColor:s,strokeWidth:l,className:c,borderRadius:u,shapeRendering:d,onClick:h,selected:f})=>{let{background:p,backgroundColor:y}=a||{};return m.createElement("rect",{className:g(["react-flow__minimap-node",{selected:f},c]),x:t,y:n,rx:u,ry:u,width:r,height:o,fill:i||p||y,stroke:s,strokeWidth:l,shapeRendering:d,onClick:h?t=>h(t,e):void 0})};aS.displayName="MiniMapNode";var a_=(0,m.memo)(aS);let aE=e=>e.nodeOrigin,aj=e=>e.getNodes().filter(e=>!e.hidden&&e.width&&e.height),ak=e=>e instanceof Function?e:()=>e;var aC=(0,m.memo)(function({nodeStrokeColor:e="transparent",nodeColor:t="#e2e2e2",nodeClassName:n="",nodeBorderRadius:r=5,nodeStrokeWidth:o=2,nodeComponent:a=a_,onClick:i}){let s=nr(aj,j),l=nr(aE),c=ak(t),u=ak(e),d=ak(n),h="undefined"==typeof window||window.chrome?"crispEdges":"geometricPrecision";return m.createElement(m.Fragment,null,s.map(e=>{let{x:t,y:n}=n5(e,l).positionAbsolute;return m.createElement(a,{key:e.id,x:t,y:n,width:e.width,height:e.height,style:e.style,selected:e.selected,className:d(e),color:c(e),borderRadius:r,strokeColor:u(e),strokeWidth:o,shapeRendering:h,onClick:i,id:e.id})}))});let aM=e=>{let t=e.getNodes(),n={x:-e.transform[0]/e.transform[2],y:-e.transform[1]/e.transform[2],width:e.width/e.transform[2],height:e.height/e.transform[2]};return{viewBB:n,boundingRect:t.length>0?nv(n6(t,e.nodeOrigin),n):n,rfId:e.rfId}};function aA({style:e,className:t,nodeStrokeColor:n="transparent",nodeColor:r="#e2e2e2",nodeClassName:o="",nodeBorderRadius:a=5,nodeStrokeWidth:i=2,nodeComponent:s,maskColor:l="rgb(240, 240, 240, 0.6)",maskStrokeColor:c="none",maskStrokeWidth:u=1,position:d="bottom-right",onClick:h,onNodeClick:f,pannable:p=!1,zoomable:y=!1,ariaLabel:x="React Flow mini map",inversePan:v=!1,zoomStep:b=10,offsetScale:w=5}){let N=no(),S=(0,m.useRef)(null),{boundingRect:_,viewBB:E,rfId:k}=nr(aM,j),C=e?.width??200,M=e?.height??150,A=Math.max(_.width/C,_.height/M),z=A*C,I=A*M,P=w*A,T=_.x-(z-_.width)/2-P,D=_.y-(I-_.height)/2-P,R=z+2*P,$=I+2*P,O=`react-flow__minimap-desc-${k}`,B=(0,m.useRef)(0);B.current=A,(0,m.useEffect)(()=>{if(S.current){let e=ex(S.current),t=t1().on("zoom",p?e=>{let{transform:t,d3Selection:n,d3Zoom:r,translateExtent:o,width:a,height:i}=N.getState();if("mousemove"!==e.sourceEvent.type||!n||!r)return;let s=B.current*Math.max(1,t[2])*(v?-1:1),l={x:t[0]-e.sourceEvent.movementX*s,y:t[1]-e.sourceEvent.movementY*s},c=tq.translate(l.x,l.y).scale(t[2]),u=r.constrain()(c,[[0,0],[a,i]],o);r.transform(n,u)}:null).on("zoom.wheel",y?e=>{let{transform:t,d3Selection:n,d3Zoom:r}=N.getState();if("wheel"!==e.sourceEvent.type||!n||!r)return;let o=-e.sourceEvent.deltaY*(1===e.sourceEvent.deltaMode?.05:e.sourceEvent.deltaMode?1:.002)*b,a=t[2]*Math.pow(2,o);r.scaleTo(n,a)}:null);return e.call(t),()=>{e.on("zoom",null)}}},[p,y,v,b]);let L=h?e=>{let t=ek(e);h(e,{x:t[0],y:t[1]})}:void 0;return m.createElement(ni,{position:d,style:e,className:g(["react-flow__minimap",t]),"data-testid":"rf__minimap"},m.createElement("svg",{width:C,height:M,viewBox:`${T} ${D} ${R} ${$}`,role:"img","aria-labelledby":O,ref:S,onClick:L},x&&m.createElement("title",{id:O},x),m.createElement(aC,{onClick:f?(e,t)=>{f(e,N.getState().nodeInternals.get(t))}:void 0,nodeColor:r,nodeStrokeColor:n,nodeBorderRadius:a,nodeClassName:o,nodeStrokeWidth:i,nodeComponent:s}),m.createElement("path",{className:"react-flow__minimap-mask",d:`M${T-P},${D-P}h${R+2*P}v${$+2*P}h${-R-2*P}z
        M${E.x},${E.y}h${E.width}v${E.height}h${-E.width}z`,fill:l,fillRule:"evenodd",stroke:c,strokeWidth:u,pointerEvents:"none"})))}aA.displayName="MiniMap";var az=(0,m.memo)(aA);n(5854);let{useDebugValue:aI}=m,{useSyncExternalStoreWithSelector:aP}=y,aT=!1,aD=e=>e,aR=e=>{"function"!=typeof e&&console.warn("[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.");let t="function"==typeof e?v(e):e,n=(e,n)=>(function(e,t=aD,n){n&&!aT&&(console.warn("[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937"),aT=!0);let r=aP(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return aI(r),r})(t,e,n);return Object.assign(n,t),n},a$=new Map,aO=e=>{let t=a$.get(e);return t?Object.fromEntries(Object.entries(t.stores).map(([e,t])=>[e,t.getState()])):{}},aB=(e,t,n)=>{if(void 0===e)return{type:"untracked",connection:t.connect(n)};let r=a$.get(n.name);if(r)return{type:"tracked",store:e,...r};let o={connection:t.connect(n),stores:{}};return a$.set(n.name,o),{type:"tracked",store:e,...o}},aL=(e,t)=>{let n;try{n=JSON.parse(e)}catch(e){console.error("[zustand devtools middleware] Could not parse the received json",e)}void 0!==n&&t(n)},aH=[],aF=[],aV=(o?aR(o):aR)(((e,t={})=>(n,r,o)=>{let a;let{enabled:i,anonymousActionType:s,store:l,...c}=t;try{a=(null==i||i)&&window.__REDUX_DEVTOOLS_EXTENSION__}catch(e){}if(!a)return i&&console.warn("[zustand devtools middleware] Please install/enable Redux devtools extension"),e(n,r,o);let{connection:u,...d}=aB(l,a,c),h=!0;o.setState=(e,t,a)=>{let i=n(e,t);if(!h)return i;let d=void 0===a?{type:s||"anonymous"}:"string"==typeof a?{type:a}:a;return void 0===l?null==u||u.send(d,r()):null==u||u.send({...d,type:`${l}/${d.type}`},{...aO(c.name),[l]:o.getState()}),i};let f=(...e)=>{let t=h;h=!1,n(...e),h=t},m=e(o.setState,r,o);if("untracked"===d.type?null==u||u.init(m):(d.stores[d.store]=o,null==u||u.init(Object.fromEntries(Object.entries(d.stores).map(([e,t])=>[e,e===d.store?m:t.getState()])))),o.dispatchFromDevtools&&"function"==typeof o.dispatch){let e=!1,t=o.dispatch;o.dispatch=(...n)=>{"__setState"!==n[0].type||e||(console.warn('[zustand devtools middleware] "__setState" action type is reserved to set state from the devtools. Avoid using it.'),e=!0),t(...n)}}return u.subscribe(e=>{var t;switch(e.type){case"ACTION":if("string"!=typeof e.payload){console.error("[zustand devtools middleware] Unsupported action format");return}return aL(e.payload,e=>{if("__setState"===e.type){if(void 0===l){f(e.state);return}1!==Object.keys(e.state).length&&console.error(`
                    [zustand devtools middleware] Unsupported __setState action format. 
                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),
                    and value of this only key should be a state object. Example: { "type": "__setState", "state": { "abc123Store": { "foo": "bar" } } }
                    `);let t=e.state[l];if(null==t)return;JSON.stringify(o.getState())!==JSON.stringify(t)&&f(t);return}o.dispatchFromDevtools&&"function"==typeof o.dispatch&&o.dispatch(e)});case"DISPATCH":switch(e.payload.type){case"RESET":if(f(m),void 0===l)return null==u?void 0:u.init(o.getState());return null==u?void 0:u.init(aO(c.name));case"COMMIT":if(void 0===l){null==u||u.init(o.getState());break}return null==u?void 0:u.init(aO(c.name));case"ROLLBACK":return aL(e.state,e=>{if(void 0===l){f(e),null==u||u.init(o.getState());return}f(e[l]),null==u||u.init(aO(c.name))});case"JUMP_TO_STATE":case"JUMP_TO_ACTION":return aL(e.state,e=>{if(void 0===l){f(e);return}JSON.stringify(o.getState())!==JSON.stringify(e[l])&&f(e[l])});case"IMPORT_STATE":{let{nextLiftedState:n}=e.payload,r=null==(t=n.computedStates.slice(-1)[0])?void 0:t.state;if(!r)return;void 0===l?f(r):f(r[l]),null==u||u.send(null,n);break}case"PAUSE_RECORDING":return h=!h}return}}),m})((r=(e,t)=>({currentFlow:null,nodes:aH,edges:aF,isLoading:!1,selectedNodeId:null,selectedEdgeId:null,isDirty:!1,setCurrentFlow:t=>{e({currentFlow:t}),t&&e({nodes:t.nodes.map(e=>({id:e.id,type:e.type,position:e.position,data:{label:e.label,config:e.config,inputs:e.inputs,outputs:e.outputs,category:e.category,description:e.description,icon:e.icon,color:e.color}})),edges:t.connections.map(e=>({id:e.id,source:e.sourceNodeId,target:e.targetNodeId,sourceHandle:e.sourceHandle,targetHandle:e.targetHandle,type:"smoothstep"})),isDirty:!1})},setNodes:t=>e({nodes:t,isDirty:!0}),setEdges:t=>e({edges:t,isDirty:!0}),onNodesChange:n=>{e({nodes:r9(n,t().nodes),isDirty:!0})},onEdgesChange:n=>{e({edges:oe(n,t().edges),isDirty:!0})},onConnect:n=>{e({edges:n2({...n,id:`edge-${Date.now()}`,type:"smoothstep"},t().edges),isDirty:!0})},addNode:n=>{let r={id:`node-${Date.now()}`,type:n.type||"default",position:n.position||{x:100,y:100},data:n.data||{},...n};e({nodes:[...t().nodes,r],isDirty:!0})},updateNode:(n,r)=>{e({nodes:t().nodes.map(e=>e.id===n?{...e,...r}:e),isDirty:!0})},deleteNode:n=>{e({nodes:t().nodes.filter(e=>e.id!==n),edges:t().edges.filter(e=>e.source!==n&&e.target!==n),selectedNodeId:t().selectedNodeId===n?null:t().selectedNodeId,isDirty:!0})},duplicateNode:n=>{let r=t().nodes.find(e=>e.id===n);if(r){let n={...r,id:`node-${Date.now()}`,position:{x:r.position.x+50,y:r.position.y+50}};e({nodes:[...t().nodes,n],isDirty:!0})}},selectNode:t=>{e({selectedNodeId:t,selectedEdgeId:null})},selectEdge:t=>{e({selectedEdgeId:t,selectedNodeId:null})},setLoading:t=>e({isLoading:t}),setDirty:t=>e({isDirty:t}),resetFlow:()=>{e({currentFlow:null,nodes:[],edges:[],selectedNodeId:null,selectedEdgeId:null,isDirty:!1})}}),(e,t,n)=>{let o=n.subscribe;return n.subscribe=(e,t,r)=>{let a=e;if(t){let o=(null==r?void 0:r.equalityFn)||Object.is,i=e(n.getState());a=n=>{let r=e(n);if(!o(i,r)){let e=i;t(i=r,e)}},(null==r?void 0:r.fireImmediately)&&t(i,i)}return o(a)},r(e,t,n)}),{name:"flow-store"}));var aZ=n(3651),aY=n(5532),aq=n(2812),aX=n(3407),aK=n(7756),aW=n(3509);let aU=(0,aW.Z)("Wrench",[["path",{d:"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z",key:"cbrjhi"}]]),aG=(0,aW.Z)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]),aQ=(0,aW.Z)("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]]),aJ=(0,aW.Z)("Scissors",[["circle",{cx:"6",cy:"6",r:"3",key:"1lh9wr"}],["path",{d:"M8.12 8.12 12 12",key:"1alkpv"}],["path",{d:"M20 4 8.12 15.88",key:"xgtan2"}],["circle",{cx:"6",cy:"18",r:"3",key:"fqmcym"}],["path",{d:"M14.8 14.8 20 20",key:"ptml3r"}]]);var a0=n(3764);let a1=(0,aW.Z)("Link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]]),a2=(0,aW.Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),a3=(0,aW.Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);var a4=n(4669),a5=n(7107);let a6={start:aX.Z,llm:aK.Z,tool:aU,memory:aG,vectorStore:aG,documentLoader:aQ,textSplitter:aJ,embeddings:a0.Z,retriever:a0.Z,chain:a1,agent:aK.Z,outputParser:a2,promptTemplate:aQ,conditional:a3,loop:a3,webhook:a1,apiCall:a1,custom:a2,default:a2},a7={start:"bg-green-500",llm:"bg-blue-500",tool:"bg-orange-500",memory:"bg-purple-500",vectorStore:"bg-purple-500",documentLoader:"bg-pink-500",textSplitter:"bg-cyan-500",embeddings:"bg-lime-500",retriever:"bg-red-500",chain:"bg-gray-500",agent:"bg-orange-500",outputParser:"bg-teal-500",promptTemplate:"bg-violet-500",conditional:"bg-yellow-500",loop:"bg-yellow-500",webhook:"bg-indigo-500",apiCall:"bg-indigo-500",custom:"bg-gray-700",default:"bg-gray-500"},a8=(0,m.memo)(({data:e,selected:t,type:n="default"})=>{let r=a6[n]||a6.default,o=a7[n]||a7.default,a=e.inputs||[],i=e.outputs||[];return(0,f.jsxs)(aZ.Zb,{className:(0,a5.cn)("min-w-[200px] transition-all duration-200",t&&"ring-2 ring-blue-500 ring-offset-2","hover:shadow-md"),children:[a.map((e,t)=>f.jsx(rh,{type:"target",position:d.Left,id:e.id,style:{top:`${(t+1)/(a.length+1)*100}%`,background:"#6b7280"},className:"w-3 h-3 border-2 border-white"},e.id)),i.map((e,t)=>f.jsx(rh,{type:"source",position:d.Right,id:e.id,style:{top:`${(t+1)/(i.length+1)*100}%`,background:"#6b7280"},className:"w-3 h-3 border-2 border-white"},e.id)),f.jsx(aZ.Ol,{className:"pb-2",children:(0,f.jsxs)("div",{className:"flex items-center justify-between",children:[(0,f.jsxs)("div",{className:"flex items-center space-x-2",children:[f.jsx("div",{className:(0,a5.cn)("p-1.5 rounded text-white",o),children:f.jsx(r,{className:"h-4 w-4"})}),(0,f.jsxs)("div",{children:[f.jsx("h3",{className:"font-medium text-sm",children:e.label||"Node"}),e.category&&f.jsx(aY.C,{variant:"secondary",className:"text-xs mt-1",children:e.category})]})]}),f.jsx(aq.z,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0",children:f.jsx(a4.Z,{className:"h-3 w-3"})})]})}),(0,f.jsxs)(aZ.aY,{className:"pt-0 pb-3",children:[e.description&&f.jsx("p",{className:"text-xs text-gray-600 mb-2 line-clamp-2",children:e.description}),e.config&&Object.keys(e.config).length>0&&(0,f.jsxs)("div",{className:"space-y-1",children:[Object.entries(e.config).slice(0,2).map(([e,t])=>(0,f.jsxs)("div",{className:"flex justify-between text-xs",children:[(0,f.jsxs)("span",{className:"text-gray-500 capitalize",children:[e,":"]}),f.jsx("span",{className:"text-gray-700 truncate ml-2",children:"object"==typeof t?JSON.stringify(t):String(t)})]},e)),Object.keys(e.config).length>2&&(0,f.jsxs)("div",{className:"text-xs text-gray-500",children:["+",Object.keys(e.config).length-2," more"]})]}),(0,f.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mt-2 pt-2 border-t",children:[(0,f.jsxs)("span",{children:[a.length," inputs"]}),(0,f.jsxs)("span",{children:[i.length," outputs"]})]})]})]})});var a9=n(7637);let ie=(0,m.memo)(({id:e,sourceX:t,sourceY:n,targetX:r,targetY:o,sourcePosition:a,targetPosition:i,style:s={},data:l,selected:c})=>{let[u,d,h]=nH({sourceX:t,sourceY:n,sourcePosition:a,targetX:r,targetY:o,targetPosition:i});return(0,f.jsxs)(f.Fragment,{children:[f.jsx(nz,{path:u,style:{...s,strokeWidth:c?3:2,stroke:c?"#3B82F6":"#6B7280"}}),c&&f.jsx(aa,{children:f.jsx("div",{style:{position:"absolute",transform:`translate(-50%, -50%) translate(${d}px,${h}px)`,fontSize:12,pointerEvents:"all"},className:"nodrag nopan",children:f.jsx(aq.z,{variant:"destructive",size:"sm",className:"h-6 w-6 p-0 rounded-full",onClick:()=>{console.log("Edge clicked:",e)},children:f.jsx(a9.Z,{className:"h-3 w-3"})})})})]})}),it=(0,aW.Z)("ZoomIn",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"11",x2:"11",y1:"8",y2:"14",key:"1vmskp"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]),ir=(0,aW.Z)("ZoomOut",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]),io=(0,aW.Z)("Maximize",[["path",{d:"M8 3H5a2 2 0 0 0-2 2v3",key:"1dcmit"}],["path",{d:"M21 8V5a2 2 0 0 0-2-2h-3",key:"1e4gt3"}],["path",{d:"M3 16v3a2 2 0 0 0 2 2h3",key:"wsl5sc"}],["path",{d:"M16 21h3a2 2 0 0 0 2-2v-3",key:"18trek"}]]),ia=(0,aW.Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]),ii=(0,aW.Z)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]),is={default:a8,start:a8,llm:a8,tool:a8,memory:a8,vectorStore:a8,documentLoader:a8,textSplitter:a8,embeddings:a8,retriever:a8,chain:a8,agent:a8,outputParser:a8,promptTemplate:a8,conditional:a8,loop:a8,webhook:a8,apiCall:a8,custom:a8},il={default:ie,smoothstep:ie};function ic(){let e=(0,m.useRef)(null),{nodes:t,edges:n,onNodesChange:r,onEdgesChange:o,onConnect:a,selectNode:s,selectEdge:l}=aV(),{zoomIn:c,zoomOut:u,fitView:d,getViewport:h,setViewport:p}=rK(),g=(0,m.useCallback)((e,t)=>{s(t.id)},[s]),y=(0,m.useCallback)((e,t)=>{l(t.id)},[l]),x=(0,m.useCallback)(()=>{s(null),l(null)},[s,l]),v=(0,m.useCallback)(e=>{e.preventDefault(),e.dataTransfer.dropEffect="move"},[]),b=(0,m.useCallback)(t=>{t.preventDefault();let n=e.current?.getBoundingClientRect(),r=t.dataTransfer.getData("application/reactflow");void 0!==r&&r&&n&&console.log("Drop node:",{type:r,position:{x:t.clientX-n.left,y:t.clientY-n.top}})},[]),w=(0,m.useCallback)(()=>{let e="data:application/json;charset=utf-8,"+encodeURIComponent(JSON.stringify({nodes:t,edges:n,viewport:h()},null,2)),r=document.createElement("a");r.setAttribute("href",e),r.setAttribute("download","flow.json"),r.click()},[t,n,h]),N=(0,m.useCallback)(()=>{let e=document.createElement("input");e.type="file",e.accept=".json",e.onchange=e=>{let t=e.target.files?.[0];if(t){let e=new FileReader;e.onload=e=>{try{let t=JSON.parse(e.target?.result);console.log("Import flow:",t)}catch(e){console.error("Failed to import flow:",e)}},e.readAsText(t)}},e.click()},[]);return f.jsx("div",{className:"w-full h-full",ref:e,children:(0,f.jsxs)(ar,{nodes:t,edges:n,onNodesChange:r,onEdgesChange:o,onConnect:a,onNodeClick:g,onEdgeClick:y,onPaneClick:x,onDrop:b,onDragOver:v,nodeTypes:is,edgeTypes:il,connectionMode:i.Loose,fitView:!0,attributionPosition:"bottom-left",className:"bg-gray-50",children:[f.jsx(af,{color:"#e5e7eb",gap:20}),f.jsx(aN,{className:"bg-white border border-gray-200 rounded-lg shadow-sm",showInteractive:!1}),f.jsx(az,{className:"bg-white border border-gray-200 rounded-lg",nodeColor:e=>e.data?.color||"#3B82F6",maskColor:"rgba(0, 0, 0, 0.1)"}),(0,f.jsxs)(ni,{position:"top-right",className:"space-y-2",children:[(0,f.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg shadow-sm p-2 space-y-1",children:[(0,f.jsxs)(aq.z,{variant:"ghost",size:"sm",onClick:()=>c(),className:"w-full justify-start",children:[f.jsx(it,{className:"h-4 w-4 mr-2"}),"Zoom In"]}),(0,f.jsxs)(aq.z,{variant:"ghost",size:"sm",onClick:()=>u(),className:"w-full justify-start",children:[f.jsx(ir,{className:"h-4 w-4 mr-2"}),"Zoom Out"]}),(0,f.jsxs)(aq.z,{variant:"ghost",size:"sm",onClick:()=>d(),className:"w-full justify-start",children:[f.jsx(io,{className:"h-4 w-4 mr-2"}),"Fit View"]})]}),(0,f.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg shadow-sm p-2 space-y-1",children:[(0,f.jsxs)(aq.z,{variant:"ghost",size:"sm",onClick:w,className:"w-full justify-start",children:[f.jsx(ia,{className:"h-4 w-4 mr-2"}),"Export"]}),(0,f.jsxs)(aq.z,{variant:"ghost",size:"sm",onClick:N,className:"w-full justify-start",children:[f.jsx(ii,{className:"h-4 w-4 mr-2"}),"Import"]})]})]})]})})}function iu(){return f.jsx(o5,{children:f.jsx(ic,{})})}let id=(0,aW.Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]),ih=(0,aW.Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]]),im=(0,aW.Z)("Share",[["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["polyline",{points:"16 6 12 2 8 6",key:"m901s6"}],["line",{x1:"12",x2:"12",y1:"2",y2:"15",key:"1p0rca"}]]),ip=(0,aW.Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]),ig=(0,aW.Z)("PanelRight",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["line",{x1:"15",x2:"15",y1:"3",y2:"21",key:"1hpv9i"}]]);var iy=n(6600);function ix({flow:e,onToggleChat:t,onToggleProperties:n,showChat:r,showProperties:o}){let a=(0,p.useRouter)(),{isDirty:i,setDirty:s}=aV(),[l,c]=(0,m.useState)(!1),[u,d]=(0,m.useState)(!1),h=async()=>{c(!0);try{await new Promise(e=>setTimeout(e,1e3)),s(!1),iy.ZP.success("Flow saved successfully")}catch(e){iy.ZP.error("Failed to save flow")}finally{c(!1)}},g=async()=>{d(!0);try{await new Promise(e=>setTimeout(e,2e3)),iy.ZP.success("Flow test completed")}catch(e){iy.ZP.error("Flow test failed")}finally{d(!1)}};return(0,f.jsxs)("div",{className:"h-14 bg-white border-b border-gray-200 flex items-center justify-between px-4",children:[(0,f.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,f.jsxs)(aq.z,{variant:"ghost",size:"sm",onClick:()=>a.push("/flows"),children:[f.jsx(id,{className:"h-4 w-4 mr-2"}),"Back"]}),f.jsx("div",{className:"h-6 w-px bg-gray-300"}),f.jsx("div",{className:"flex items-center space-x-3",children:(0,f.jsxs)("div",{children:[f.jsx("h1",{className:"font-semibold text-gray-900",children:e.name}),(0,f.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-500",children:[(0,f.jsxs)("span",{children:["Flow ID: ",e.id]}),f.jsx(aY.C,{className:{active:"bg-green-100 text-green-800",draft:"bg-yellow-100 text-yellow-800",inactive:"bg-gray-100 text-gray-800"}[e.status],children:e.status}),i&&f.jsx(aY.C,{variant:"outline",className:"text-orange-600 border-orange-600",children:"Unsaved changes"})]})]})})]}),(0,f.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,f.jsxs)(aq.z,{variant:"outline",size:"sm",onClick:h,disabled:l||!i,children:[f.jsx(ih,{className:"h-4 w-4 mr-2"}),l?"Saving...":"Save"]}),(0,f.jsxs)(aq.z,{variant:"outline",size:"sm",onClick:g,disabled:u,children:[f.jsx(aX.Z,{className:"h-4 w-4 mr-2"}),u?"Testing...":"Test"]}),(0,f.jsxs)(aq.z,{variant:"outline",size:"sm",onClick:()=>{navigator.clipboard.writeText(window.location.href),iy.ZP.success("Flow URL copied to clipboard")},children:[f.jsx(im,{className:"h-4 w-4 mr-2"}),"Share"]})]}),(0,f.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,f.jsxs)(aq.z,{variant:r?"default":"outline",size:"sm",onClick:t,children:[f.jsx(ip,{className:"h-4 w-4 mr-2"}),"Chat"]}),(0,f.jsxs)(aq.z,{variant:o?"default":"outline",size:"sm",onClick:n,children:[f.jsx(ig,{className:"h-4 w-4 mr-2"}),"Properties"]}),f.jsx("div",{className:"h-6 w-px bg-gray-300"}),f.jsx(aq.z,{variant:"ghost",size:"sm",children:f.jsx(a2,{className:"h-4 w-4"})}),f.jsx("div",{className:"relative",children:f.jsx(aq.z,{variant:"ghost",size:"sm",children:f.jsx(a4.Z,{className:"h-4 w-4"})})})]})]})}var iv=n(5460),ib=n(6e3);let iw=(0,aW.Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);var iN=n(2371);let iS=[{id:"openai-gpt4",type:"llm",name:"OpenAI GPT-4",description:"OpenAI GPT-4 language model",category:"LLM",icon:aK.Z,color:"#3B82F6"},{id:"anthropic-claude",type:"llm",name:"Anthropic Claude",description:"Anthropic Claude language model",category:"LLM",icon:aK.Z,color:"#3B82F6"},{id:"google-palm",type:"llm",name:"Google PaLM",description:"Google PaLM language model",category:"LLM",icon:aK.Z,color:"#3B82F6"},{id:"conversation-memory",type:"memory",name:"Conversation Memory",description:"Store conversation history",category:"Memory",icon:aG,color:"#10B981"},{id:"buffer-memory",type:"memory",name:"Buffer Memory",description:"Simple buffer memory",category:"Memory",icon:aG,color:"#10B981"},{id:"web-search",type:"tool",name:"Web Search",description:"Search the web for information",category:"Tools",icon:aU,color:"#F59E0B"},{id:"calculator",type:"tool",name:"Calculator",description:"Perform mathematical calculations",category:"Tools",icon:aU,color:"#F59E0B"},{id:"pinecone",type:"vectorStore",name:"Pinecone",description:"Pinecone vector database",category:"Vector Stores",icon:aG,color:"#8B5CF6"},{id:"chroma",type:"vectorStore",name:"Chroma",description:"Chroma vector database",category:"Vector Stores",icon:aG,color:"#8B5CF6"},{id:"pdf-loader",type:"documentLoader",name:"PDF Loader",description:"Load PDF documents",category:"Document Loaders",icon:aQ,color:"#EC4899"},{id:"text-loader",type:"documentLoader",name:"Text Loader",description:"Load text documents",category:"Document Loaders",icon:aQ,color:"#EC4899"},{id:"recursive-splitter",type:"textSplitter",name:"Recursive Text Splitter",description:"Split text recursively",category:"Text Splitters",icon:aJ,color:"#06B6D4"},{id:"llm-chain",type:"chain",name:"LLM Chain",description:"Simple LLM chain",category:"Chains",icon:a1,color:"#6B7280"},{id:"retrieval-qa",type:"chain",name:"Retrieval QA",description:"Question answering with retrieval",category:"Chains",icon:a1,color:"#6B7280"}];function i_(){let[e,t]=(0,m.useState)(""),[n,r]=(0,m.useState)(new Set(["LLM","Memory","Tools"])),o=iS.filter(t=>t.name.toLowerCase().includes(e.toLowerCase())||t.description.toLowerCase().includes(e.toLowerCase())||t.category.toLowerCase().includes(e.toLowerCase())).reduce((e,t)=>(e[t.category]||(e[t.category]=[]),e[t.category].push(t),e),{}),a=e=>{let t=new Set(n);t.has(e)?t.delete(e):t.add(e),r(t)},i=(e,t)=>{e.dataTransfer.setData("application/reactflow",t),e.dataTransfer.effectAllowed="move"};return(0,f.jsxs)("div",{className:"w-80 bg-white border-r border-gray-200 flex flex-col",children:[(0,f.jsxs)("div",{className:"p-4 border-b border-gray-200",children:[f.jsx("h2",{className:"font-semibold text-gray-900 mb-3",children:"Node Library"}),(0,f.jsxs)("div",{className:"relative",children:[f.jsx(a0.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),f.jsx(iv.I,{placeholder:"Search nodes...",value:e,onChange:e=>t(e.target.value),className:"pl-10"})]})]}),(0,f.jsxs)("div",{className:"flex-1 overflow-y-auto p-4 space-y-4",children:[Object.entries(o).map(([e,t])=>(0,f.jsxs)("div",{children:[f.jsx(aq.z,{variant:"ghost",className:"w-full justify-between p-2 h-auto",onClick:()=>a(e),children:(0,f.jsxs)("div",{className:"flex items-center space-x-2",children:[n.has(e)?f.jsx(ib.Z,{className:"h-4 w-4"}):f.jsx(iw,{className:"h-4 w-4"}),f.jsx("span",{className:"font-medium",children:e}),f.jsx(aY.C,{variant:"secondary",className:"text-xs",children:t.length})]})}),n.has(e)&&f.jsx("div",{className:"mt-2 space-y-2",children:t.map(e=>{let t=e.icon;return f.jsx(aZ.Zb,{className:"cursor-grab hover:shadow-md transition-shadow",draggable:!0,onDragStart:t=>i(t,e.type),children:f.jsx(aZ.aY,{className:"p-3",children:(0,f.jsxs)("div",{className:"flex items-start space-x-3",children:[f.jsx("div",{className:"p-2 rounded text-white flex-shrink-0",style:{backgroundColor:e.color},children:f.jsx(t,{className:"h-4 w-4"})}),(0,f.jsxs)("div",{className:"flex-1 min-w-0",children:[f.jsx("h4",{className:"font-medium text-sm text-gray-900 truncate",children:e.name}),f.jsx("p",{className:"text-xs text-gray-600 mt-1 line-clamp-2",children:e.description})]})]})})},e.id)})})]},e)),0===Object.keys(o).length&&(0,f.jsxs)("div",{className:"text-center py-8",children:[f.jsx(a0.Z,{className:"h-8 w-8 text-gray-400 mx-auto mb-2"}),f.jsx("p",{className:"text-gray-600 text-sm",children:"No nodes found"}),f.jsx("p",{className:"text-gray-500 text-xs",children:"Try adjusting your search"})]})]}),f.jsx("div",{className:"p-4 border-t border-gray-200",children:(0,f.jsxs)(aq.z,{variant:"outline",className:"w-full",size:"sm",children:[f.jsx(iN.Z,{className:"h-4 w-4 mr-2"}),"Create Custom Node"]})})]})}var iE=n(4578),ij=n(1936),ik=n(6464),iC=n(6295),iM=n(8687),iA=n(5674),iz=n(6958),iI=n(5353),iP=n(4582),iT=n(1285),iD=n(3485),iR=n(3175),i$="rovingFocusGroup.onEntryFocus",iO={bubbles:!1,cancelable:!0},iB="RovingFocusGroup",[iL,iH,iF]=(0,iA.B)(iB),[iV,iZ]=(0,iM.b)(iB,[iF]),[iY,iq]=iV(iB),iX=m.forwardRef((e,t)=>(0,f.jsx)(iL.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,f.jsx)(iL.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,f.jsx)(iK,{...e,ref:t})})}));iX.displayName=iB;var iK=m.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:r,loop:o=!1,dir:a,currentTabStopId:i,defaultCurrentTabStopId:s,onCurrentTabStopIdChange:l,onEntryFocus:c,preventScrollOnEntryFocus:u=!1,...d}=e,h=m.useRef(null),p=(0,iz.e)(t,h),g=(0,iR.gm)(a),[y,x]=(0,iD.T)({prop:i,defaultProp:s??null,onChange:l,caller:iB}),[v,b]=m.useState(!1),w=(0,iT.W)(c),N=iH(n),S=m.useRef(!1),[_,E]=m.useState(0);return m.useEffect(()=>{let e=h.current;if(e)return e.addEventListener(i$,w),()=>e.removeEventListener(i$,w)},[w]),(0,f.jsx)(iY,{scope:n,orientation:r,dir:g,loop:o,currentTabStopId:y,onItemFocus:m.useCallback(e=>x(e),[x]),onItemShiftTab:m.useCallback(()=>b(!0),[]),onFocusableItemAdd:m.useCallback(()=>E(e=>e+1),[]),onFocusableItemRemove:m.useCallback(()=>E(e=>e-1),[]),children:(0,f.jsx)(iP.WV.div,{tabIndex:v||0===_?-1:0,"data-orientation":r,...d,ref:p,style:{outline:"none",...e.style},onMouseDown:(0,iC.M)(e.onMouseDown,()=>{S.current=!0}),onFocus:(0,iC.M)(e.onFocus,e=>{let t=!S.current;if(e.target===e.currentTarget&&t&&!v){let t=new CustomEvent(i$,iO);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=N().filter(e=>e.focusable);iQ([e.find(e=>e.active),e.find(e=>e.id===y),...e].filter(Boolean).map(e=>e.ref.current),u)}}S.current=!1}),onBlur:(0,iC.M)(e.onBlur,()=>b(!1))})})}),iW="RovingFocusGroupItem",iU=m.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:r=!0,active:o=!1,tabStopId:a,children:i,...s}=e,l=(0,iI.M)(),c=a||l,u=iq(iW,n),d=u.currentTabStopId===c,h=iH(n),{onFocusableItemAdd:p,onFocusableItemRemove:g,currentTabStopId:y}=u;return m.useEffect(()=>{if(r)return p(),()=>g()},[r,p,g]),(0,f.jsx)(iL.ItemSlot,{scope:n,id:c,focusable:r,active:o,children:(0,f.jsx)(iP.WV.span,{tabIndex:d?0:-1,"data-orientation":u.orientation,...s,ref:t,onMouseDown:(0,iC.M)(e.onMouseDown,e=>{r?u.onItemFocus(c):e.preventDefault()}),onFocus:(0,iC.M)(e.onFocus,()=>u.onItemFocus(c)),onKeyDown:(0,iC.M)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){u.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return iG[o]}(e,u.orientation,u.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=h().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=u.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>iQ(n))}}),children:"function"==typeof i?i({isCurrentTabStop:d,hasTabStop:null!=y}):i})})});iU.displayName=iW;var iG={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function iQ(e,t=!1){let n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var iJ=n(9830),i0="Tabs",[i1,i2]=(0,iM.b)(i0,[iZ]),i3=iZ(),[i4,i5]=i1(i0),i6=m.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,onValueChange:o,defaultValue:a,orientation:i="horizontal",dir:s,activationMode:l="automatic",...c}=e,u=(0,iR.gm)(s),[d,h]=(0,iD.T)({prop:r,onChange:o,defaultProp:a??"",caller:i0});return(0,f.jsx)(i4,{scope:n,baseId:(0,iI.M)(),value:d,onValueChange:h,orientation:i,dir:u,activationMode:l,children:(0,f.jsx)(iP.WV.div,{dir:u,"data-orientation":i,...c,ref:t})})});i6.displayName=i0;var i7="TabsList",i8=m.forwardRef((e,t)=>{let{__scopeTabs:n,loop:r=!0,...o}=e,a=i5(i7,n),i=i3(n);return(0,f.jsx)(iX,{asChild:!0,...i,orientation:a.orientation,dir:a.dir,loop:r,children:(0,f.jsx)(iP.WV.div,{role:"tablist","aria-orientation":a.orientation,...o,ref:t})})});i8.displayName=i7;var i9="TabsTrigger",se=m.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,disabled:o=!1,...a}=e,i=i5(i9,n),s=i3(n),l=sr(i.baseId,r),c=so(i.baseId,r),u=r===i.value;return(0,f.jsx)(iU,{asChild:!0,...s,focusable:!o,active:u,children:(0,f.jsx)(iP.WV.button,{type:"button",role:"tab","aria-selected":u,"aria-controls":c,"data-state":u?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:l,...a,ref:t,onMouseDown:(0,iC.M)(e.onMouseDown,e=>{o||0!==e.button||!1!==e.ctrlKey?e.preventDefault():i.onValueChange(r)}),onKeyDown:(0,iC.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&i.onValueChange(r)}),onFocus:(0,iC.M)(e.onFocus,()=>{let e="manual"!==i.activationMode;u||o||!e||i.onValueChange(r)})})})});se.displayName=i9;var st="TabsContent",sn=m.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,forceMount:o,children:a,...i}=e,s=i5(st,n),l=sr(s.baseId,r),c=so(s.baseId,r),u=r===s.value,d=m.useRef(u);return m.useEffect(()=>{let e=requestAnimationFrame(()=>d.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,f.jsx)(iJ.z,{present:o||u,children:({present:n})=>(0,f.jsx)(iP.WV.div,{"data-state":u?"active":"inactive","data-orientation":s.orientation,role:"tabpanel","aria-labelledby":l,hidden:!n,id:c,tabIndex:0,...i,ref:t,style:{...e.style,animationDuration:d.current?"0s":void 0},children:n&&a})})});function sr(e,t){return`${e}-trigger-${t}`}function so(e,t){return`${e}-content-${t}`}sn.displayName=st;let sa=m.forwardRef(({className:e,...t},n)=>f.jsx(i8,{ref:n,className:(0,a5.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));sa.displayName=i8.displayName;let si=m.forwardRef(({className:e,...t},n)=>f.jsx(se,{ref:n,className:(0,a5.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));si.displayName=se.displayName;let ss=m.forwardRef(({className:e,...t},n)=>f.jsx(sn,{ref:n,className:(0,a5.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));ss.displayName=sn.displayName;var sl=n(5297);let sc=(0,aW.Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),su=(0,aW.Z)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]),sd=(0,aW.Z)("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]]);function sh(){let{selectedNodeId:e,selectedEdgeId:t,nodes:n,edges:r,updateNode:o}=aV(),[a,i]=(0,m.useState)({}),s=e?n.find(t=>t.id===e):null,l=t?r.find(e=>e.id===t):null,c=(e,t)=>{i(n=>({...n,config:{...n.config,[e]:t}}))};return s||l?l?(0,f.jsxs)("div",{className:"h-full flex flex-col",children:[f.jsx("div",{className:"p-4 border-b border-gray-200",children:f.jsx("h2",{className:"font-semibold text-gray-900",children:"Edge Properties"})}),f.jsx("div",{className:"flex-1 p-4",children:(0,f.jsxs)(aZ.Zb,{children:[f.jsx(aZ.Ol,{children:f.jsx(aZ.ll,{className:"text-sm",children:"Connection Details"})}),(0,f.jsxs)(aZ.aY,{className:"space-y-3",children:[(0,f.jsxs)("div",{children:[f.jsx(ij._,{className:"text-xs",children:"Source Node"}),f.jsx("p",{className:"text-sm text-gray-600",children:l.source})]}),(0,f.jsxs)("div",{children:[f.jsx(ij._,{className:"text-xs",children:"Target Node"}),f.jsx("p",{className:"text-sm text-gray-600",children:l.target})]}),(0,f.jsxs)("div",{children:[f.jsx(ij._,{className:"text-xs",children:"Source Handle"}),f.jsx("p",{className:"text-sm text-gray-600",children:l.sourceHandle})]}),(0,f.jsxs)("div",{children:[f.jsx(ij._,{className:"text-xs",children:"Target Handle"}),f.jsx("p",{className:"text-sm text-gray-600",children:l.targetHandle})]})]})]})})]}):(0,f.jsxs)("div",{className:"h-full flex flex-col",children:[(0,f.jsxs)("div",{className:"p-4 border-b border-gray-200",children:[(0,f.jsxs)("div",{className:"flex items-center justify-between",children:[f.jsx("h2",{className:"font-semibold text-gray-900",children:"Node Properties"}),(0,f.jsxs)("div",{className:"flex items-center space-x-1",children:[f.jsx(aq.z,{variant:"ghost",size:"sm",onClick:()=>{s&&console.log("Duplicate node:",s.id)},children:f.jsx(sl.Z,{className:"h-4 w-4"})}),f.jsx(aq.z,{variant:"ghost",size:"sm",onClick:()=>{s&&console.log("Delete node:",s.id)},children:f.jsx(sc,{className:"h-4 w-4"})})]})]}),s&&f.jsx("div",{className:"mt-2",children:f.jsx(aY.C,{variant:"secondary",className:"text-xs",children:s.type})})]}),f.jsx("div",{className:"flex-1 overflow-y-auto",children:(0,f.jsxs)(i6,{defaultValue:"general",className:"h-full",children:[(0,f.jsxs)(sa,{className:"grid w-full grid-cols-3 m-4",children:[(0,f.jsxs)(si,{value:"general",className:"text-xs",children:[f.jsx(su,{className:"h-3 w-3 mr-1"}),"General"]}),(0,f.jsxs)(si,{value:"config",className:"text-xs",children:[f.jsx(a2,{className:"h-3 w-3 mr-1"}),"Config"]}),(0,f.jsxs)(si,{value:"advanced",className:"text-xs",children:[f.jsx(sd,{className:"h-3 w-3 mr-1"}),"Advanced"]})]}),(0,f.jsxs)(ss,{value:"general",className:"p-4 space-y-4",children:[(0,f.jsxs)("div",{className:"space-y-2",children:[f.jsx(ij._,{htmlFor:"label",children:"Label"}),f.jsx(iv.I,{id:"label",value:a.label||"",onChange:e=>i(t=>({...t,label:e.target.value})),placeholder:"Node label"})]}),(0,f.jsxs)("div",{className:"space-y-2",children:[f.jsx(ij._,{htmlFor:"description",children:"Description"}),f.jsx(iE.g,{id:"description",value:a.description||"",onChange:e=>i(t=>({...t,description:e.target.value})),placeholder:"Node description",rows:3})]}),s&&(0,f.jsxs)(aZ.Zb,{children:[f.jsx(aZ.Ol,{children:f.jsx(aZ.ll,{className:"text-sm",children:"Node Information"})}),(0,f.jsxs)(aZ.aY,{className:"space-y-2 text-xs",children:[(0,f.jsxs)("div",{className:"flex justify-between",children:[f.jsx("span",{className:"text-gray-500",children:"ID:"}),f.jsx("span",{className:"font-mono",children:s.id})]}),(0,f.jsxs)("div",{className:"flex justify-between",children:[f.jsx("span",{className:"text-gray-500",children:"Type:"}),f.jsx("span",{children:s.type})]}),(0,f.jsxs)("div",{className:"flex justify-between",children:[f.jsx("span",{className:"text-gray-500",children:"Category:"}),f.jsx("span",{children:s.data?.category})]}),(0,f.jsxs)("div",{className:"flex justify-between",children:[f.jsx("span",{className:"text-gray-500",children:"Inputs:"}),f.jsx("span",{children:s.data?.inputs?.length||0})]}),(0,f.jsxs)("div",{className:"flex justify-between",children:[f.jsx("span",{className:"text-gray-500",children:"Outputs:"}),f.jsx("span",{children:s.data?.outputs?.length||0})]})]})]})]}),(0,f.jsxs)(ss,{value:"config",className:"p-4 space-y-4",children:[s?.type==="llm"&&f.jsx(sf,{config:a.config||{},onChange:c}),s?.type==="tool"&&f.jsx(sm,{config:a.config||{},onChange:c}),s?.type==="memory"&&f.jsx(sp,{config:a.config||{},onChange:c}),!["llm","tool","memory"].includes(s?.type||"")&&(0,f.jsxs)("div",{className:"space-y-4",children:[f.jsx("p",{className:"text-sm text-gray-600",children:"Configuration options for this node type are not yet implemented."}),f.jsx("pre",{className:"text-xs bg-gray-100 p-3 rounded",children:JSON.stringify(a.config,null,2)})]})]}),f.jsx(ss,{value:"advanced",className:"p-4 space-y-4",children:(0,f.jsxs)("div",{className:"space-y-2",children:[f.jsx(ij._,{children:"Raw Configuration"}),f.jsx(iE.g,{value:JSON.stringify(a.config,null,2),onChange:e=>{try{let t=JSON.parse(e.target.value);i(e=>({...e,config:t}))}catch(e){}},rows:10,className:"font-mono text-xs"})]})})]})}),f.jsx("div",{className:"p-4 border-t border-gray-200",children:f.jsx(aq.z,{onClick:()=>{s&&o(s.id,{data:{...s.data,label:a.label,description:a.description,config:a.config}})},className:"w-full",size:"sm",children:"Save Changes"})})]}):(0,f.jsxs)("div",{className:"h-full flex flex-col",children:[f.jsx("div",{className:"p-4 border-b border-gray-200",children:f.jsx("h2",{className:"font-semibold text-gray-900",children:"Properties"})}),f.jsx("div",{className:"flex-1 flex items-center justify-center p-4",children:(0,f.jsxs)("div",{className:"text-center",children:[f.jsx(a2,{className:"h-8 w-8 text-gray-400 mx-auto mb-2"}),f.jsx("p",{className:"text-gray-600 text-sm",children:"Select a node or edge"}),f.jsx("p",{className:"text-gray-500 text-xs",children:"to view its properties"})]})})]})}function sf({config:e,onChange:t}){return(0,f.jsxs)("div",{className:"space-y-4",children:[(0,f.jsxs)("div",{className:"space-y-2",children:[f.jsx(ij._,{children:"Model"}),(0,f.jsxs)(ik.Ph,{value:e.model||"gpt-4",onValueChange:e=>t("model",e),children:[f.jsx(ik.i4,{children:f.jsx(ik.ki,{})}),(0,f.jsxs)(ik.Bw,{children:[f.jsx(ik.Ql,{value:"gpt-4",children:"GPT-4"}),f.jsx(ik.Ql,{value:"gpt-3.5-turbo",children:"GPT-3.5 Turbo"}),f.jsx(ik.Ql,{value:"claude-3-opus",children:"Claude 3 Opus"}),f.jsx(ik.Ql,{value:"claude-3-sonnet",children:"Claude 3 Sonnet"})]})]})]}),(0,f.jsxs)("div",{className:"space-y-2",children:[f.jsx(ij._,{children:"Temperature"}),f.jsx(iv.I,{type:"number",min:"0",max:"2",step:"0.1",value:e.temperature||.7,onChange:e=>t("temperature",parseFloat(e.target.value))})]}),(0,f.jsxs)("div",{className:"space-y-2",children:[f.jsx(ij._,{children:"Max Tokens"}),f.jsx(iv.I,{type:"number",min:"1",max:"4000",value:e.maxTokens||1e3,onChange:e=>t("maxTokens",parseInt(e.target.value))})]}),(0,f.jsxs)("div",{className:"space-y-2",children:[f.jsx(ij._,{children:"System Message"}),f.jsx(iE.g,{value:e.systemMessage||"",onChange:e=>t("systemMessage",e.target.value),placeholder:"You are a helpful assistant...",rows:3})]})]})}function sm({config:e,onChange:t}){return(0,f.jsxs)("div",{className:"space-y-4",children:[(0,f.jsxs)("div",{className:"space-y-2",children:[f.jsx(ij._,{children:"Tool Type"}),(0,f.jsxs)(ik.Ph,{value:e.toolType||"web-search",onValueChange:e=>t("toolType",e),children:[f.jsx(ik.i4,{children:f.jsx(ik.ki,{})}),(0,f.jsxs)(ik.Bw,{children:[f.jsx(ik.Ql,{value:"web-search",children:"Web Search"}),f.jsx(ik.Ql,{value:"calculator",children:"Calculator"}),f.jsx(ik.Ql,{value:"api-call",children:"API Call"}),f.jsx(ik.Ql,{value:"custom",children:"Custom"})]})]})]}),(0,f.jsxs)("div",{className:"space-y-2",children:[f.jsx(ij._,{children:"API Endpoint"}),f.jsx(iv.I,{value:e.apiEndpoint||"",onChange:e=>t("apiEndpoint",e.target.value),placeholder:"https://api.example.com/search"})]}),(0,f.jsxs)("div",{className:"space-y-2",children:[f.jsx(ij._,{children:"API Key"}),f.jsx(iv.I,{type:"password",value:e.apiKey||"",onChange:e=>t("apiKey",e.target.value),placeholder:"Your API key"})]})]})}function sp({config:e,onChange:t}){return(0,f.jsxs)("div",{className:"space-y-4",children:[(0,f.jsxs)("div",{className:"space-y-2",children:[f.jsx(ij._,{children:"Memory Type"}),(0,f.jsxs)(ik.Ph,{value:e.memoryType||"conversation",onValueChange:e=>t("memoryType",e),children:[f.jsx(ik.i4,{children:f.jsx(ik.ki,{})}),(0,f.jsxs)(ik.Bw,{children:[f.jsx(ik.Ql,{value:"conversation",children:"Conversation Memory"}),f.jsx(ik.Ql,{value:"buffer",children:"Buffer Memory"}),f.jsx(ik.Ql,{value:"summary",children:"Summary Memory"})]})]})]}),(0,f.jsxs)("div",{className:"space-y-2",children:[f.jsx(ij._,{children:"Max Messages"}),f.jsx(iv.I,{type:"number",min:"1",max:"100",value:e.maxMessages||10,onChange:e=>t("maxMessages",parseInt(e.target.value))})]}),(0,f.jsxs)("div",{className:"space-y-2",children:[f.jsx(ij._,{children:"Return Messages"}),f.jsx(iv.I,{type:"number",min:"1",max:"20",value:e.returnMessages||5,onChange:e=>t("returnMessages",parseInt(e.target.value))})]})]})}let sg=(0,aW.Z)("Square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]]),sy=(0,aW.Z)("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]]),sx=(0,aW.Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);function sv({flowId:e}){let[t,n]=(0,m.useState)([{id:"1",role:"system",content:"Chat session started. You can now test your flow by sending messages.",timestamp:new Date}]),[r,o]=(0,m.useState)(""),[a,i]=(0,m.useState)(!1),[s,l]=(0,m.useState)(""),c=(0,m.useRef)(null),u=async()=>{if(!r.trim()||a)return;let e={id:`msg-${Date.now()}`,role:"user",content:r.trim(),timestamp:new Date};n(t=>[...t,e]),o(""),i(!0);try{await new Promise(e=>setTimeout(e,2e3));let t={id:`msg-${Date.now()}-response`,role:"assistant",content:`This is a mock response to: "${e.content}". In a real implementation, this would be the output from your flow execution.`,timestamp:new Date};n(e=>[...e,t])}catch(t){let e={id:`msg-${Date.now()}-error`,role:"assistant",content:"Sorry, there was an error processing your message. Please try again.",timestamp:new Date};n(t=>[...t,e])}finally{i(!1)}};return(0,f.jsxs)("div",{className:"h-full flex flex-col bg-white",children:[(0,f.jsxs)("div",{className:"p-4 border-b border-gray-200",children:[(0,f.jsxs)("div",{className:"flex items-center justify-between",children:[(0,f.jsxs)("div",{className:"flex items-center space-x-2",children:[f.jsx(ip,{className:"h-5 w-5 text-blue-600"}),f.jsx("h2",{className:"font-semibold text-gray-900",children:"Test Chat"})]}),(0,f.jsxs)("div",{className:"flex items-center space-x-1",children:[f.jsx(aq.z,{variant:"ghost",size:"sm",onClick:()=>{let n="data:application/json;charset=utf-8,"+encodeURIComponent(JSON.stringify({flowId:e,sessionId:s,messages:t,exportedAt:new Date().toISOString()},null,2)),r=`chat-${s}.json`,o=document.createElement("a");o.setAttribute("href",n),o.setAttribute("download",r),o.click()},children:f.jsx(ia,{className:"h-4 w-4"})}),f.jsx(aq.z,{variant:"ghost",size:"sm",onClick:()=>{n([{id:"1",role:"system",content:"Chat session cleared. You can start a new conversation.",timestamp:new Date}]),l(`session-${Date.now()}`)},children:f.jsx(sc,{className:"h-4 w-4"})}),f.jsx(aq.z,{variant:"ghost",size:"sm",children:f.jsx(a2,{className:"h-4 w-4"})})]})]}),(0,f.jsxs)("div",{className:"mt-2 flex items-center space-x-2",children:[(0,f.jsxs)(aY.C,{variant:"secondary",className:"text-xs",children:["Session: ",s.slice(-8)]}),(0,f.jsxs)(aY.C,{variant:"outline",className:"text-xs",children:[t.filter(e=>"system"!==e.role).length," messages"]})]})]}),(0,f.jsxs)("div",{className:"flex-1 overflow-y-auto p-4 space-y-4",children:[t.map(e=>f.jsx(sb,{message:e},e.id)),a&&(0,f.jsxs)("div",{className:"flex items-center space-x-2 text-gray-500",children:[f.jsx(aK.Z,{className:"h-4 w-4"}),(0,f.jsxs)("div",{className:"flex space-x-1",children:[f.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce"}),f.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),f.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})]}),f.jsx("div",{ref:c})]}),(0,f.jsxs)("div",{className:"p-4 border-t border-gray-200",children:[(0,f.jsxs)("div",{className:"flex space-x-2",children:[f.jsx(iv.I,{value:r,onChange:e=>o(e.target.value),onKeyPress:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),u())},placeholder:"Type your message...",disabled:a,className:"flex-1"}),f.jsx(aq.z,{onClick:u,disabled:!r.trim()||a,size:"sm",children:a?f.jsx(sg,{className:"h-4 w-4"}):f.jsx(sy,{className:"h-4 w-4"})})]}),f.jsx("p",{className:"text-xs text-gray-500 mt-2",children:"Press Enter to send, Shift+Enter for new line"})]})]})}function sb({message:e}){let t="user"===e.role;return"system"===e.role?f.jsx("div",{className:"flex justify-center",children:f.jsx("div",{className:"bg-gray-100 text-gray-600 text-xs px-3 py-1 rounded-full",children:e.content})}):f.jsx("div",{className:(0,a5.cn)("flex",t?"justify-end":"justify-start"),children:(0,f.jsxs)("div",{className:(0,a5.cn)("flex space-x-2 max-w-[80%]",t&&"flex-row-reverse space-x-reverse"),children:[f.jsx("div",{className:(0,a5.cn)("w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0",t?"bg-blue-600 text-white":"bg-gray-200 text-gray-600"),children:t?f.jsx(sx,{className:"h-4 w-4"}):f.jsx(aK.Z,{className:"h-4 w-4"})}),(0,f.jsxs)("div",{className:(0,a5.cn)("rounded-lg px-3 py-2 text-sm",t?"bg-blue-600 text-white":"bg-gray-100 text-gray-900"),children:[f.jsx("p",{className:"whitespace-pre-wrap",children:e.content}),f.jsx("p",{className:(0,a5.cn)("text-xs mt-1 opacity-70",t?"text-blue-100":"text-gray-500"),children:e.timestamp.toLocaleTimeString()})]})]})})}function sw(){let e=(0,p.useParams)().id,{setCurrentFlow:t,currentFlow:n,isLoading:r,setLoading:o}=aV(),[a,i]=(0,m.useState)(!1),[s,l]=(0,m.useState)(!0);return r?f.jsx("div",{className:"h-screen flex items-center justify-center",children:(0,f.jsxs)("div",{className:"text-center",children:[f.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"}),f.jsx("p",{className:"text-gray-600",children:"Loading flow..."})]})}):n?(0,f.jsxs)("div",{className:"h-screen flex flex-col bg-gray-50",children:[f.jsx(ix,{flow:n,onToggleChat:()=>i(!a),onToggleProperties:()=>l(!s),showChat:a,showProperties:s}),(0,f.jsxs)("div",{className:"flex-1 flex overflow-hidden",children:[f.jsx(i_,{}),f.jsx("div",{className:"flex-1 relative",children:f.jsx(iu,{})}),s&&f.jsx("div",{className:"w-80 border-l bg-white",children:f.jsx(sh,{})}),a&&f.jsx("div",{className:"w-96 border-l bg-white",children:f.jsx(sv,{flowId:e})})]})]}):f.jsx("div",{className:"h-screen flex items-center justify-center",children:(0,f.jsxs)("div",{className:"text-center",children:[f.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Flow not found"}),f.jsx("p",{className:"text-gray-600",children:"The requested flow could not be loaded."})]})})}new Date().toISOString(),new Date().toISOString(),new Date().toISOString(),new Date().toISOString(),new Date().toISOString(),new Date().toISOString()},5682:(e,t,n)=>{"use strict";n.d(t,{Providers:()=>c});var r=n(7491),o=n(843),a=n(5668),i=n(675),s=n(2781),l=n(6364);function c({children:e}){let[t]=(0,l.useState)(()=>new o.S({defaultOptions:{queries:{staleTime:6e4,retry:1}}}));return(0,r.jsxs)(a.aH,{client:t,children:[r.jsx(s.f,{attribute:"class",defaultTheme:"light",enableSystem:!0,disableTransitionOnChange:!0,children:e}),r.jsx(i.t,{initialIsOpen:!1})]})}},5532:(e,t,n)=>{"use strict";n.d(t,{C:()=>s});var r=n(7491);n(6364);var o=n(4295),a=n(7107);let i=(0,o.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function s({className:e,variant:t,...n}){return r.jsx("div",{className:(0,a.cn)(i({variant:t}),e),...n})}},2812:(e,t,n)=>{"use strict";n.d(t,{z:()=>c});var r=n(7491),o=n(6364),a=n(7723),i=n(4295),s=n(7107);let l=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=o.forwardRef(({className:e,variant:t,size:n,asChild:o=!1,...i},c)=>{let u=o?a.g7:"button";return r.jsx(u,{className:(0,s.cn)(l({variant:t,size:n,className:e})),ref:c,...i})});c.displayName="Button"},3651:(e,t,n)=>{"use strict";n.d(t,{Ol:()=>s,SZ:()=>c,Zb:()=>i,aY:()=>u,ll:()=>l});var r=n(7491),o=n(6364),a=n(7107);let i=o.forwardRef(({className:e,...t},n)=>r.jsx("div",{ref:n,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));i.displayName="Card";let s=o.forwardRef(({className:e,...t},n)=>r.jsx("div",{ref:n,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",e),...t}));s.displayName="CardHeader";let l=o.forwardRef(({className:e,...t},n)=>r.jsx("h3",{ref:n,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));l.displayName="CardTitle";let c=o.forwardRef(({className:e,...t},n)=>r.jsx("p",{ref:n,className:(0,a.cn)("text-sm text-muted-foreground",e),...t}));c.displayName="CardDescription";let u=o.forwardRef(({className:e,...t},n)=>r.jsx("div",{ref:n,className:(0,a.cn)("p-6 pt-0",e),...t}));u.displayName="CardContent",o.forwardRef(({className:e,...t},n)=>r.jsx("div",{ref:n,className:(0,a.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},5460:(e,t,n)=>{"use strict";n.d(t,{I:()=>i});var r=n(7491),o=n(6364),a=n(7107);let i=o.forwardRef(({className:e,type:t,...n},o)=>r.jsx("input",{type:t,className:(0,a.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:o,...n}));i.displayName="Input"},1936:(e,t,n)=>{"use strict";n.d(t,{_:()=>c});var r=n(7491),o=n(6364),a=n(2245),i=n(4295),s=n(7107);let l=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=o.forwardRef(({className:e,...t},n)=>r.jsx(a.f,{ref:n,className:(0,s.cn)(l(),e),...t}));c.displayName=a.f.displayName},6464:(e,t,n)=>{"use strict";n.d(t,{Bw:()=>p,Ph:()=>u,Ql:()=>g,i4:()=>h,ki:()=>d});var r=n(7491),o=n(6364),a=n(7670),i=n(6e3),s=n(5990),l=n(6532),c=n(7107);let u=a.fC;a.ZA;let d=a.B4,h=o.forwardRef(({className:e,children:t,...n},o)=>(0,r.jsxs)(a.xz,{ref:o,className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...n,children:[t,r.jsx(a.JO,{asChild:!0,children:r.jsx(i.Z,{className:"h-4 w-4 opacity-50"})})]}));h.displayName=a.xz.displayName;let f=o.forwardRef(({className:e,...t},n)=>r.jsx(a.u_,{ref:n,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:r.jsx(s.Z,{className:"h-4 w-4"})}));f.displayName=a.u_.displayName;let m=o.forwardRef(({className:e,...t},n)=>r.jsx(a.$G,{ref:n,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:r.jsx(i.Z,{className:"h-4 w-4"})}));m.displayName=a.$G.displayName;let p=o.forwardRef(({className:e,children:t,position:n="popper",...o},i)=>r.jsx(a.h_,{children:(0,r.jsxs)(a.VY,{ref:i,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:n,...o,children:[r.jsx(f,{}),r.jsx(a.l_,{className:(0,c.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),r.jsx(m,{})]})}));p.displayName=a.VY.displayName,o.forwardRef(({className:e,...t},n)=>r.jsx(a.__,{ref:n,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=a.__.displayName;let g=o.forwardRef(({className:e,children:t,...n},o)=>(0,r.jsxs)(a.ck,{ref:o,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...n,children:[r.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:r.jsx(a.wU,{children:r.jsx(l.Z,{className:"h-4 w-4"})})}),r.jsx(a.eT,{children:t})]}));g.displayName=a.ck.displayName,o.forwardRef(({className:e,...t},n)=>r.jsx(a.Z0,{ref:n,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=a.Z0.displayName},4578:(e,t,n)=>{"use strict";n.d(t,{g:()=>i});var r=n(7491),o=n(6364),a=n(7107);let i=o.forwardRef(({className:e,...t},n)=>r.jsx("textarea",{className:(0,a.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:n,...t}));i.displayName="Textarea"},7107:(e,t,n)=>{"use strict";n.d(t,{Ox:()=>s,cn:()=>a,p6:()=>i});var r=n(7672),o=n(2154);function a(...e){return(0,o.m6)((0,r.W)(e))}function i(e){return new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})}function s(){return Math.random().toString(36).substr(2,9)}},2828:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>r});let r=(0,n(4798).createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\agent_framework\packages\ui\src\app\flows\[id]\page.tsx#default`)},2367:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>c,metadata:()=>l});var r=n(4519),o=n(1283),a=n.n(o);n(1369);let i=(0,n(4798).createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\agent_framework\packages\ui\src\components\providers.tsx#Providers`);var s=n(6533);let l={title:"Flowwise Clone - Visual AI Agent Builder",description:"Build AI agents, chatbots, and multi-agent systems visually through a drag-and-drop interface.",keywords:["AI","chatbot","agent","visual builder","no-code","LLM"],authors:[{name:"Flowwise Clone Team"}],viewport:"width=device-width, initial-scale=1",themeColor:"#3B82F6"};function c({children:e}){return r.jsx("html",{lang:"en",suppressHydrationWarning:!0,children:r.jsx("body",{className:a().className,children:(0,r.jsxs)(i,{children:[e,r.jsx(s.x7,{position:"top-right",toastOptions:{duration:4e3,style:{background:"hsl(var(--card))",color:"hsl(var(--card-foreground))",border:"1px solid hsl(var(--border))"}}})]})})})}},5854:()=>{},1369:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[903,763,864],()=>n(1143));module.exports=r})();