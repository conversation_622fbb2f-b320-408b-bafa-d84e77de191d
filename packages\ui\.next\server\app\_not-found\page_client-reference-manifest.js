globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/_not-found/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"1021":{"*":{"id":"3143","name":"*","chunks":[],"async":false}},"1739":{"*":{"id":"543","name":"*","chunks":[],"async":false}},"2597":{"*":{"id":"9501","name":"*","chunks":[],"async":false}},"2608":{"*":{"id":"2247","name":"*","chunks":[],"async":false}},"3160":{"*":{"id":"8958","name":"*","chunks":[],"async":false}},"3415":{"*":{"id":"9822","name":"*","chunks":[],"async":false}},"3849":{"*":{"id":"8611","name":"*","chunks":[],"async":false}},"6625":{"*":{"id":"8789","name":"*","chunks":[],"async":false}},"6864":{"*":{"id":"3627","name":"*","chunks":[],"async":false}},"7474":{"*":{"id":"6600","name":"*","chunks":[],"async":false}},"9338":{"*":{"id":"5682","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Documents\\augment-projects\\agent_framework\\node_modules\\.pnpm\\next@14.2.30_react-dom@18.3.1_react@18.3.1\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":1021,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\agent_framework\\node_modules\\.pnpm\\next@14.2.30_react-dom@18.3.1_react@18.3.1\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":1021,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\agent_framework\\node_modules\\.pnpm\\next@14.2.30_react-dom@18.3.1_react@18.3.1\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":6864,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\agent_framework\\node_modules\\.pnpm\\next@14.2.30_react-dom@18.3.1_react@18.3.1\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":6864,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\agent_framework\\node_modules\\.pnpm\\next@14.2.30_react-dom@18.3.1_react@18.3.1\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":3160,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\agent_framework\\node_modules\\.pnpm\\next@14.2.30_react-dom@18.3.1_react@18.3.1\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":3160,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\agent_framework\\node_modules\\.pnpm\\next@14.2.30_react-dom@18.3.1_react@18.3.1\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":2597,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\agent_framework\\node_modules\\.pnpm\\next@14.2.30_react-dom@18.3.1_react@18.3.1\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":2597,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\agent_framework\\node_modules\\.pnpm\\next@14.2.30_react-dom@18.3.1_react@18.3.1\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":1739,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\agent_framework\\node_modules\\.pnpm\\next@14.2.30_react-dom@18.3.1_react@18.3.1\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":1739,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\agent_framework\\node_modules\\.pnpm\\next@14.2.30_react-dom@18.3.1_react@18.3.1\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":6625,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\agent_framework\\node_modules\\.pnpm\\next@14.2.30_react-dom@18.3.1_react@18.3.1\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":6625,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\agent_framework\\node_modules\\.pnpm\\next@14.2.30_react-dom@18.3.1_react@18.3.1\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":4794,"name":"*","chunks":["474","static/chunks/474-708aedf4037e986a.js","669","static/chunks/669-47d3f52a25381196.js","185","static/chunks/app/layout-f615272dcaac44b4.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\agent_framework\\node_modules\\.pnpm\\react-hot-toast@2.5.2_react-dom@18.3.1_react@18.3.1\\node_modules\\react-hot-toast\\dist\\index.mjs":{"id":7474,"name":"*","chunks":["474","static/chunks/474-708aedf4037e986a.js","669","static/chunks/669-47d3f52a25381196.js","185","static/chunks/app/layout-f615272dcaac44b4.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\agent_framework\\packages\\ui\\src\\app\\globals.css":{"id":6030,"name":"*","chunks":["474","static/chunks/474-708aedf4037e986a.js","669","static/chunks/669-47d3f52a25381196.js","185","static/chunks/app/layout-f615272dcaac44b4.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\agent_framework\\packages\\ui\\src\\components\\providers.tsx":{"id":9338,"name":"*","chunks":["474","static/chunks/474-708aedf4037e986a.js","669","static/chunks/669-47d3f52a25381196.js","185","static/chunks/app/layout-f615272dcaac44b4.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\agent_framework\\packages\\ui\\src\\app\\flows\\[id]\\page.tsx":{"id":2608,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\agent_framework\\packages\\ui\\src\\app\\flows\\page.tsx":{"id":3849,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\agent_framework\\packages\\ui\\src\\app\\page.tsx":{"id":3415,"name":"*","chunks":["720","static/chunks/720-ba51961659a0ef1c.js","765","static/chunks/765-6e4790645cef5550.js","931","static/chunks/app/page-63b9499e69539f6e.js"],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Documents\\augment-projects\\agent_framework\\packages\\ui\\src\\":[],"C:\\Users\\<USER>\\Documents\\augment-projects\\agent_framework\\packages\\ui\\src\\app\\layout":["static/css/41558598ad3e1cbb.css"],"C:\\Users\\<USER>\\Documents\\augment-projects\\agent_framework\\packages\\ui\\src\\app\\page":[],"C:\\Users\\<USER>\\Documents\\augment-projects\\agent_framework\\packages\\ui\\src\\app\\_not-found\\page":[]}}