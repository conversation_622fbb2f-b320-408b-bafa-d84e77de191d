{"version": 3, "sources": ["../../src/ReactQueryDevtoolsPanel.tsx"], "sourcesContent": ["'use client'\nimport * as React from 'react'\nimport { onlineManager, useQueryClient } from '@tanstack/react-query'\nimport { TanstackQueryDevtoolsPanel } from '@tanstack/query-devtools'\nimport type { DevtoolsErrorType } from '@tanstack/query-devtools'\nimport type { QueryClient } from '@tanstack/react-query'\n\nexport interface DevtoolsPanelOptions {\n  /**\n   * Custom instance of QueryClient\n   */\n  client?: QueryClient\n  /**\n   * Use this so you can define custom errors that can be shown in the devtools.\n   */\n  errorTypes?: Array<DevtoolsErrorType>\n  /**\n   * Use this to pass a nonce to the style tag that is added to the document head. This is useful if you are using a Content Security Policy (CSP) nonce to allow inline styles.\n   */\n  styleNonce?: string\n  /**\n   * Use this so you can attach the devtool's styles to specific element in the DOM.\n   */\n  shadowDOMTarget?: ShadowRoot\n\n  /**\n   * Custom styles for the devtools panel\n   * @default { height: '500px' }\n   * @example { height: '100%' }\n   * @example { height: '100%', width: '100%' }\n   */\n  style?: React.CSSProperties\n\n  /**\n   * Callback function that is called when the devtools panel is closed\n   */\n  onClose?: () => unknown\n}\n\nexport function ReactQueryDevtoolsPanel(\n  props: DevtoolsPanelOptions,\n): React.ReactElement | null {\n  const queryClient = useQueryClient(props.client)\n  const ref = React.useRef<HTMLDivElement>(null)\n  const { errorTypes, styleNonce, shadowDOMTarget } = props\n  const [devtools] = React.useState(\n    new TanstackQueryDevtoolsPanel({\n      client: queryClient,\n      queryFlavor: 'React Query',\n      version: '5',\n      onlineManager,\n      buttonPosition: 'bottom-left',\n      position: 'bottom',\n      initialIsOpen: true,\n      errorTypes,\n      styleNonce,\n      shadowDOMTarget,\n      onClose: props.onClose,\n    }),\n  )\n\n  React.useEffect(() => {\n    devtools.setClient(queryClient)\n  }, [queryClient, devtools])\n\n  React.useEffect(() => {\n    devtools.setOnClose(props.onClose ?? (() => {}))\n  }, [props.onClose, devtools])\n\n  React.useEffect(() => {\n    devtools.setErrorTypes(errorTypes || [])\n  }, [errorTypes, devtools])\n\n  React.useEffect(() => {\n    if (ref.current) {\n      devtools.mount(ref.current)\n    }\n\n    return () => {\n      devtools.unmount()\n    }\n  }, [devtools])\n\n  return (\n    <div\n      style={{ height: '500px', ...props.style }}\n      className=\"tsqd-parent-container\"\n      ref={ref}\n    ></div>\n  )\n}\n"], "mappings": ";;;AACA,YAAY,WAAW;AACvB,SAAS,eAAe,sBAAsB;AAC9C,SAAS,kCAAkC;AAiFvC;AA7CG,SAAS,wBACd,OAC2B;AAC3B,QAAM,cAAc,eAAe,MAAM,MAAM;AAC/C,QAAM,MAAY,aAAuB,IAAI;AAC7C,QAAM,EAAE,YAAY,YAAY,gBAAgB,IAAI;AACpD,QAAM,CAAC,QAAQ,IAAU;AAAA,IACvB,IAAI,2BAA2B;AAAA,MAC7B,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,SAAS;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,eAAe;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA,SAAS,MAAM;AAAA,IACjB,CAAC;AAAA,EACH;AAEA,EAAM,gBAAU,MAAM;AACpB,aAAS,UAAU,WAAW;AAAA,EAChC,GAAG,CAAC,aAAa,QAAQ,CAAC;AAE1B,EAAM,gBAAU,MAAM;AACpB,aAAS,WAAW,MAAM,YAAY,MAAM;AAAA,IAAC,EAAE;AAAA,EACjD,GAAG,CAAC,MAAM,SAAS,QAAQ,CAAC;AAE5B,EAAM,gBAAU,MAAM;AACpB,aAAS,cAAc,cAAc,CAAC,CAAC;AAAA,EACzC,GAAG,CAAC,YAAY,QAAQ,CAAC;AAEzB,EAAM,gBAAU,MAAM;AACpB,QAAI,IAAI,SAAS;AACf,eAAS,MAAM,IAAI,OAAO;AAAA,IAC5B;AAEA,WAAO,MAAM;AACX,eAAS,QAAQ;AAAA,IACnB;AAAA,EACF,GAAG,CAAC,QAAQ,CAAC;AAEb,SACE;AAAA,IAAC;AAAA;AAAA,MACC,OAAO,EAAE,QAAQ,SAAS,GAAG,MAAM,MAAM;AAAA,MACzC,WAAU;AAAA,MACV;AAAA;AAAA,EACD;AAEL;", "names": []}