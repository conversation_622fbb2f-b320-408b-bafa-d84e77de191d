{"name": "@flowwise-clone/server", "version": "1.0.0", "description": "Backend API server for Flowwise Clone", "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "clean": "<PERSON><PERSON><PERSON> dist", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts", "test": "jest"}, "dependencies": {"@flowwise-clone/shared": "workspace:*", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "socket.io": "^4.7.4", "prisma": "^5.7.1", "@prisma/client": "^5.7.1", "zod": "^3.22.4", "openai": "^4.20.1", "@anthropic-ai/sdk": "^0.9.1", "@google-ai/generativelanguage": "^2.5.0", "langchain": "^0.0.212", "pinecone-client": "^1.1.0", "chromadb": "^1.7.3", "pdf-parse": "^1.1.1", "mammoth": "^1.6.0", "csv-parser": "^3.0.0", "cheerio": "^1.0.0-rc.12", "axios": "^1.6.2", "ws": "^8.14.2"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/compression": "^1.7.5", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/node": "^20.10.0", "@types/ws": "^8.5.10", "tsx": "^4.6.2", "typescript": "^5.3.0", "rimraf": "^5.0.5", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "jest": "^29.7.0", "@types/jest": "^29.5.8"}}