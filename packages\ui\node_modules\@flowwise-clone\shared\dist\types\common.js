"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseEntitySchema = exports.PositionSchema = exports.ExecutionStatus = exports.FlowStatus = exports.DataType = exports.ConnectionType = exports.NodeType = exports.TimestampSchema = exports.IdSchema = void 0;
const zod_1 = require("zod");
// Base schemas
exports.IdSchema = zod_1.z.string().uuid();
exports.TimestampSchema = zod_1.z.string().datetime();
// Common enums
var NodeType;
(function (NodeType) {
    NodeType["START"] = "start";
    NodeType["LLM"] = "llm";
    NodeType["TOOL"] = "tool";
    NodeType["MEMORY"] = "memory";
    NodeType["VECTOR_STORE"] = "vectorStore";
    NodeType["DOCUMENT_LOADER"] = "documentLoader";
    NodeType["TEXT_SPLITTER"] = "textSplitter";
    NodeType["EMBEDDINGS"] = "embeddings";
    NodeType["RETRIEVER"] = "retriever";
    NodeType["CHAIN"] = "chain";
    NodeType["AGENT"] = "agent";
    NodeType["OUTPUT_PARSER"] = "outputParser";
    NodeType["PROMPT_TEMPLATE"] = "promptTemplate";
    NodeType["CONDITIONAL"] = "conditional";
    NodeType["LOOP"] = "loop";
    NodeType["WEBHOOK"] = "webhook";
    NodeType["API_CALL"] = "apiCall";
    NodeType["CUSTOM"] = "custom";
})(NodeType || (exports.NodeType = NodeType = {}));
var ConnectionType;
(function (ConnectionType) {
    ConnectionType["INPUT"] = "input";
    ConnectionType["OUTPUT"] = "output";
})(ConnectionType || (exports.ConnectionType = ConnectionType = {}));
var DataType;
(function (DataType) {
    DataType["STRING"] = "string";
    DataType["NUMBER"] = "number";
    DataType["BOOLEAN"] = "boolean";
    DataType["OBJECT"] = "object";
    DataType["ARRAY"] = "array";
    DataType["LLM"] = "llm";
    DataType["MEMORY"] = "memory";
    DataType["VECTOR_STORE"] = "vectorStore";
    DataType["DOCUMENT"] = "document";
    DataType["TOOL"] = "tool";
    DataType["AGENT"] = "agent";
})(DataType || (exports.DataType = DataType = {}));
var FlowStatus;
(function (FlowStatus) {
    FlowStatus["DRAFT"] = "draft";
    FlowStatus["ACTIVE"] = "active";
    FlowStatus["INACTIVE"] = "inactive";
    FlowStatus["ARCHIVED"] = "archived";
})(FlowStatus || (exports.FlowStatus = FlowStatus = {}));
var ExecutionStatus;
(function (ExecutionStatus) {
    ExecutionStatus["PENDING"] = "pending";
    ExecutionStatus["RUNNING"] = "running";
    ExecutionStatus["COMPLETED"] = "completed";
    ExecutionStatus["FAILED"] = "failed";
    ExecutionStatus["CANCELLED"] = "cancelled";
})(ExecutionStatus || (exports.ExecutionStatus = ExecutionStatus = {}));
// Position schema for node positioning
exports.PositionSchema = zod_1.z.object({
    x: zod_1.z.number(),
    y: zod_1.z.number()
});
// Base entity schema
exports.BaseEntitySchema = zod_1.z.object({
    id: exports.IdSchema,
    createdAt: exports.TimestampSchema,
    updatedAt: exports.TimestampSchema
});
//# sourceMappingURL=common.js.map