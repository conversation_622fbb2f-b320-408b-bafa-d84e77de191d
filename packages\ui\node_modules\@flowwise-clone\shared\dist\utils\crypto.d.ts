/**
 * Generates a random UUID v4
 */
export declare function generateUUID(): string;
/**
 * Generates a random session ID
 */
export declare function generateSessionId(): string;
/**
 * Generates a random API key
 */
export declare function generateApiKey(): string;
/**
 * Hashes a string using SHA-256
 */
export declare function hashString(input: string): string;
/**
 * Generates a random string of specified length
 */
export declare function generateRandomString(length: number): string;
//# sourceMappingURL=crypto.d.ts.map