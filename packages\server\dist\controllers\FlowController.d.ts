import { Request, Response } from 'express';
export declare class FlowController {
    private flowService;
    constructor();
    getFlows: (req: Request, res: Response) => Promise<void>;
    getFlow: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
    createFlow: (req: Request, res: Response) => Promise<void>;
    updateFlow: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
    deleteFlow: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
    executeFlow: (req: Request, res: Response) => Promise<void>;
    validateFlow: (req: Request, res: Response) => Promise<void>;
    duplicateFlow: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
}
//# sourceMappingURL=FlowController.d.ts.map