import { Request, Response } from 'express';
export declare class FlowController {
    private flowService;
    constructor();
    getFlows: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
    getFlow: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
    createFlow: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
    updateFlow: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
    deleteFlow: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
    executeFlow: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
    validateFlow: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
    duplicateFlow: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
}
//# sourceMappingURL=FlowController.d.ts.map