import { Request, Response, NextFunction } from 'express';

// Extend Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        role: string;
      };
    }
  }
}

export function authMiddleware(req: Request, res: Response, next: NextFunction) {
  // TODO: Implement proper JWT authentication
  // For now, just add a mock user
  req.user = {
    id: 'user-1',
    email: '<EMAIL>',
    role: 'user',
  };
  
  next();
}
