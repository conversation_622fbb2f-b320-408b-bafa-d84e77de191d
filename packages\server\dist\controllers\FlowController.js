"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FlowController = void 0;
const FlowService_1 = require("../services/FlowService");
class FlowController {
    constructor() {
        this.getFlows = async (req, res) => {
            try {
                const userId = req.user?.id;
                const flows = await this.flowService.getFlowsByUser(userId);
                res.json(flows);
            }
            catch (error) {
                res.status(500).json({ error: 'Failed to get flows' });
            }
        };
        this.getFlow = async (req, res) => {
            try {
                const { id } = req.params;
                const userId = req.user?.id;
                const flow = await this.flowService.getFlow(id, userId);
                if (!flow) {
                    return res.status(404).json({ error: 'Flow not found' });
                }
                res.json(flow);
            }
            catch (error) {
                res.status(500).json({ error: 'Failed to get flow' });
            }
        };
        this.createFlow = async (req, res) => {
            try {
                const userId = req.user?.id;
                const flowData = req.body;
                const flow = await this.flowService.createFlow({ ...flowData, userId });
                res.status(201).json(flow);
            }
            catch (error) {
                res.status(500).json({ error: 'Failed to create flow' });
            }
        };
        this.updateFlow = async (req, res) => {
            try {
                const { id } = req.params;
                const userId = req.user?.id;
                const updates = req.body;
                const flow = await this.flowService.updateFlow(id, userId, updates);
                if (!flow) {
                    return res.status(404).json({ error: 'Flow not found' });
                }
                res.json(flow);
            }
            catch (error) {
                res.status(500).json({ error: 'Failed to update flow' });
            }
        };
        this.deleteFlow = async (req, res) => {
            try {
                const { id } = req.params;
                const userId = req.user?.id;
                const success = await this.flowService.deleteFlow(id, userId);
                if (!success) {
                    return res.status(404).json({ error: 'Flow not found' });
                }
                res.status(204).send();
            }
            catch (error) {
                res.status(500).json({ error: 'Failed to delete flow' });
            }
        };
        this.executeFlow = async (req, res) => {
            try {
                const { id } = req.params;
                const { input, sessionId } = req.body;
                const userId = req.user?.id;
                const result = await this.flowService.executeFlow(id, userId, input, sessionId);
                res.json(result);
            }
            catch (error) {
                res.status(500).json({ error: 'Failed to execute flow' });
            }
        };
        this.validateFlow = async (req, res) => {
            try {
                const { id } = req.params;
                const userId = req.user?.id;
                const validation = await this.flowService.validateFlow(id, userId);
                res.json(validation);
            }
            catch (error) {
                res.status(500).json({ error: 'Failed to validate flow' });
            }
        };
        this.duplicateFlow = async (req, res) => {
            try {
                const { id } = req.params;
                const userId = req.user?.id;
                const { name } = req.body;
                const duplicatedFlow = await this.flowService.duplicateFlow(id, userId, name);
                if (!duplicatedFlow) {
                    return res.status(404).json({ error: 'Flow not found' });
                }
                res.status(201).json(duplicatedFlow);
            }
            catch (error) {
                res.status(500).json({ error: 'Failed to duplicate flow' });
            }
        };
        this.flowService = new FlowService_1.FlowService();
    }
}
exports.FlowController = FlowController;
//# sourceMappingURL=FlowController.js.map