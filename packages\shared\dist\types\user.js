"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiKeySchema = exports.WorkspaceMemberSchema = exports.WorkspaceSchema = exports.UserSchema = exports.UserRole = void 0;
const zod_1 = require("zod");
const common_1 = require("./common");
// User role enum
var UserRole;
(function (UserRole) {
    UserRole["ADMIN"] = "admin";
    UserRole["USER"] = "user";
    UserRole["VIEWER"] = "viewer";
})(UserRole || (exports.UserRole = UserRole = {}));
// User schema
exports.UserSchema = common_1.BaseEntitySchema.extend({
    email: zod_1.z.string().email(),
    name: zod_1.z.string(),
    avatar: zod_1.z.string().optional(),
    role: zod_1.z.nativeEnum(UserRole),
    isActive: zod_1.z.boolean().default(true),
    lastLoginAt: zod_1.z.string().datetime().optional(),
    preferences: zod_1.z.record(zod_1.z.any()).optional()
});
// Workspace schema
exports.WorkspaceSchema = common_1.BaseEntitySchema.extend({
    name: zod_1.z.string(),
    description: zod_1.z.string().optional(),
    ownerId: zod_1.z.string().uuid(),
    isActive: zod_1.z.boolean().default(true),
    settings: zod_1.z.record(zod_1.z.any()).optional()
});
// Workspace member schema
exports.WorkspaceMemberSchema = common_1.BaseEntitySchema.extend({
    workspaceId: zod_1.z.string().uuid(),
    userId: zod_1.z.string().uuid(),
    role: zod_1.z.nativeEnum(UserRole),
    joinedAt: zod_1.z.string().datetime()
});
// API key schema
exports.ApiKeySchema = common_1.BaseEntitySchema.extend({
    name: zod_1.z.string(),
    key: zod_1.z.string(),
    userId: zod_1.z.string().uuid(),
    workspaceId: zod_1.z.string().uuid().optional(),
    isActive: zod_1.z.boolean().default(true),
    expiresAt: zod_1.z.string().datetime().optional(),
    lastUsedAt: zod_1.z.string().datetime().optional(),
    permissions: zod_1.z.array(zod_1.z.string()).default([])
});
//# sourceMappingURL=user.js.map