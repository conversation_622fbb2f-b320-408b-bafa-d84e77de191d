(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[931],{8292:function(e,s,t){Promise.resolve().then(t.bind(t,3415))},3389:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(5069).Z)("Bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]])},3415:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return j}});var r=t(8980),l=t(9211),n=t(9838),i=t(3815),a=t(3389),c=t(5069);let d=(0,c.Z)("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]]),o=(0,c.Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]),x=(0,c.Z)("Workflow",[["rect",{width:"8",height:"8",x:"3",y:"3",rx:"2",key:"by2w9f"}],["path",{d:"M7 11v4a2 2 0 0 0 2 2h4",key:"xkn7yn"}],["rect",{width:"8",height:"8",x:"13",y:"13",rx:"2",key:"1cgmvn"}]]),h=(0,c.Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),u=(0,c.Z)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]),m=(0,c.Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]);var f=t(9014);function j(){let e=(0,l.useRouter)();return(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50",children:[(0,r.jsx)("header",{className:"border-b bg-white/80 backdrop-blur-sm",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 py-4 flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(a.Z,{className:"h-8 w-8 text-blue-600"}),(0,r.jsx)("span",{className:"text-xl font-bold",children:"Flowwise Clone"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(n.z,{variant:"ghost",asChild:!0,children:(0,r.jsx)(f.default,{href:"/docs",children:"Documentation"})}),(0,r.jsx)(n.z,{variant:"ghost",asChild:!0,children:(0,r.jsxs)(f.default,{href:"https://github.com/your-repo/flowwise-clone",target:"_blank",children:[(0,r.jsx)(d,{className:"h-4 w-4 mr-2"}),"GitHub"]})}),(0,r.jsx)(n.z,{asChild:!0,children:(0,r.jsx)(f.default,{href:"/flows",children:"Get Started"})})]})]})}),(0,r.jsx)("section",{className:"container mx-auto px-4 py-20 text-center",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,r.jsxs)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:["Build AI Agents,"," ",(0,r.jsx)("span",{className:"text-blue-600",children:"Visually"})]}),(0,r.jsx)("p",{className:"text-xl text-gray-600 mb-8 max-w-2xl mx-auto",children:"Create powerful AI agents, chatbots, and multi-agent systems through an intuitive drag-and-drop interface. No coding required."}),(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-4",children:[(0,r.jsxs)(n.z,{size:"lg",onClick:()=>e.push("/flows"),children:["Start Building",(0,r.jsx)(o,{className:"ml-2 h-4 w-4"})]}),(0,r.jsx)(n.z,{variant:"outline",size:"lg",asChild:!0,children:(0,r.jsx)(f.default,{href:"/templates",children:"View Templates"})})]})]})}),(0,r.jsxs)("section",{className:"container mx-auto px-4 py-20",children:[(0,r.jsxs)("div",{className:"text-center mb-16",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Everything you need to build AI agents"}),(0,r.jsx)("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"From simple chatbots to complex multi-agent systems, our platform provides all the tools you need."})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[(0,r.jsxs)(i.Zb,{children:[(0,r.jsxs)(i.Ol,{children:[(0,r.jsx)(x,{className:"h-8 w-8 text-blue-600 mb-2"}),(0,r.jsx)(i.ll,{children:"Visual Flow Builder"}),(0,r.jsx)(i.SZ,{children:"Drag-and-drop interface for building complex AI workflows without code"})]}),(0,r.jsx)(i.aY,{children:(0,r.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[(0,r.jsx)("li",{children:"• Node-based editor"}),(0,r.jsx)("li",{children:"• Real-time validation"}),(0,r.jsx)("li",{children:"• Template library"})]})})]}),(0,r.jsxs)(i.Zb,{children:[(0,r.jsxs)(i.Ol,{children:[(0,r.jsx)(h,{className:"h-8 w-8 text-green-600 mb-2"}),(0,r.jsx)(i.ll,{children:"Multi-Agent Systems"}),(0,r.jsx)(i.SZ,{children:"Coordinate multiple AI agents for complex task orchestration"})]}),(0,r.jsx)(i.aY,{children:(0,r.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[(0,r.jsx)("li",{children:"• Agent coordination"}),(0,r.jsx)("li",{children:"• Task distribution"}),(0,r.jsx)("li",{children:"• Workflow management"})]})})]}),(0,r.jsxs)(i.Zb,{children:[(0,r.jsxs)(i.Ol,{children:[(0,r.jsx)(a.Z,{className:"h-8 w-8 text-purple-600 mb-2"}),(0,r.jsx)(i.ll,{children:"LLM Integrations"}),(0,r.jsx)(i.SZ,{children:"Connect to OpenAI, Anthropic, Google, and other LLM providers"})]}),(0,r.jsx)(i.aY,{children:(0,r.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[(0,r.jsx)("li",{children:"• Multiple providers"}),(0,r.jsx)("li",{children:"• Streaming support"}),(0,r.jsx)("li",{children:"• Custom models"})]})})]}),(0,r.jsxs)(i.Zb,{children:[(0,r.jsxs)(i.Ol,{children:[(0,r.jsx)(u,{className:"h-8 w-8 text-yellow-600 mb-2"}),(0,r.jsx)(i.ll,{children:"RAG & Vector Search"}),(0,r.jsx)(i.SZ,{children:"Integrate with vector databases for knowledge retrieval"})]}),(0,r.jsx)(i.aY,{children:(0,r.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[(0,r.jsx)("li",{children:"• Document processing"}),(0,r.jsx)("li",{children:"• Vector embeddings"}),(0,r.jsx)("li",{children:"• Semantic search"})]})})]}),(0,r.jsxs)(i.Zb,{children:[(0,r.jsxs)(i.Ol,{children:[(0,r.jsx)(m,{className:"h-8 w-8 text-orange-600 mb-2"}),(0,r.jsx)(i.ll,{children:"Human-in-the-Loop"}),(0,r.jsx)(i.SZ,{children:"Add human approval workflows and manual intervention"})]}),(0,r.jsx)(i.aY,{children:(0,r.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[(0,r.jsx)("li",{children:"• Approval workflows"}),(0,r.jsx)("li",{children:"• Task review"}),(0,r.jsx)("li",{children:"• Manual override"})]})})]}),(0,r.jsxs)(i.Zb,{children:[(0,r.jsxs)(i.Ol,{children:[(0,r.jsx)(o,{className:"h-8 w-8 text-red-600 mb-2"}),(0,r.jsx)(i.ll,{children:"API & Integrations"}),(0,r.jsx)(i.SZ,{children:"REST APIs, SDKs, and embedded chat widgets"})]}),(0,r.jsx)(i.aY,{children:(0,r.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[(0,r.jsx)("li",{children:"• REST API"}),(0,r.jsx)("li",{children:"• TypeScript SDK"}),(0,r.jsx)("li",{children:"• Embedded widgets"})]})})]})]})]}),(0,r.jsx)("section",{className:"bg-blue-600 text-white py-20",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 text-center",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold mb-4",children:"Ready to build your first AI agent?"}),(0,r.jsx)("p",{className:"text-xl mb-8 opacity-90",children:"Join thousands of developers building the future of AI"}),(0,r.jsxs)(n.z,{size:"lg",variant:"secondary",onClick:()=>e.push("/flows"),children:["Get Started for Free",(0,r.jsx)(o,{className:"ml-2 h-4 w-4"})]})]})}),(0,r.jsx)("footer",{className:"bg-gray-900 text-white py-12",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,r.jsx)(a.Z,{className:"h-6 w-6"}),(0,r.jsx)("span",{className:"text-lg font-bold",children:"Flowwise Clone"})]}),(0,r.jsx)("p",{className:"text-gray-400",children:"Visual AI agent builder for the modern web"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold mb-4",children:"Product"}),(0,r.jsxs)("ul",{className:"space-y-2 text-gray-400",children:[(0,r.jsx)("li",{children:(0,r.jsx)(f.default,{href:"/features",children:"Features"})}),(0,r.jsx)("li",{children:(0,r.jsx)(f.default,{href:"/templates",children:"Templates"})}),(0,r.jsx)("li",{children:(0,r.jsx)(f.default,{href:"/pricing",children:"Pricing"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold mb-4",children:"Resources"}),(0,r.jsxs)("ul",{className:"space-y-2 text-gray-400",children:[(0,r.jsx)("li",{children:(0,r.jsx)(f.default,{href:"/docs",children:"Documentation"})}),(0,r.jsx)("li",{children:(0,r.jsx)(f.default,{href:"/examples",children:"Examples"})}),(0,r.jsx)("li",{children:(0,r.jsx)(f.default,{href:"/community",children:"Community"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold mb-4",children:"Company"}),(0,r.jsxs)("ul",{className:"space-y-2 text-gray-400",children:[(0,r.jsx)("li",{children:(0,r.jsx)(f.default,{href:"/about",children:"About"})}),(0,r.jsx)("li",{children:(0,r.jsx)(f.default,{href:"/contact",children:"Contact"})}),(0,r.jsx)("li",{children:(0,r.jsx)(f.default,{href:"/privacy",children:"Privacy"})})]})]})]}),(0,r.jsx)("div",{className:"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400",children:(0,r.jsx)("p",{children:"\xa9 2024 Flowwise Clone. All rights reserved."})})]})})]})}},9838:function(e,s,t){"use strict";t.d(s,{z:function(){return d}});var r=t(8980),l=t(2208),n=t(9382),i=t(3868),a=t(2424);let c=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=l.forwardRef((e,s)=>{let{className:t,variant:l,size:i,asChild:d=!1,...o}=e,x=d?n.g7:"button";return(0,r.jsx)(x,{className:(0,a.cn)(c({variant:l,size:i,className:t})),ref:s,...o})});d.displayName="Button"},3815:function(e,s,t){"use strict";t.d(s,{Ol:function(){return a},SZ:function(){return d},Zb:function(){return i},aY:function(){return o},ll:function(){return c}});var r=t(8980),l=t(2208),n=t(2424);let i=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,r.jsx)("div",{ref:s,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...l})});i.displayName="Card";let a=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,r.jsx)("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",t),...l})});a.displayName="CardHeader";let c=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,r.jsx)("h3",{ref:s,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",t),...l})});c.displayName="CardTitle";let d=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,r.jsx)("p",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",t),...l})});d.displayName="CardDescription";let o=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,r.jsx)("div",{ref:s,className:(0,n.cn)("p-6 pt-0",t),...l})});o.displayName="CardContent",l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,r.jsx)("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",t),...l})}).displayName="CardFooter"},2424:function(e,s,t){"use strict";t.d(s,{Ox:function(){return a},cn:function(){return n},p6:function(){return i}});var r=t(2240),l=t(3946);function n(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,l.m6)((0,r.W)(s))}function i(e){return new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})}function a(){return Math.random().toString(36).substr(2,9)}}},function(e){e.O(0,[720,765,14,512,744],function(){return e(e.s=8292)}),_N_E=e.O()}]);