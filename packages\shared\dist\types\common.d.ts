import { z } from 'zod';
export declare const IdSchema: z.ZodString;
export declare const TimestampSchema: z.ZodString;
export declare enum NodeType {
    START = "start",
    LLM = "llm",
    TOOL = "tool",
    MEMORY = "memory",
    VECTOR_STORE = "vectorStore",
    DOCUMENT_LOADER = "documentLoader",
    TEXT_SPLITTER = "textSplitter",
    EMBEDDINGS = "embeddings",
    RETRIEVER = "retriever",
    CHAIN = "chain",
    AGENT = "agent",
    OUTPUT_PARSER = "outputParser",
    PROMPT_TEMPLATE = "promptTemplate",
    CONDITIONAL = "conditional",
    LOOP = "loop",
    WEBHOOK = "webhook",
    API_CALL = "apiCall",
    CUSTOM = "custom"
}
export declare enum ConnectionType {
    INPUT = "input",
    OUTPUT = "output"
}
export declare enum DataType {
    STRING = "string",
    NUMBER = "number",
    BOOLEAN = "boolean",
    OBJECT = "object",
    ARRAY = "array",
    LLM = "llm",
    MEMORY = "memory",
    VECTOR_STORE = "vectorStore",
    DOCUMENT = "document",
    TOOL = "tool",
    AGENT = "agent"
}
export declare enum FlowStatus {
    DRAFT = "draft",
    ACTIVE = "active",
    INACTIVE = "inactive",
    ARCHIVED = "archived"
}
export declare enum ExecutionStatus {
    PENDING = "pending",
    RUNNING = "running",
    COMPLETED = "completed",
    FAILED = "failed",
    CANCELLED = "cancelled"
}
export declare const PositionSchema: z.ZodObject<{
    x: z.ZodNumber;
    y: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    x: number;
    y: number;
}, {
    x: number;
    y: number;
}>;
export type Position = z.infer<typeof PositionSchema>;
export declare const BaseEntitySchema: z.ZodObject<{
    id: z.ZodString;
    createdAt: z.ZodString;
    updatedAt: z.ZodString;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: string;
    updatedAt: string;
}, {
    id: string;
    createdAt: string;
    updatedAt: string;
}>;
export type BaseEntity = z.infer<typeof BaseEntitySchema>;
//# sourceMappingURL=common.d.ts.map