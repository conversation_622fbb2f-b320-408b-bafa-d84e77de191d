import { Router } from 'express';
import { FlowController } from '../controllers/FlowController';

const router = Router();
const flowController = new FlowController();

// GET /api/flows - Get all flows for user
router.get('/', flowController.getFlows);

// GET /api/flows/:id - Get specific flow
router.get('/:id', flowController.getFlow);

// POST /api/flows - Create new flow
router.post('/', flowController.createFlow);

// PUT /api/flows/:id - Update flow
router.put('/:id', flowController.updateFlow);

// DELETE /api/flows/:id - Delete flow
router.delete('/:id', flowController.deleteFlow);

// POST /api/flows/:id/execute - Execute flow
router.post('/:id/execute', flowController.executeFlow);

// POST /api/flows/:id/validate - Validate flow
router.post('/:id/validate', flowController.validateFlow);

// POST /api/flows/:id/duplicate - Duplicate flow
router.post('/:id/duplicate', flowController.duplicateFlow);

export default router;
