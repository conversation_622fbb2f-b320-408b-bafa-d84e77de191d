import { Flow, FlowExecution, FlowValidationResult } from '@flowwise-clone/shared';
export declare class FlowService {
    getFlowsByUser(userId: string): Promise<Flow[]>;
    getFlow(id: string, userId: string): Promise<Flow | null>;
    createFlow(flowData: Partial<Flow>): Promise<Flow>;
    updateFlow(id: string, userId: string, updates: Partial<Flow>): Promise<Flow | null>;
    deleteFlow(id: string, userId: string): Promise<boolean>;
    executeFlow(id: string, userId: string, input: any, sessionId?: string): Promise<FlowExecution>;
    validateFlow(id: string, userId: string): Promise<FlowValidationResult>;
    duplicateFlow(id: string, userId: string, name?: string): Promise<Flow | null>;
}
//# sourceMappingURL=FlowService.d.ts.map