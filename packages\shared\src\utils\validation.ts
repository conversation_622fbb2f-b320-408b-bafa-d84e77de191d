import { z } from 'zod';

/**
 * Validates data against a Zod schema and returns a result object
 */
export function validateData<T>(
  schema: z.ZodSchema<T>,
  data: unknown
): { success: true; data: T } | { success: false; errors: string[] } {
  try {
    const result = schema.parse(data);
    return { success: true, data: result };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors = error.errors.map(err => 
        `${err.path.join('.')}: ${err.message}`
      );
      return { success: false, errors };
    }
    return { success: false, errors: ['Unknown validation error'] };
  }
}

/**
 * Safely parses data with a Zod schema, returning null on error
 */
export function safeParseData<T>(
  schema: z.ZodSchema<T>,
  data: unknown
): T | null {
  try {
    return schema.parse(data);
  } catch {
    return null;
  }
}

/**
 * Validates an array of data against a schema
 */
export function validateArray<T>(
  schema: z.ZodSchema<T>,
  data: unknown[]
): { success: true; data: T[] } | { success: false; errors: string[] } {
  const arraySchema = z.array(schema);
  return validateData(arraySchema, data);
}
