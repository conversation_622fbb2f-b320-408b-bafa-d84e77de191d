"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[606],{4895:function(e,t,n){n.d(t,{Ry:function(){return c}});var r=new WeakMap,o=new WeakMap,i={},l=0,a=function(e){return e&&(e.host||a(e.parentNode))},u=function(e,t,n,u){var c=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=a(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});i[n]||(i[n]=new WeakMap);var s=i[n],d=[],f=new Set,p=new Set(c),v=function(e){!e||f.has(e)||(f.add(e),v(e.parentNode))};c.forEach(v);var h=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))h(e);else try{var t=e.getAttribute(u),i=null!==t&&"false"!==t,l=(r.get(e)||0)+1,a=(s.get(e)||0)+1;r.set(e,l),s.set(e,a),d.push(e),1===l&&i&&o.set(e,!0),1===a&&e.setAttribute(n,"true"),i||e.setAttribute(u,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return h(t),f.clear(),l++,function(){d.forEach(function(e){var t=r.get(e)-1,i=s.get(e)-1;r.set(e,t),s.set(e,i),t||(o.has(e)||e.removeAttribute(u),o.delete(e)),i||e.removeAttribute(n)}),--l||(r=new WeakMap,r=new WeakMap,o=new WeakMap,i={})}},c=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),u(r,o,n,"aria-hidden")):function(){return null}}},4362:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(5069).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5837:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(5069).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},6887:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(5069).Z)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},3744:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(5069).Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},9808:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(5069).Z)("MoreHorizontal",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},5207:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(5069).Z)("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]])},9008:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(5069).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},4507:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(5069).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},4164:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(5069).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},7942:function(e,t,n){n.d(t,{Z:function(){return q}});var r,o,i,l,a,u,c,s=function(){return(s=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function d(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}"function"==typeof SuppressedError&&SuppressedError;var f=n(2208),p="right-scroll-bar-position",v="width-before-scroll-bar";function h(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var m="undefined"!=typeof window?f.useLayoutEffect:f.useEffect,g=new WeakMap,y=(void 0===o&&(o={}),(void 0===i&&(i=function(e){return e}),l=[],a=!1,u={read:function(){if(a)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return l.length?l[l.length-1]:null},useMedium:function(e){var t=i(e,a);return l.push(t),function(){l=l.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(a=!0;l.length;){var t=l;l=[],t.forEach(e)}l={push:function(t){return e(t)},filter:function(){return l}}},assignMedium:function(e){a=!0;var t=[];if(l.length){var n=l;l=[],n.forEach(e),t=l}var r=function(){var n=t;t=[],n.forEach(e)},o=function(){return Promise.resolve().then(r)};o(),l={push:function(e){t.push(e),o()},filter:function(e){return t=t.filter(e),l}}}}).options=s({async:!0,ssr:!1},o),u),w=function(){},b=f.forwardRef(function(e,t){var n,r,o,i,l=f.useRef(null),a=f.useState({onScrollCapture:w,onWheelCapture:w,onTouchMoveCapture:w}),u=a[0],c=a[1],p=e.forwardProps,v=e.children,b=e.className,x=e.removeScrollBar,E=e.enabled,S=e.shards,C=e.sideCar,R=e.noRelative,M=e.noIsolation,T=e.inert,A=e.allowPinchZoom,k=e.as,P=e.gapMode,N=d(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),L=(n=[l,t],r=function(e){return n.forEach(function(t){return h(t,e)})},(o=(0,f.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,i=o.facade,m(function(){var e=g.get(i);if(e){var t=new Set(e),r=new Set(n),o=i.current;t.forEach(function(e){r.has(e)||h(e,null)}),r.forEach(function(e){t.has(e)||h(e,o)})}g.set(i,n)},[n]),i),O=s(s({},N),u);return f.createElement(f.Fragment,null,E&&f.createElement(C,{sideCar:y,removeScrollBar:x,shards:S,noRelative:R,noIsolation:M,inert:T,setCallbacks:c,allowPinchZoom:!!A,lockRef:l,gapMode:P}),p?f.cloneElement(f.Children.only(v),s(s({},O),{ref:L})):f.createElement(void 0===k?"div":k,s({},O,{className:b,ref:L}),v))});b.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},b.classNames={fullWidth:v,zeroRight:p};var x=function(e){var t=e.sideCar,n=d(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return f.createElement(r,s({},n))};x.isSideCarExport=!0;var E=function(){var e=0,t=null;return{add:function(o){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=r||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,l;(i=t).styleSheet?i.styleSheet.cssText=o:i.appendChild(document.createTextNode(o)),l=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(l)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},S=function(){var e=E();return function(t,n){f.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},C=function(){var e=S();return function(t){return e(t.styles,t.dynamic),null}},R={left:0,top:0,right:0,gap:0},M=function(e){return parseInt(e||"",10)||0},T=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[M(n),M(r),M(o)]},A=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return R;var t=T(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},k=C(),P="data-scroll-locked",N=function(e,t,n,r){var o=e.left,i=e.top,l=e.right,a=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(a,"px ").concat(r,";\n  }\n  body[").concat(P,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(l,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(a,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(p," {\n    right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(v," {\n    margin-right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(p," .").concat(p," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(v," .").concat(v," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(P,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(a,"px;\n  }\n")},L=function(){var e=parseInt(document.body.getAttribute(P)||"0",10);return isFinite(e)?e:0},O=function(){f.useEffect(function(){return document.body.setAttribute(P,(L()+1).toString()),function(){var e=L()-1;e<=0?document.body.removeAttribute(P):document.body.setAttribute(P,e.toString())}},[])},j=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;O();var i=f.useMemo(function(){return A(o)},[o]);return f.createElement(k,{styles:N(i,!t,o,n?"":"!important")})},D=!1;if("undefined"!=typeof window)try{var W=Object.defineProperty({},"passive",{get:function(){return D=!0,!0}});window.addEventListener("test",W,W),window.removeEventListener("test",W,W)}catch(e){D=!1}var I=!!D&&{passive:!1},F=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===n[t])},V=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),H(e,r)){var o=_(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},H=function(e,t){return"v"===e?F(t,"overflowY"):F(t,"overflowX")},_=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},B=function(e,t,n,r,o){var i,l=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),a=l*r,u=n.target,c=t.contains(u),s=!1,d=a>0,f=0,p=0;do{if(!u)break;var v=_(e,u),h=v[0],m=v[1]-v[2]-l*h;(h||m)&&H(e,u)&&(f+=m,p+=h);var g=u.parentNode;u=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&a>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-a>p)&&(s=!0),s},z=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Z=function(e){return[e.deltaX,e.deltaY]},U=function(e){return e&&"current"in e?e.current:e},K=0,X=[],$=(c=function(e){var t=f.useRef([]),n=f.useRef([0,0]),r=f.useRef(),o=f.useState(K++)[0],i=f.useState(C)[0],l=f.useRef(e);f.useEffect(function(){l.current=e},[e]),f.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(U),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var a=f.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!l.current.allowPinchZoom;var o,i=z(e),a=n.current,u="deltaX"in e?e.deltaX:a[0]-i[0],c="deltaY"in e?e.deltaY:a[1]-i[1],s=e.target,d=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=V(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=V(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||c)&&(r.current=o),!o)return!0;var p=r.current||o;return B(p,t,e,"h"===p?u:c,!0)},[]),u=f.useCallback(function(e){if(X.length&&X[X.length-1]===i){var n="deltaY"in e?Z(e):z(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(l.current.shards||[]).map(U).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?a(e,o[0]):!l.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=f.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),s=f.useCallback(function(e){n.current=z(e),r.current=void 0},[]),d=f.useCallback(function(t){c(t.type,Z(t),t.target,a(t,e.lockRef.current))},[]),p=f.useCallback(function(t){c(t.type,z(t),t.target,a(t,e.lockRef.current))},[]);f.useEffect(function(){return X.push(i),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:p}),document.addEventListener("wheel",u,I),document.addEventListener("touchmove",u,I),document.addEventListener("touchstart",s,I),function(){X=X.filter(function(e){return e!==i}),document.removeEventListener("wheel",u,I),document.removeEventListener("touchmove",u,I),document.removeEventListener("touchstart",s,I)}},[]);var v=e.removeScrollBar,h=e.inert;return f.createElement(f.Fragment,null,h?f.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,v?f.createElement(j,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},y.useMedium(c),x),Y=f.forwardRef(function(e,t){return f.createElement(b,s({},e,{ref:t,sideCar:$}))});Y.classNames=b.classNames;var q=Y},6734:function(e,t,n){n.d(t,{M:function(){return r}});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},3151:function(e,t,n){n.d(t,{B:function(){return u}});var r=n(2208),o=n(662),i=n(916),l=n(9382),a=n(8980);function u(e){let t=e+"CollectionProvider",[n,u]=(0,o.b)(t),[c,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),d=e=>{let{scope:t,children:n}=e,o=r.useRef(null),i=r.useRef(new Map).current;return(0,a.jsx)(c,{scope:t,itemMap:i,collectionRef:o,children:n})};d.displayName=t;let f=e+"CollectionSlot",p=(0,l.Z8)(f),v=r.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=s(f,n),l=(0,i.e)(t,o.collectionRef);return(0,a.jsx)(p,{ref:l,children:r})});v.displayName=f;let h=e+"CollectionItemSlot",m="data-radix-collection-item",g=(0,l.Z8)(h),y=r.forwardRef((e,t)=>{let{scope:n,children:o,...l}=e,u=r.useRef(null),c=(0,i.e)(t,u),d=s(h,n);return r.useEffect(()=>(d.itemMap.set(u,{ref:u,...l}),()=>void d.itemMap.delete(u))),(0,a.jsx)(g,{[m]:"",ref:c,children:o})});return y.displayName=h,[{Provider:d,Slot:v,ItemSlot:y},function(t){let n=s(e+"CollectionConsumer",t);return r.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(m,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},u]}},662:function(e,t,n){n.d(t,{b:function(){return l},k:function(){return i}});var r=n(2208),o=n(8980);function i(e,t){let n=r.createContext(t),i=e=>{let{children:t,...i}=e,l=r.useMemo(()=>i,Object.values(i));return(0,o.jsx)(n.Provider,{value:l,children:t})};return i.displayName=e+"Provider",[i,function(o){let i=r.useContext(n);if(i)return i;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function l(e,t=[]){let n=[],i=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return i.scopeName=e,[function(t,i){let l=r.createContext(i),a=n.length;n=[...n,i];let u=t=>{let{scope:n,children:i,...u}=t,c=n?.[e]?.[a]||l,s=r.useMemo(()=>u,Object.values(u));return(0,o.jsx)(c.Provider,{value:s,children:i})};return u.displayName=t+"Provider",[u,function(n,o){let u=o?.[e]?.[a]||l,c=r.useContext(u);if(c)return c;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(i,...t)]}},8044:function(e,t,n){n.d(t,{gm:function(){return i}});var r=n(2208);n(8980);var o=r.createContext(void 0);function i(e){let t=r.useContext(o);return e||t||"ltr"}},6137:function(e,t,n){n.d(t,{XB:function(){return f}});var r,o=n(2208),i=n(6734),l=n(7976),a=n(916),u=n(5696),c=n(8980),s="dismissableLayer.update",d=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{var n,f;let{disableOutsidePointerEvents:h=!1,onEscapeKeyDown:m,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:w,onDismiss:b,...x}=e,E=o.useContext(d),[S,C]=o.useState(null),R=null!==(f=null==S?void 0:S.ownerDocument)&&void 0!==f?f:null===(n=globalThis)||void 0===n?void 0:n.document,[,M]=o.useState({}),T=(0,a.e)(t,e=>C(e)),A=Array.from(E.layers),[k]=[...E.layersWithOutsidePointerEventsDisabled].slice(-1),P=A.indexOf(k),N=S?A.indexOf(S):-1,L=E.layersWithOutsidePointerEventsDisabled.size>0,O=N>=P,j=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,u.W)(e),i=o.useRef(!1),l=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){v("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",l.current),l.current=t,n.addEventListener("click",l.current,{once:!0})):t()}else n.removeEventListener("click",l.current);i.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",l.current)}},[n,r]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,n=[...E.branches].some(e=>e.contains(t));!O||n||(null==g||g(e),null==w||w(e),e.defaultPrevented||null==b||b())},R),D=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,u.W)(e),i=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!i.current&&v("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;[...E.branches].some(e=>e.contains(t))||(null==y||y(e),null==w||w(e),e.defaultPrevented||null==b||b())},R);return!function(e,t=globalThis?.document){let n=(0,u.W)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{N!==E.layers.size-1||(null==m||m(e),!e.defaultPrevented&&b&&(e.preventDefault(),b()))},R),o.useEffect(()=>{if(S)return h&&(0===E.layersWithOutsidePointerEventsDisabled.size&&(r=R.body.style.pointerEvents,R.body.style.pointerEvents="none"),E.layersWithOutsidePointerEventsDisabled.add(S)),E.layers.add(S),p(),()=>{h&&1===E.layersWithOutsidePointerEventsDisabled.size&&(R.body.style.pointerEvents=r)}},[S,R,h,E]),o.useEffect(()=>()=>{S&&(E.layers.delete(S),E.layersWithOutsidePointerEventsDisabled.delete(S),p())},[S,E]),o.useEffect(()=>{let e=()=>M({});return document.addEventListener(s,e),()=>document.removeEventListener(s,e)},[]),(0,c.jsx)(l.WV.div,{...x,ref:T,style:{pointerEvents:L?O?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.M)(e.onFocusCapture,D.onFocusCapture),onBlurCapture:(0,i.M)(e.onBlurCapture,D.onBlurCapture),onPointerDownCapture:(0,i.M)(e.onPointerDownCapture,j.onPointerDownCapture)})});function p(){let e=new CustomEvent(s);document.dispatchEvent(e)}function v(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),o?(0,l.jH)(i,a):i.dispatchEvent(a)}f.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(d),r=o.useRef(null),i=(0,a.e)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,c.jsx)(l.WV.div,{...e,ref:i})}).displayName="DismissableLayerBranch"},7914:function(e,t,n){n.d(t,{EW:function(){return i}});var r=n(2208),o=0;function i(){r.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=n[0])&&void 0!==e?e:l()),document.body.insertAdjacentElement("beforeend",null!==(t=n[1])&&void 0!==t?t:l()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function l(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},8174:function(e,t,n){let r;n.d(t,{M:function(){return f}});var o=n(2208),i=n(916),l=n(7976),a=n(5696),u=n(8980),c="focusScope.autoFocusOnMount",s="focusScope.autoFocusOnUnmount",d={bubbles:!1,cancelable:!0},f=o.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:f,onUnmountAutoFocus:g,...y}=e,[w,b]=o.useState(null),x=(0,a.W)(f),E=(0,a.W)(g),S=o.useRef(null),C=(0,i.e)(t,e=>b(e)),R=o.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;o.useEffect(()=>{if(r){let e=function(e){if(R.paused||!w)return;let t=e.target;w.contains(t)?S.current=t:h(S.current,{select:!0})},t=function(e){if(R.paused||!w)return;let t=e.relatedTarget;null===t||w.contains(t)||h(S.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&h(w)});return w&&n.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,w,R.paused]),o.useEffect(()=>{if(w){m.add(R);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(c,d);w.addEventListener(c,x),w.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(h(r,{select:t}),document.activeElement!==n)return}(p(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&h(w))}return()=>{w.removeEventListener(c,x),setTimeout(()=>{let t=new CustomEvent(s,d);w.addEventListener(s,E),w.dispatchEvent(t),t.defaultPrevented||h(null!=e?e:document.body,{select:!0}),w.removeEventListener(s,E),m.remove(R)},0)}}},[w,x,E,R]);let M=o.useCallback(e=>{if(!n&&!r||R.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,i]=function(e){let t=p(e);return[v(t,e),v(t.reverse(),e)]}(t);r&&i?e.shiftKey||o!==i?e.shiftKey&&o===r&&(e.preventDefault(),n&&h(i,{select:!0})):(e.preventDefault(),n&&h(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,R.paused]);return(0,u.jsx)(l.WV.div,{tabIndex:-1,...y,ref:C,onKeyDown:M})});function p(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function v(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function h(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}f.displayName="FocusScope";var m=(r=[],{add(e){let t=r[0];e!==t&&(null==t||t.pause()),(r=g(r,e)).unshift(e)},remove(e){var t;null===(t=(r=g(r,e))[0])||void 0===t||t.resume()}});function g(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},3102:function(e,t,n){n.d(t,{M:function(){return u}});var r,o=n(2208),i=n(9218),l=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),a=0;function u(e){let[t,n]=o.useState(l());return(0,i.b)(()=>{e||n(e=>e??String(a++))},[e]),e||(t?`radix-${t}`:"")}},3747:function(e,t,n){n.d(t,{f:function(){return a}});var r=n(2208),o=n(7976),i=n(8980),l=r.forwardRef((e,t)=>(0,i.jsx)(o.WV.label,{...e,ref:t,onMouseDown:t=>{var n;t.target.closest("button, input, select, textarea")||(null===(n=e.onMouseDown)||void 0===n||n.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var a=l},8612:function(e,t,n){n.d(t,{h:function(){return u}});var r=n(2208),o=n(6186),i=n(7976),l=n(9218),a=n(8980),u=r.forwardRef((e,t)=>{var n,u;let{container:c,...s}=e,[d,f]=r.useState(!1);(0,l.b)(()=>f(!0),[]);let p=c||d&&(null===(u=globalThis)||void 0===u?void 0:null===(n=u.document)||void 0===n?void 0:n.body);return p?o.createPortal((0,a.jsx)(i.WV.div,{...s,ref:t}),p):null});u.displayName="Portal"},4357:function(e,t,n){n.d(t,{z:function(){return l}});var r=n(2208),o=n(916),i=n(9218),l=e=>{var t,n;let l,u;let{present:c,children:s}=e,d=function(e){var t,n;let[o,l]=r.useState(),u=r.useRef(null),c=r.useRef(e),s=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=a(u.current);s.current="mounted"===d?e:"none"},[d]),(0,i.b)(()=>{let t=u.current,n=c.current;if(n!==e){let r=s.current,o=a(t);e?f("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),c.current=e}},[e,f]),(0,i.b)(()=>{if(o){var e;let t;let n=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=a(u.current).includes(e.animationName);if(e.target===o&&r&&(f("ANIMATION_END"),!c.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},i=e=>{e.target===o&&(s.current=a(u.current))};return o.addEventListener("animationstart",i),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",i),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{u.current=e?getComputedStyle(e):null,l(e)},[])}}(c),f="function"==typeof s?s({present:d.isPresent}):r.Children.only(s),p=(0,o.e)(d.ref,(l=null===(t=Object.getOwnPropertyDescriptor(f.props,"ref"))||void 0===t?void 0:t.get)&&"isReactWarning"in l&&l.isReactWarning?f.ref:(l=null===(n=Object.getOwnPropertyDescriptor(f,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in l&&l.isReactWarning?f.props.ref:f.props.ref||f.ref);return"function"==typeof s||d.isPresent?r.cloneElement(f,{ref:p}):null};function a(e){return(null==e?void 0:e.animationName)||"none"}l.displayName="Presence"},7976:function(e,t,n){n.d(t,{WV:function(){return a},jH:function(){return u}});var r=n(2208),o=n(6186),i=n(9382),l=n(8980),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,i.Z8)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...i}=e,a=o?n:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(a,{...i,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function u(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},242:function(e,t,n){n.d(t,{VY:function(){return ns},ZA:function(){return nf},JO:function(){return nu},ck:function(){return nv},wU:function(){return nm},eT:function(){return nh},__:function(){return np},h_:function(){return nc},fC:function(){return ni},$G:function(){return ny},u_:function(){return ng},Z0:function(){return nw},xz:function(){return nl},B4:function(){return na},l_:function(){return nd}});var r=n(2208),o=n(6186);function i(e,[t,n]){return Math.min(n,Math.max(t,e))}var l=n(6734),a=n(3151),u=n(916),c=n(662),s=n(8044),d=n(6137),f=n(7914),p=n(8174),v=n(3102);let h=["top","right","bottom","left"],m=Math.min,g=Math.max,y=Math.round,w=Math.floor,b=e=>({x:e,y:e}),x={left:"right",right:"left",bottom:"top",top:"bottom"},E={start:"end",end:"start"};function S(e,t){return"function"==typeof e?e(t):e}function C(e){return e.split("-")[0]}function R(e){return e.split("-")[1]}function M(e){return"x"===e?"y":"x"}function T(e){return"y"===e?"height":"width"}let A=new Set(["top","bottom"]);function k(e){return A.has(C(e))?"y":"x"}function P(e){return e.replace(/start|end/g,e=>E[e])}let N=["left","right"],L=["right","left"],O=["top","bottom"],j=["bottom","top"];function D(e){return e.replace(/left|right|bottom|top/g,e=>x[e])}function W(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function I(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function F(e,t,n){let r,{reference:o,floating:i}=e,l=k(t),a=M(k(t)),u=T(a),c=C(t),s="y"===l,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,p=o[u]/2-i[u]/2;switch(c){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch(R(t)){case"start":r[a]-=p*(n&&s?-1:1);break;case"end":r[a]+=p*(n&&s?-1:1)}return r}let V=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),u=await (null==l.isRTL?void 0:l.isRTL(t)),c=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:d}=F(c,r,u),f=r,p={},v=0;for(let n=0;n<a.length;n++){let{name:i,fn:h}=a[n],{x:m,y:g,data:y,reset:w}=await h({x:s,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:c,platform:l,elements:{reference:e,floating:t}});s=null!=m?m:s,d=null!=g?g:d,p={...p,[i]:{...p[i],...y}},w&&v<=50&&(v++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(c=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:d}=F(c,f,u)),n=-1)}return{x:s,y:d,placement:f,strategy:o,middlewareData:p}};async function H(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:l,elements:a,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=S(t,e),v=W(p),h=a[f?"floating"===d?"reference":"floating":d],m=I(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(h)))||n?h:h.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:c,rootBoundary:s,strategy:u})),g="floating"===d?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,y=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),w=await (null==i.isElement?void 0:i.isElement(y))&&await (null==i.getScale?void 0:i.getScale(y))||{x:1,y:1},b=I(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:g,offsetParent:y,strategy:u}):g);return{top:(m.top-b.top+v.top)/w.y,bottom:(b.bottom-m.bottom+v.bottom)/w.y,left:(m.left-b.left+v.left)/w.x,right:(b.right-m.right+v.right)/w.x}}function _(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function B(e){return h.some(t=>e[t]>=0)}let z=new Set(["left","top"]);async function Z(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),l=C(n),a=R(n),u="y"===k(n),c=z.has(l)?-1:1,s=i&&u?-1:1,d=S(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:v}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&"number"==typeof v&&(p="end"===a?-1*v:v),u?{x:p*s,y:f*c}:{x:f*c,y:p*s}}function U(){return"undefined"!=typeof window}function K(e){return Y(e)?(e.nodeName||"").toLowerCase():"#document"}function X(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function $(e){var t;return null==(t=(Y(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function Y(e){return!!U()&&(e instanceof Node||e instanceof X(e).Node)}function q(e){return!!U()&&(e instanceof Element||e instanceof X(e).Element)}function G(e){return!!U()&&(e instanceof HTMLElement||e instanceof X(e).HTMLElement)}function J(e){return!!U()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof X(e).ShadowRoot)}let Q=new Set(["inline","contents"]);function ee(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=ed(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!Q.has(o)}let et=new Set(["table","td","th"]),en=[":popover-open",":modal"];function er(e){return en.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let eo=["transform","translate","scale","rotate","perspective"],ei=["transform","translate","scale","rotate","perspective","filter"],el=["paint","layout","strict","content"];function ea(e){let t=eu(),n=q(e)?ed(e):e;return eo.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||ei.some(e=>(n.willChange||"").includes(e))||el.some(e=>(n.contain||"").includes(e))}function eu(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let ec=new Set(["html","body","#document"]);function es(e){return ec.has(K(e))}function ed(e){return X(e).getComputedStyle(e)}function ef(e){return q(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function ep(e){if("html"===K(e))return e;let t=e.assignedSlot||e.parentNode||J(e)&&e.host||$(e);return J(t)?t.host:t}function ev(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=ep(t);return es(n)?t.ownerDocument?t.ownerDocument.body:t.body:G(n)&&ee(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=X(o);if(i){let e=eh(l);return t.concat(l,l.visualViewport||[],ee(o)?o:[],e&&n?ev(e):[])}return t.concat(o,ev(o,[],n))}function eh(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function em(e){let t=ed(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=G(e),i=o?e.offsetWidth:n,l=o?e.offsetHeight:r,a=y(n)!==i||y(r)!==l;return a&&(n=i,r=l),{width:n,height:r,$:a}}function eg(e){return q(e)?e:e.contextElement}function ey(e){let t=eg(e);if(!G(t))return b(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=em(t),l=(i?y(n.width):n.width)/r,a=(i?y(n.height):n.height)/o;return l&&Number.isFinite(l)||(l=1),a&&Number.isFinite(a)||(a=1),{x:l,y:a}}let ew=b(0);function eb(e){let t=X(e);return eu()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:ew}function ex(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),l=eg(e),a=b(1);t&&(r?q(r)&&(a=ey(r)):a=ey(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===X(l))&&o)?eb(l):b(0),c=(i.left+u.x)/a.x,s=(i.top+u.y)/a.y,d=i.width/a.x,f=i.height/a.y;if(l){let e=X(l),t=r&&q(r)?X(r):r,n=e,o=eh(n);for(;o&&r&&t!==n;){let e=ey(o),t=o.getBoundingClientRect(),r=ed(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,s*=e.y,d*=e.x,f*=e.y,c+=i,s+=l,o=eh(n=X(o))}}return I({width:d,height:f,x:c,y:s})}function eE(e,t){let n=ef(e).scrollLeft;return t?t.left+n:ex($(e)).left+n}function eS(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:eE(e,r)),y:r.top+t.scrollTop}}let eC=new Set(["absolute","fixed"]);function eR(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=X(e),r=$(e),o=n.visualViewport,i=r.clientWidth,l=r.clientHeight,a=0,u=0;if(o){i=o.width,l=o.height;let e=eu();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,u=o.offsetTop)}return{width:i,height:l,x:a,y:u}}(e,n);else if("document"===t)r=function(e){let t=$(e),n=ef(e),r=e.ownerDocument.body,o=g(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=g(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+eE(e),a=-n.scrollTop;return"rtl"===ed(r).direction&&(l+=g(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:a}}($(e));else if(q(t))r=function(e,t){let n=ex(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=G(e)?ey(e):b(1),l=e.clientWidth*i.x;return{width:l,height:e.clientHeight*i.y,x:o*i.x,y:r*i.y}}(t,n);else{let n=eb(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return I(r)}function eM(e){return"static"===ed(e).position}function eT(e,t){if(!G(e)||"fixed"===ed(e).position)return null;if(t)return t(e);let n=e.offsetParent;return $(e)===n&&(n=n.ownerDocument.body),n}function eA(e,t){var n;let r=X(e);if(er(e))return r;if(!G(e)){let t=ep(e);for(;t&&!es(t);){if(q(t)&&!eM(t))return t;t=ep(t)}return r}let o=eT(e,t);for(;o&&(n=o,et.has(K(n)))&&eM(o);)o=eT(o,t);return o&&es(o)&&eM(o)&&!ea(o)?r:o||function(e){let t=ep(e);for(;G(t)&&!es(t);){if(ea(t))return t;if(er(t))break;t=ep(t)}return null}(e)||r}let ek=async function(e){let t=this.getOffsetParent||eA,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=G(t),o=$(t),i="fixed"===n,l=ex(e,!0,i,t),a={scrollLeft:0,scrollTop:0},u=b(0);if(r||!r&&!i){if(("body"!==K(t)||ee(o))&&(a=ef(t)),r){let e=ex(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=eE(o))}i&&!r&&o&&(u.x=eE(o));let c=!o||r||i?b(0):eS(o,a);return{x:l.left+a.scrollLeft-u.x-c.x,y:l.top+a.scrollTop-u.y-c.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eP={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,l=$(r),a=!!t&&er(t.floating);if(r===l||a&&i)return n;let u={scrollLeft:0,scrollTop:0},c=b(1),s=b(0),d=G(r);if((d||!d&&!i)&&(("body"!==K(r)||ee(l))&&(u=ef(r)),G(r))){let e=ex(r);c=ey(r),s.x=e.x+r.clientLeft,s.y=e.y+r.clientTop}let f=!l||d||i?b(0):eS(l,u,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-u.scrollLeft*c.x+s.x+f.x,y:n.y*c.y-u.scrollTop*c.y+s.y+f.y}},getDocumentElement:$,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,i=[..."clippingAncestors"===n?er(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=ev(e,[],!1).filter(e=>q(e)&&"body"!==K(e)),o=null,i="fixed"===ed(e).position,l=i?ep(e):e;for(;q(l)&&!es(l);){let t=ed(l),n=ea(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&eC.has(o.position)||ee(l)&&!n&&function e(t,n){let r=ep(t);return!(r===n||!q(r)||es(r))&&("fixed"===ed(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):o=t,l=ep(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],l=i[0],a=i.reduce((e,n)=>{let r=eR(t,n,o);return e.top=g(r.top,e.top),e.right=m(r.right,e.right),e.bottom=m(r.bottom,e.bottom),e.left=g(r.left,e.left),e},eR(t,l,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:eA,getElementRects:ek,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=em(e);return{width:t,height:n}},getScale:ey,isElement:q,isRTL:function(e){return"rtl"===ed(e).direction}};function eN(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eL=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:i,platform:l,elements:a,middlewareData:u}=t,{element:c,padding:s=0}=S(e,t)||{};if(null==c)return{};let d=W(s),f={x:n,y:r},p=M(k(o)),v=T(p),h=await l.getDimensions(c),y="y"===p,w=y?"clientHeight":"clientWidth",b=i.reference[v]+i.reference[p]-f[p]-i.floating[v],x=f[p]-i.reference[p],E=await (null==l.getOffsetParent?void 0:l.getOffsetParent(c)),C=E?E[w]:0;C&&await (null==l.isElement?void 0:l.isElement(E))||(C=a.floating[w]||i.floating[v]);let A=C/2-h[v]/2-1,P=m(d[y?"top":"left"],A),N=m(d[y?"bottom":"right"],A),L=C-h[v]-N,O=C/2-h[v]/2+(b/2-x/2),j=g(P,m(O,L)),D=!u.arrow&&null!=R(o)&&O!==j&&i.reference[v]/2-(O<P?P:N)-h[v]/2<0,I=D?O<P?O-P:O-L:0;return{[p]:f[p]+I,data:{[p]:j,centerOffset:O-j-I,...D&&{alignmentOffset:I}},reset:D}}}),eO=(e,t,n)=>{let r=new Map,o={platform:eP,...n},i={...o.platform,_c:r};return V(e,t,{...o,platform:i})};var ej="undefined"!=typeof document?r.useLayoutEffect:function(){};function eD(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eD(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!eD(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eW(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eI(e,t){let n=eW(e);return Math.round(t*n)/n}function eF(e){let t=r.useRef(e);return ej(()=>{t.current=e}),t}let eV=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?eL({element:n.current,padding:r}).fn(t):{}:n?eL({element:n,padding:r}).fn(t):{}}}),eH=(e,t)=>{var n;return{...(void 0===(n=e)&&(n=0),{name:"offset",options:n,async fn(e){var t,r;let{x:o,y:i,placement:l,middlewareData:a}=e,u=await Z(e,n);return l===(null==(t=a.offset)?void 0:t.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:l}}}}),options:[e,t]}},e_=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"shift",options:n,async fn(e){let{x:t,y:r,placement:o}=e,{mainAxis:i=!0,crossAxis:l=!1,limiter:a={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...u}=S(n,e),c={x:t,y:r},s=await H(e,u),d=k(C(o)),f=M(d),p=c[f],v=c[d];if(i){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=p+s[e],r=p-s[t];p=g(n,m(p,r))}if(l){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=v+s[e],r=v-s[t];v=g(n,m(v,r))}let h=a.fn({...e,[f]:p,[d]:v});return{...h,data:{x:h.x-t,y:h.y-r,enabled:{[f]:i,[d]:l}}}}}),options:[e,t]}},eB=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{options:n,fn(e){let{x:t,y:r,placement:o,rects:i,middlewareData:l}=e,{offset:a=0,mainAxis:u=!0,crossAxis:c=!0}=S(n,e),s={x:t,y:r},d=k(o),f=M(d),p=s[f],v=s[d],h=S(a,e),m="number"==typeof h?{mainAxis:h,crossAxis:0}:{mainAxis:0,crossAxis:0,...h};if(u){let e="y"===f?"height":"width",t=i.reference[f]-i.floating[e]+m.mainAxis,n=i.reference[f]+i.reference[e]-m.mainAxis;p<t?p=t:p>n&&(p=n)}if(c){var g,y;let e="y"===f?"width":"height",t=z.has(C(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(g=l.offset)?void 0:g[d])||0)+(t?0:m.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(y=l.offset)?void 0:y[d])||0)-(t?m.crossAxis:0);v<n?v=n:v>r&&(v=r)}return{[f]:p,[d]:v}}}),options:[e,t]}},ez=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"flip",options:n,async fn(e){var t,r,o,i,l;let{placement:a,middlewareData:u,rects:c,initialPlacement:s,platform:d,elements:f}=e,{mainAxis:p=!0,crossAxis:v=!0,fallbackPlacements:h,fallbackStrategy:m="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:y=!0,...w}=S(n,e);if(null!=(t=u.arrow)&&t.alignmentOffset)return{};let b=C(a),x=k(s),E=C(s)===s,A=await (null==d.isRTL?void 0:d.isRTL(f.floating)),W=h||(E||!y?[D(s)]:function(e){let t=D(e);return[P(e),t,P(t)]}(s)),I="none"!==g;!h&&I&&W.push(...function(e,t,n,r){let o=R(e),i=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?L:N;return t?N:L;case"left":case"right":return t?O:j;default:return[]}}(C(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(P)))),i}(s,y,g,A));let F=[s,...W],V=await H(e,w),_=[],B=(null==(r=u.flip)?void 0:r.overflows)||[];if(p&&_.push(V[b]),v){let e=function(e,t,n){void 0===n&&(n=!1);let r=R(e),o=M(k(e)),i=T(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=D(l)),[l,D(l)]}(a,c,A);_.push(V[e[0]],V[e[1]])}if(B=[...B,{placement:a,overflows:_}],!_.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=F[e];if(t&&(!("alignment"===v&&x!==k(t))||B.every(e=>e.overflows[0]>0&&k(e.placement)===x)))return{data:{index:e,overflows:B},reset:{placement:t}};let n=null==(i=B.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(m){case"bestFit":{let e=null==(l=B.filter(e=>{if(I){let t=k(e.placement);return t===x||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=s}if(a!==n)return{reset:{placement:n}}}return{}}}),options:[e,t]}},eZ=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"size",options:n,async fn(e){var t,r;let o,i;let{placement:l,rects:a,platform:u,elements:c}=e,{apply:s=()=>{},...d}=S(n,e),f=await H(e,d),p=C(l),v=R(l),h="y"===k(l),{width:y,height:w}=a.floating;"top"===p||"bottom"===p?(o=p,i=v===(await (null==u.isRTL?void 0:u.isRTL(c.floating))?"start":"end")?"left":"right"):(i=p,o="end"===v?"top":"bottom");let b=w-f.top-f.bottom,x=y-f.left-f.right,E=m(w-f[o],b),M=m(y-f[i],x),T=!e.middlewareData.shift,A=E,P=M;if(null!=(t=e.middlewareData.shift)&&t.enabled.x&&(P=x),null!=(r=e.middlewareData.shift)&&r.enabled.y&&(A=b),T&&!v){let e=g(f.left,0),t=g(f.right,0),n=g(f.top,0),r=g(f.bottom,0);h?P=y-2*(0!==e||0!==t?e+t:g(f.left,f.right)):A=w-2*(0!==n||0!==r?n+r:g(f.top,f.bottom))}await s({...e,availableWidth:P,availableHeight:A});let N=await u.getDimensions(c.floating);return y!==N.width||w!==N.height?{reset:{rects:!0}}:{}}}),options:[e,t]}},eU=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"hide",options:n,async fn(e){let{rects:t}=e,{strategy:r="referenceHidden",...o}=S(n,e);switch(r){case"referenceHidden":{let n=_(await H(e,{...o,elementContext:"reference"}),t.reference);return{data:{referenceHiddenOffsets:n,referenceHidden:B(n)}}}case"escaped":{let n=_(await H(e,{...o,altBoundary:!0}),t.floating);return{data:{escapedOffsets:n,escaped:B(n)}}}default:return{}}}}),options:[e,t]}},eK=(e,t)=>({...eV(e),options:[e,t]});var eX=n(7976),e$=n(8980),eY=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,e$.jsx)(eX.WV.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,e$.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eY.displayName="Arrow";var eq=n(5696),eG=n(9218),eJ="Popper",[eQ,e0]=(0,c.b)(eJ),[e1,e2]=eQ(eJ),e8=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,e$.jsx)(e1,{scope:t,anchor:o,onAnchorChange:i,children:n})};e8.displayName=eJ;var e6="PopperAnchor",e9=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...i}=e,l=e2(e6,n),a=r.useRef(null),c=(0,u.e)(t,a);return r.useEffect(()=>{l.onAnchorChange((null==o?void 0:o.current)||a.current)}),o?null:(0,e$.jsx)(eX.WV.div,{...i,ref:c})});e9.displayName=e6;var e5="PopperContent",[e7,e4]=eQ(e5),e3=r.forwardRef((e,t)=>{var n,i,l,a,c,s,d,f;let{__scopePopper:p,side:v="bottom",sideOffset:h=0,align:y="center",alignOffset:b=0,arrowPadding:x=0,avoidCollisions:E=!0,collisionBoundary:S=[],collisionPadding:C=0,sticky:R="partial",hideWhenDetached:M=!1,updatePositionStrategy:T="optimized",onPlaced:A,...k}=e,P=e2(e5,p),[N,L]=r.useState(null),O=(0,u.e)(t,e=>L(e)),[j,D]=r.useState(null),W=function(e){let[t,n]=r.useState(void 0);return(0,eG.b)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(j),I=null!==(d=null==W?void 0:W.width)&&void 0!==d?d:0,F=null!==(f=null==W?void 0:W.height)&&void 0!==f?f:0,V="number"==typeof C?C:{top:0,right:0,bottom:0,left:0,...C},H=Array.isArray(S)?S:[S],_=H.length>0,B={padding:V,boundary:H.filter(tr),altBoundary:_},{refs:z,floatingStyles:Z,placement:U,isPositioned:K,middlewareData:X}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:i=[],platform:l,elements:{reference:a,floating:u}={},transform:c=!0,whileElementsMounted:s,open:d}=e,[f,p]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[v,h]=r.useState(i);eD(v,i)||h(i);let[m,g]=r.useState(null),[y,w]=r.useState(null),b=r.useCallback(e=>{e!==C.current&&(C.current=e,g(e))},[]),x=r.useCallback(e=>{e!==R.current&&(R.current=e,w(e))},[]),E=a||m,S=u||y,C=r.useRef(null),R=r.useRef(null),M=r.useRef(f),T=null!=s,A=eF(s),k=eF(l),P=eF(d),N=r.useCallback(()=>{if(!C.current||!R.current)return;let e={placement:t,strategy:n,middleware:v};k.current&&(e.platform=k.current),eO(C.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==P.current};L.current&&!eD(M.current,t)&&(M.current=t,o.flushSync(()=>{p(t)}))})},[v,t,n,k,P]);ej(()=>{!1===d&&M.current.isPositioned&&(M.current.isPositioned=!1,p(e=>({...e,isPositioned:!1})))},[d]);let L=r.useRef(!1);ej(()=>(L.current=!0,()=>{L.current=!1}),[]),ej(()=>{if(E&&(C.current=E),S&&(R.current=S),E&&S){if(A.current)return A.current(E,S,N);N()}},[E,S,N,A,T]);let O=r.useMemo(()=>({reference:C,floating:R,setReference:b,setFloating:x}),[b,x]),j=r.useMemo(()=>({reference:E,floating:S}),[E,S]),D=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!j.floating)return e;let t=eI(j.floating,f.x),r=eI(j.floating,f.y);return c?{...e,transform:"translate("+t+"px, "+r+"px)",...eW(j.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,c,j.floating,f.x,f.y]);return r.useMemo(()=>({...f,update:N,refs:O,elements:j,floatingStyles:D}),[f,N,O,j,D])}({strategy:"fixed",placement:v+("center"!==y?"-"+y:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:i=!0,ancestorResize:l=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:u="function"==typeof IntersectionObserver,animationFrame:c=!1}=r,s=eg(e),d=i||l?[...s?ev(s):[],...ev(t)]:[];d.forEach(e=>{i&&e.addEventListener("scroll",n,{passive:!0}),l&&e.addEventListener("resize",n)});let f=s&&u?function(e,t){let n,r=null,o=$(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function l(a,u){void 0===a&&(a=!1),void 0===u&&(u=1),i();let c=e.getBoundingClientRect(),{left:s,top:d,width:f,height:p}=c;if(a||t(),!f||!p)return;let v=w(d),h=w(o.clientWidth-(s+f)),y={rootMargin:-v+"px "+-h+"px "+-w(o.clientHeight-(d+p))+"px "+-w(s)+"px",threshold:g(0,m(1,u))||1},b=!0;function x(t){let r=t[0].intersectionRatio;if(r!==u){if(!b)return l();r?l(!1,r):n=setTimeout(()=>{l(!1,1e-7)},1e3)}1!==r||eN(c,e.getBoundingClientRect())||l(),b=!1}try{r=new IntersectionObserver(x,{...y,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(x,y)}r.observe(e)}(!0),i}(s,n):null,p=-1,v=null;a&&(v=new ResizeObserver(e=>{let[r]=e;r&&r.target===s&&v&&(v.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=v)||e.observe(t)})),n()}),s&&!c&&v.observe(s),v.observe(t));let h=c?ex(e):null;return c&&function t(){let r=ex(e);h&&!eN(h,r)&&n(),h=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;d.forEach(e=>{i&&e.removeEventListener("scroll",n),l&&e.removeEventListener("resize",n)}),null==f||f(),null==(e=v)||e.disconnect(),v=null,c&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===T})},elements:{reference:P.anchor},middleware:[eH({mainAxis:h+F,alignmentAxis:b}),E&&e_({mainAxis:!0,crossAxis:!1,limiter:"partial"===R?eB():void 0,...B}),E&&ez({...B}),eZ({...B,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:l}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(i,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),j&&eK({element:j,padding:x}),to({arrowWidth:I,arrowHeight:F}),M&&eU({strategy:"referenceHidden",...B})]}),[Y,q]=ti(U),G=(0,eq.W)(A);(0,eG.b)(()=>{K&&(null==G||G())},[K,G]);let J=null===(n=X.arrow)||void 0===n?void 0:n.x,Q=null===(i=X.arrow)||void 0===i?void 0:i.y,ee=(null===(l=X.arrow)||void 0===l?void 0:l.centerOffset)!==0,[et,en]=r.useState();return(0,eG.b)(()=>{N&&en(window.getComputedStyle(N).zIndex)},[N]),(0,e$.jsx)("div",{ref:z.setFloating,"data-radix-popper-content-wrapper":"",style:{...Z,transform:K?Z.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:et,"--radix-popper-transform-origin":[null===(a=X.transformOrigin)||void 0===a?void 0:a.x,null===(c=X.transformOrigin)||void 0===c?void 0:c.y].join(" "),...(null===(s=X.hide)||void 0===s?void 0:s.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,e$.jsx)(e7,{scope:p,placedSide:Y,onArrowChange:D,arrowX:J,arrowY:Q,shouldHideArrow:ee,children:(0,e$.jsx)(eX.WV.div,{"data-side":Y,"data-align":q,...k,ref:O,style:{...k.style,animation:K?void 0:"none"}})})})});e3.displayName=e5;var te="PopperArrow",tt={top:"bottom",right:"left",bottom:"top",left:"right"},tn=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=e4(te,n),i=tt[o.placedSide];return(0,e$.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,e$.jsx)(eY,{...r,ref:t,style:{...r.style,display:"block"}})})});function tr(e){return null!==e}tn.displayName=te;var to=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,l;let{placement:a,rects:u,middlewareData:c}=t,s=(null===(n=c.arrow)||void 0===n?void 0:n.centerOffset)!==0,d=s?0:e.arrowWidth,f=s?0:e.arrowHeight,[p,v]=ti(a),h={start:"0%",center:"50%",end:"100%"}[v],m=(null!==(i=null===(r=c.arrow)||void 0===r?void 0:r.x)&&void 0!==i?i:0)+d/2,g=(null!==(l=null===(o=c.arrow)||void 0===o?void 0:o.y)&&void 0!==l?l:0)+f/2,y="",w="";return"bottom"===p?(y=s?h:"".concat(m,"px"),w="".concat(-f,"px")):"top"===p?(y=s?h:"".concat(m,"px"),w="".concat(u.floating.height+f,"px")):"right"===p?(y="".concat(-f,"px"),w=s?h:"".concat(g,"px")):"left"===p&&(y="".concat(u.floating.width+f,"px"),w=s?h:"".concat(g,"px")),{data:{x:y,y:w}}}});function ti(e){let[t,n="center"]=e.split("-");return[t,n]}var tl=n(8612),ta=n(9382),tu=n(9350),tc=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});r.forwardRef((e,t)=>(0,e$.jsx)(eX.WV.span,{...e,ref:t,style:{...tc,...e.style}})).displayName="VisuallyHidden";var ts=n(4895),td=n(7942),tf=[" ","Enter","ArrowUp","ArrowDown"],tp=[" ","Enter"],tv="Select",[th,tm,tg]=(0,a.B)(tv),[ty,tw]=(0,c.b)(tv,[tg,e0]),tb=e0(),[tx,tE]=ty(tv),[tS,tC]=ty(tv),tR=e=>{let{__scopeSelect:t,children:n,open:o,defaultOpen:i,onOpenChange:l,value:a,defaultValue:u,onValueChange:c,dir:d,name:f,autoComplete:p,disabled:h,required:m,form:g}=e,y=tb(t),[w,b]=r.useState(null),[x,E]=r.useState(null),[S,C]=r.useState(!1),R=(0,s.gm)(d),[M,T]=(0,tu.T)({prop:o,defaultProp:null!=i&&i,onChange:l,caller:tv}),[A,k]=(0,tu.T)({prop:a,defaultProp:u,onChange:c,caller:tv}),P=r.useRef(null),N=!w||g||!!w.closest("form"),[L,O]=r.useState(new Set),j=Array.from(L).map(e=>e.props.value).join(";");return(0,e$.jsx)(e8,{...y,children:(0,e$.jsxs)(tx,{required:m,scope:t,trigger:w,onTriggerChange:b,valueNode:x,onValueNodeChange:E,valueNodeHasChildren:S,onValueNodeHasChildrenChange:C,contentId:(0,v.M)(),value:A,onValueChange:k,open:M,onOpenChange:T,dir:R,triggerPointerDownPosRef:P,disabled:h,children:[(0,e$.jsx)(th.Provider,{scope:t,children:(0,e$.jsx)(tS,{scope:e.__scopeSelect,onNativeOptionAdd:r.useCallback(e=>{O(t=>new Set(t).add(e))},[]),onNativeOptionRemove:r.useCallback(e=>{O(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),N?(0,e$.jsxs)(nt,{"aria-hidden":!0,required:m,tabIndex:-1,name:f,autoComplete:p,value:A,onChange:e=>k(e.target.value),disabled:h,form:g,children:[void 0===A?(0,e$.jsx)("option",{value:""}):null,Array.from(L)]},j):null]})})};tR.displayName=tv;var tM="SelectTrigger",tT=r.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:o=!1,...i}=e,a=tb(n),c=tE(tM,n),s=c.disabled||o,d=(0,u.e)(t,c.onTriggerChange),f=tm(n),p=r.useRef("touch"),[v,h,m]=nr(e=>{let t=f().filter(e=>!e.disabled),n=t.find(e=>e.value===c.value),r=no(t,e,n);void 0!==r&&c.onValueChange(r.value)}),g=e=>{s||(c.onOpenChange(!0),m()),e&&(c.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,e$.jsx)(e9,{asChild:!0,...a,children:(0,e$.jsx)(eX.WV.button,{type:"button",role:"combobox","aria-controls":c.contentId,"aria-expanded":c.open,"aria-required":c.required,"aria-autocomplete":"none",dir:c.dir,"data-state":c.open?"open":"closed",disabled:s,"data-disabled":s?"":void 0,"data-placeholder":nn(c.value)?"":void 0,...i,ref:d,onClick:(0,l.M)(i.onClick,e=>{e.currentTarget.focus(),"mouse"!==p.current&&g(e)}),onPointerDown:(0,l.M)(i.onPointerDown,e=>{p.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(g(e),e.preventDefault())}),onKeyDown:(0,l.M)(i.onKeyDown,e=>{let t=""!==v.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||h(e.key),(!t||" "!==e.key)&&tf.includes(e.key)&&(g(),e.preventDefault())})})})});tT.displayName=tM;var tA="SelectValue",tk=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:i,placeholder:l="",...a}=e,c=tE(tA,n),{onValueNodeHasChildrenChange:s}=c,d=void 0!==i,f=(0,u.e)(t,c.onValueNodeChange);return(0,eG.b)(()=>{s(d)},[s,d]),(0,e$.jsx)(eX.WV.span,{...a,ref:f,style:{pointerEvents:"none"},children:nn(c.value)?(0,e$.jsx)(e$.Fragment,{children:l}):i})});tk.displayName=tA;var tP=r.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,e$.jsx)(eX.WV.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});tP.displayName="SelectIcon";var tN=e=>(0,e$.jsx)(tl.h,{asChild:!0,...e});tN.displayName="SelectPortal";var tL="SelectContent",tO=r.forwardRef((e,t)=>{let n=tE(tL,e.__scopeSelect),[i,l]=r.useState();return((0,eG.b)(()=>{l(new DocumentFragment)},[]),n.open)?(0,e$.jsx)(tI,{...e,ref:t}):i?o.createPortal((0,e$.jsx)(tj,{scope:e.__scopeSelect,children:(0,e$.jsx)(th.Slot,{scope:e.__scopeSelect,children:(0,e$.jsx)("div",{children:e.children})})}),i):null});tO.displayName=tL;var[tj,tD]=ty(tL),tW=(0,ta.Z8)("SelectContent.RemoveScroll"),tI=r.forwardRef((e,t)=>{let{__scopeSelect:n,position:o="item-aligned",onCloseAutoFocus:i,onEscapeKeyDown:a,onPointerDownOutside:c,side:s,sideOffset:v,align:h,alignOffset:m,arrowPadding:g,collisionBoundary:y,collisionPadding:w,sticky:b,hideWhenDetached:x,avoidCollisions:E,...S}=e,C=tE(tL,n),[R,M]=r.useState(null),[T,A]=r.useState(null),k=(0,u.e)(t,e=>M(e)),[P,N]=r.useState(null),[L,O]=r.useState(null),j=tm(n),[D,W]=r.useState(!1),I=r.useRef(!1);r.useEffect(()=>{if(R)return(0,ts.Ry)(R)},[R]),(0,f.EW)();let F=r.useCallback(e=>{let[t,...n]=j().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(null==n||n.scrollIntoView({block:"nearest"}),n===t&&T&&(T.scrollTop=0),n===r&&T&&(T.scrollTop=T.scrollHeight),null==n||n.focus(),document.activeElement!==o))return},[j,T]),V=r.useCallback(()=>F([P,R]),[F,P,R]);r.useEffect(()=>{D&&V()},[D,V]);let{onOpenChange:H,triggerPointerDownPosRef:_}=C;r.useEffect(()=>{if(R){let e={x:0,y:0},t=t=>{var n,r,o,i;e={x:Math.abs(Math.round(t.pageX)-(null!==(o=null===(n=_.current)||void 0===n?void 0:n.x)&&void 0!==o?o:0)),y:Math.abs(Math.round(t.pageY)-(null!==(i=null===(r=_.current)||void 0===r?void 0:r.y)&&void 0!==i?i:0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():R.contains(n.target)||H(!1),document.removeEventListener("pointermove",t),_.current=null};return null!==_.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[R,H,_]),r.useEffect(()=>{let e=()=>H(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[H]);let[B,z]=nr(e=>{let t=j().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=no(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),Z=r.useCallback((e,t,n)=>{let r=!I.current&&!n;(void 0!==C.value&&C.value===t||r)&&(N(e),r&&(I.current=!0))},[C.value]),U=r.useCallback(()=>null==R?void 0:R.focus(),[R]),K=r.useCallback((e,t,n)=>{let r=!I.current&&!n;(void 0!==C.value&&C.value===t||r)&&O(e)},[C.value]),X="popper"===o?tV:tF,$=X===tV?{side:s,sideOffset:v,align:h,alignOffset:m,arrowPadding:g,collisionBoundary:y,collisionPadding:w,sticky:b,hideWhenDetached:x,avoidCollisions:E}:{};return(0,e$.jsx)(tj,{scope:n,content:R,viewport:T,onViewportChange:A,itemRefCallback:Z,selectedItem:P,onItemLeave:U,itemTextRefCallback:K,focusSelectedItem:V,selectedItemText:L,position:o,isPositioned:D,searchRef:B,children:(0,e$.jsx)(td.Z,{as:tW,allowPinchZoom:!0,children:(0,e$.jsx)(p.M,{asChild:!0,trapped:C.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,l.M)(i,e=>{var t;null===(t=C.trigger)||void 0===t||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,e$.jsx)(d.XB,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:c,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>C.onOpenChange(!1),children:(0,e$.jsx)(X,{role:"listbox",id:C.contentId,"data-state":C.open?"open":"closed",dir:C.dir,onContextMenu:e=>e.preventDefault(),...S,...$,onPlaced:()=>W(!0),ref:k,style:{display:"flex",flexDirection:"column",outline:"none",...S.style},onKeyDown:(0,l.M)(S.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||z(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=j().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>F(t)),e.preventDefault()}})})})})})})});tI.displayName="SelectContentImpl";var tF=r.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:o,...l}=e,a=tE(tL,n),c=tD(tL,n),[s,d]=r.useState(null),[f,p]=r.useState(null),v=(0,u.e)(t,e=>p(e)),h=tm(n),m=r.useRef(!1),g=r.useRef(!0),{viewport:y,selectedItem:w,selectedItemText:b,focusSelectedItem:x}=c,E=r.useCallback(()=>{if(a.trigger&&a.valueNode&&s&&f&&y&&w&&b){let e=a.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),n=a.valueNode.getBoundingClientRect(),r=b.getBoundingClientRect();if("rtl"!==a.dir){let o=r.left-t.left,l=n.left-o,a=e.left-l,u=e.width+a,c=Math.max(u,t.width),d=i(l,[10,Math.max(10,window.innerWidth-10-c)]);s.style.minWidth=u+"px",s.style.left=d+"px"}else{let o=t.right-r.right,l=window.innerWidth-n.right-o,a=window.innerWidth-e.right-l,u=e.width+a,c=Math.max(u,t.width),d=i(l,[10,Math.max(10,window.innerWidth-10-c)]);s.style.minWidth=u+"px",s.style.right=d+"px"}let l=h(),u=window.innerHeight-20,c=y.scrollHeight,d=window.getComputedStyle(f),p=parseInt(d.borderTopWidth,10),v=parseInt(d.paddingTop,10),g=parseInt(d.borderBottomWidth,10),x=p+v+c+parseInt(d.paddingBottom,10)+g,E=Math.min(5*w.offsetHeight,x),S=window.getComputedStyle(y),C=parseInt(S.paddingTop,10),R=parseInt(S.paddingBottom,10),M=e.top+e.height/2-10,T=w.offsetHeight/2,A=p+v+(w.offsetTop+T);if(A<=M){let e=l.length>0&&w===l[l.length-1].ref.current;s.style.bottom="0px";let t=f.clientHeight-y.offsetTop-y.offsetHeight;s.style.height=A+Math.max(u-M,T+(e?R:0)+t+g)+"px"}else{let e=l.length>0&&w===l[0].ref.current;s.style.top="0px";let t=Math.max(M,p+y.offsetTop+(e?C:0)+T);s.style.height=t+(x-A)+"px",y.scrollTop=A-M+y.offsetTop}s.style.margin="".concat(10,"px 0"),s.style.minHeight=E+"px",s.style.maxHeight=u+"px",null==o||o(),requestAnimationFrame(()=>m.current=!0)}},[h,a.trigger,a.valueNode,s,f,y,w,b,a.dir,o]);(0,eG.b)(()=>E(),[E]);let[S,C]=r.useState();(0,eG.b)(()=>{f&&C(window.getComputedStyle(f).zIndex)},[f]);let R=r.useCallback(e=>{e&&!0===g.current&&(E(),null==x||x(),g.current=!1)},[E,x]);return(0,e$.jsx)(tH,{scope:n,contentWrapper:s,shouldExpandOnScrollRef:m,onScrollButtonChange:R,children:(0,e$.jsx)("div",{ref:d,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:S},children:(0,e$.jsx)(eX.WV.div,{...l,ref:v,style:{boxSizing:"border-box",maxHeight:"100%",...l.style}})})})});tF.displayName="SelectItemAlignedPosition";var tV=r.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...i}=e,l=tb(n);return(0,e$.jsx)(e3,{...l,...i,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});tV.displayName="SelectPopperPosition";var[tH,t_]=ty(tL,{}),tB="SelectViewport",tz=r.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:o,...i}=e,a=tD(tB,n),c=t_(tB,n),s=(0,u.e)(t,a.onViewportChange),d=r.useRef(0);return(0,e$.jsxs)(e$.Fragment,{children:[(0,e$.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,e$.jsx)(th.Slot,{scope:n,children:(0,e$.jsx)(eX.WV.div,{"data-radix-select-viewport":"",role:"presentation",...i,ref:s,style:{position:"relative",flex:1,overflow:"hidden auto",...i.style},onScroll:(0,l.M)(i.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=c;if((null==r?void 0:r.current)&&n){let e=Math.abs(d.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let i=o+e,l=Math.min(r,i),a=i-l;n.style.height=l+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}d.current=t.scrollTop})})})]})});tz.displayName=tB;var tZ="SelectGroup",[tU,tK]=ty(tZ),tX=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=(0,v.M)();return(0,e$.jsx)(tU,{scope:n,id:o,children:(0,e$.jsx)(eX.WV.div,{role:"group","aria-labelledby":o,...r,ref:t})})});tX.displayName=tZ;var t$="SelectLabel",tY=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=tK(t$,n);return(0,e$.jsx)(eX.WV.div,{id:o.id,...r,ref:t})});tY.displayName=t$;var tq="SelectItem",[tG,tJ]=ty(tq),tQ=r.forwardRef((e,t)=>{let{__scopeSelect:n,value:o,disabled:i=!1,textValue:a,...c}=e,s=tE(tq,n),d=tD(tq,n),f=s.value===o,[p,h]=r.useState(null!=a?a:""),[m,g]=r.useState(!1),y=(0,u.e)(t,e=>{var t;return null===(t=d.itemRefCallback)||void 0===t?void 0:t.call(d,e,o,i)}),w=(0,v.M)(),b=r.useRef("touch"),x=()=>{i||(s.onValueChange(o),s.onOpenChange(!1))};if(""===o)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,e$.jsx)(tG,{scope:n,value:o,disabled:i,textId:w,isSelected:f,onItemTextChange:r.useCallback(e=>{h(t=>{var n;return t||(null!==(n=null==e?void 0:e.textContent)&&void 0!==n?n:"").trim()})},[]),children:(0,e$.jsx)(th.ItemSlot,{scope:n,value:o,disabled:i,textValue:p,children:(0,e$.jsx)(eX.WV.div,{role:"option","aria-labelledby":w,"data-highlighted":m?"":void 0,"aria-selected":f&&m,"data-state":f?"checked":"unchecked","aria-disabled":i||void 0,"data-disabled":i?"":void 0,tabIndex:i?void 0:-1,...c,ref:y,onFocus:(0,l.M)(c.onFocus,()=>g(!0)),onBlur:(0,l.M)(c.onBlur,()=>g(!1)),onClick:(0,l.M)(c.onClick,()=>{"mouse"!==b.current&&x()}),onPointerUp:(0,l.M)(c.onPointerUp,()=>{"mouse"===b.current&&x()}),onPointerDown:(0,l.M)(c.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,l.M)(c.onPointerMove,e=>{if(b.current=e.pointerType,i){var t;null===(t=d.onItemLeave)||void 0===t||t.call(d)}else"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,l.M)(c.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null===(t=d.onItemLeave)||void 0===t||t.call(d)}}),onKeyDown:(0,l.M)(c.onKeyDown,e=>{var t;(null===(t=d.searchRef)||void 0===t?void 0:t.current)!==""&&" "===e.key||(tp.includes(e.key)&&x()," "===e.key&&e.preventDefault())})})})})});tQ.displayName=tq;var t0="SelectItemText",t1=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:i,style:l,...a}=e,c=tE(t0,n),s=tD(t0,n),d=tJ(t0,n),f=tC(t0,n),[p,v]=r.useState(null),h=(0,u.e)(t,e=>v(e),d.onItemTextChange,e=>{var t;return null===(t=s.itemTextRefCallback)||void 0===t?void 0:t.call(s,e,d.value,d.disabled)}),m=null==p?void 0:p.textContent,g=r.useMemo(()=>(0,e$.jsx)("option",{value:d.value,disabled:d.disabled,children:m},d.value),[d.disabled,d.value,m]),{onNativeOptionAdd:y,onNativeOptionRemove:w}=f;return(0,eG.b)(()=>(y(g),()=>w(g)),[y,w,g]),(0,e$.jsxs)(e$.Fragment,{children:[(0,e$.jsx)(eX.WV.span,{id:d.textId,...a,ref:h}),d.isSelected&&c.valueNode&&!c.valueNodeHasChildren?o.createPortal(a.children,c.valueNode):null]})});t1.displayName=t0;var t2="SelectItemIndicator",t8=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return tJ(t2,n).isSelected?(0,e$.jsx)(eX.WV.span,{"aria-hidden":!0,...r,ref:t}):null});t8.displayName=t2;var t6="SelectScrollUpButton",t9=r.forwardRef((e,t)=>{let n=tD(t6,e.__scopeSelect),o=t_(t6,e.__scopeSelect),[i,l]=r.useState(!1),a=(0,u.e)(t,o.onScrollButtonChange);return(0,eG.b)(()=>{if(n.viewport&&n.isPositioned){let e=function(){l(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,e$.jsx)(t4,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});t9.displayName=t6;var t5="SelectScrollDownButton",t7=r.forwardRef((e,t)=>{let n=tD(t5,e.__scopeSelect),o=t_(t5,e.__scopeSelect),[i,l]=r.useState(!1),a=(0,u.e)(t,o.onScrollButtonChange);return(0,eG.b)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;l(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,e$.jsx)(t4,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});t7.displayName=t5;var t4=r.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:o,...i}=e,a=tD("SelectScrollButton",n),u=r.useRef(null),c=tm(n),s=r.useCallback(()=>{null!==u.current&&(window.clearInterval(u.current),u.current=null)},[]);return r.useEffect(()=>()=>s(),[s]),(0,eG.b)(()=>{var e;let t=c().find(e=>e.ref.current===document.activeElement);null==t||null===(e=t.ref.current)||void 0===e||e.scrollIntoView({block:"nearest"})},[c]),(0,e$.jsx)(eX.WV.div,{"aria-hidden":!0,...i,ref:t,style:{flexShrink:0,...i.style},onPointerDown:(0,l.M)(i.onPointerDown,()=>{null===u.current&&(u.current=window.setInterval(o,50))}),onPointerMove:(0,l.M)(i.onPointerMove,()=>{var e;null===(e=a.onItemLeave)||void 0===e||e.call(a),null===u.current&&(u.current=window.setInterval(o,50))}),onPointerLeave:(0,l.M)(i.onPointerLeave,()=>{s()})})}),t3=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,e$.jsx)(eX.WV.div,{"aria-hidden":!0,...r,ref:t})});t3.displayName="SelectSeparator";var ne="SelectArrow";r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=tb(n),i=tE(ne,n),l=tD(ne,n);return i.open&&"popper"===l.position?(0,e$.jsx)(tn,{...o,...r,ref:t}):null}).displayName=ne;var nt=r.forwardRef((e,t)=>{let{__scopeSelect:n,value:o,...i}=e,l=r.useRef(null),a=(0,u.e)(t,l),c=function(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(o);return r.useEffect(()=>{let e=l.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(c!==o&&t){let n=new Event("change",{bubbles:!0});t.call(e,o),e.dispatchEvent(n)}},[c,o]),(0,e$.jsx)(eX.WV.select,{...i,style:{...tc,...i.style},ref:a,defaultValue:o})});function nn(e){return""===e||void 0===e}function nr(e){let t=(0,eq.W)(e),n=r.useRef(""),o=r.useRef(0),i=r.useCallback(e=>{let r=n.current+e;t(r),function e(t){n.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout(()=>e(""),1e3))}(r)},[t]),l=r.useCallback(()=>{n.current="",window.clearTimeout(o.current)},[]);return r.useEffect(()=>()=>window.clearTimeout(o.current),[]),[n,i,l]}function no(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=(r=Math.max(n?e.indexOf(n):-1,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(i=i.filter(e=>e!==n));let l=i.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}nt.displayName="SelectBubbleInput";var ni=tR,nl=tT,na=tk,nu=tP,nc=tN,ns=tO,nd=tz,nf=tX,np=tY,nv=tQ,nh=t1,nm=t8,ng=t9,ny=t7,nw=t3},5696:function(e,t,n){n.d(t,{W:function(){return o}});var r=n(2208);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},9350:function(e,t,n){n.d(t,{T:function(){return a}});var r,o=n(2208),i=n(9218),l=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||i.b;function a({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[i,a,u]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),i=o.useRef(n),a=o.useRef(t);return l(()=>{a.current=t},[t]),o.useEffect(()=>{i.current!==n&&(a.current?.(n),i.current=n)},[n,i]),[n,r,a]}({defaultProp:t,onChange:n}),c=void 0!==e,s=c?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,r])}return[s,o.useCallback(t=>{if(c){let n="function"==typeof t?t(e):t;n!==e&&u.current?.(n)}else a(t)},[c,e,a,u])]}Symbol("RADIX:SYNC_STATE")},9218:function(e,t,n){n.d(t,{b:function(){return o}});var r=n(2208),o=globalThis?.document?r.useLayoutEffect:()=>{}}}]);