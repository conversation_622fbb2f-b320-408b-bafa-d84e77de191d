"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tanstack+react-query@5.81.5_react@18.3.1";
exports.ids = ["vendor-chunks/@tanstack+react-query@5.81.5_react@18.3.1"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@tanstack+react-query@5.81.5_react@18.3.1/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js":
/*!*************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@tanstack+react-query@5.81.5_react@18.3.1/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryClientContext: () => (/* binding */ QueryClientContext),\n/* harmony export */   QueryClientProvider: () => (/* binding */ QueryClientProvider),\n/* harmony export */   useQueryClient: () => (/* binding */ useQueryClient)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ QueryClientContext,QueryClientProvider,useQueryClient auto */ // src/QueryClientProvider.tsx\n\n\nvar QueryClientContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(void 0);\nvar useQueryClient = (queryClient)=>{\n    const client = react__WEBPACK_IMPORTED_MODULE_0__.useContext(QueryClientContext);\n    if (queryClient) {\n        return queryClient;\n    }\n    if (!client) {\n        throw new Error(\"No QueryClient set, use QueryClientProvider to set one\");\n    }\n    return client;\n};\nvar QueryClientProvider = ({ client, children })=>{\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        client.mount();\n        return ()=>{\n            client.unmount();\n        };\n    }, [\n        client\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(QueryClientContext.Provider, {\n        value: client,\n        children\n    });\n};\n //# sourceMappingURL=QueryClientProvider.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@tanstack+react-query@5.81.5_react@18.3.1/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\n");

/***/ })

};
;