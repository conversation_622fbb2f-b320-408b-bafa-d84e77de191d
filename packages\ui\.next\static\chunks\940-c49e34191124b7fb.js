"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[940],{6202:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(5069).Z)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]])},1792:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(5069).Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},8055:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(5069).Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},1929:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(5069).Z)("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},3730:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(5069).Z)("Layers",[["path",{d:"m12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83Z",key:"8b97xw"}],["path",{d:"m22 17.65-9.17 4.16a2 2 0 0 1-1.66 0L2 17.65",key:"dd6zsq"}],["path",{d:"m22 12.65-9.17 4.16a2 2 0 0 1-1.66 0L2 12.65",key:"ep9fru"}]])},3281:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(5069).Z)("List",[["line",{x1:"8",x2:"21",y1:"6",y2:"6",key:"7ey8pc"}],["line",{x1:"8",x2:"21",y1:"12",y2:"12",key:"rjfblc"}],["line",{x1:"8",x2:"21",y1:"18",y2:"18",key:"c3b1m8"}],["line",{x1:"3",x2:"3.01",y1:"6",y2:"6",key:"1g7gq3"}],["line",{x1:"3",x2:"3.01",y1:"12",y2:"12",key:"1pjlvk"}],["line",{x1:"3",x2:"3.01",y1:"18",y2:"18",key:"28t2mc"}]])},9484:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(5069).Z)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},6198:function(e,t,n){n.d(t,{Dx:function(){return er},VY:function(){return en},aV:function(){return et},dk:function(){return eo},fC:function(){return Q},h_:function(){return ee},x8:function(){return ei},xz:function(){return $}});var r=n(2208),o=n(6734),i=n(916),l=n(662),a=n(3102),u=n(9350),s=n(6137),c=n(8174),d=n(8612),f=n(4357),p=n(7976),y=n(7914),g=n(7942),x=n(4895),h=n(9382),v=n(8980),m="Dialog",[k,D]=(0,l.b)(m),[b,j]=k(m),w=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:i,onOpenChange:l,modal:s=!0}=e,c=r.useRef(null),d=r.useRef(null),[f,p]=(0,u.T)({prop:o,defaultProp:null!=i&&i,onChange:l,caller:m});return(0,v.jsx)(b,{scope:t,triggerRef:c,contentRef:d,contentId:(0,a.M)(),titleId:(0,a.M)(),descriptionId:(0,a.M)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:s,children:n})};w.displayName=m;var R="DialogTrigger",C=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,l=j(R,n),a=(0,i.e)(t,l.triggerRef);return(0,v.jsx)(p.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":G(l.open),...r,ref:a,onClick:(0,o.M)(e.onClick,l.onOpenToggle)})});C.displayName=R;var M="DialogPortal",[Z,I]=k(M,{forceMount:void 0}),_=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:i}=e,l=j(M,t);return(0,v.jsx)(Z,{scope:t,forceMount:n,children:r.Children.map(o,e=>(0,v.jsx)(f.z,{present:n||l.open,children:(0,v.jsx)(d.h,{asChild:!0,container:i,children:e})}))})};_.displayName=M;var N="DialogOverlay",O=r.forwardRef((e,t)=>{let n=I(N,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=j(N,e.__scopeDialog);return i.modal?(0,v.jsx)(f.z,{present:r||i.open,children:(0,v.jsx)(F,{...o,ref:t})}):null});O.displayName=N;var E=(0,h.Z8)("DialogOverlay.RemoveScroll"),F=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=j(N,n);return(0,v.jsx)(g.Z,{as:E,allowPinchZoom:!0,shards:[o.contentRef],children:(0,v.jsx)(p.WV.div,{"data-state":G(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),P="DialogContent",W=r.forwardRef((e,t)=>{let n=I(P,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=j(P,e.__scopeDialog);return(0,v.jsx)(f.z,{present:r||i.open,children:i.modal?(0,v.jsx)(A,{...o,ref:t}):(0,v.jsx)(V,{...o,ref:t})})});W.displayName=P;var A=r.forwardRef((e,t)=>{let n=j(P,e.__scopeDialog),l=r.useRef(null),a=(0,i.e)(t,n.contentRef,l);return r.useEffect(()=>{let e=l.current;if(e)return(0,x.Ry)(e)},[]),(0,v.jsx)(L,{...e,ref:a,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.M)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=n.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,o.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,o.M)(e.onFocusOutside,e=>e.preventDefault())})}),V=r.forwardRef((e,t)=>{let n=j(P,e.__scopeDialog),o=r.useRef(!1),i=r.useRef(!1);return(0,v.jsx)(L,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,l;null===(r=e.onCloseAutoFocus)||void 0===r||r.call(e,t),t.defaultPrevented||(o.current||null===(l=n.triggerRef.current)||void 0===l||l.focus(),t.preventDefault()),o.current=!1,i.current=!1},onInteractOutside:t=>{var r,l;null===(r=e.onInteractOutside)||void 0===r||r.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(i.current=!0));let a=t.target;(null===(l=n.triggerRef.current)||void 0===l?void 0:l.contains(a))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),L=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:l,onCloseAutoFocus:a,...u}=e,d=j(P,n),f=r.useRef(null),p=(0,i.e)(t,f);return(0,y.EW)(),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(c.M,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:l,onUnmountAutoFocus:a,children:(0,v.jsx)(s.XB,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":G(d.open),...u,ref:p,onDismiss:()=>d.onOpenChange(!1)})}),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(Y,{titleId:d.titleId}),(0,v.jsx)(J,{contentRef:f,descriptionId:d.descriptionId})]})]})}),q="DialogTitle",T=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=j(q,n);return(0,v.jsx)(p.WV.h2,{id:o.titleId,...r,ref:t})});T.displayName=q;var z="DialogDescription",S=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=j(z,n);return(0,v.jsx)(p.WV.p,{id:o.descriptionId,...r,ref:t})});S.displayName=z;var B="DialogClose",H=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=j(B,n);return(0,v.jsx)(p.WV.button,{type:"button",...r,ref:t,onClick:(0,o.M)(e.onClick,()=>i.onOpenChange(!1))})});function G(e){return e?"open":"closed"}H.displayName=B;var K="DialogTitleWarning",[U,X]=(0,l.k)(K,{contentName:P,titleName:q,docsSlug:"dialog"}),Y=e=>{let{titleId:t}=e,n=X(K),o="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return r.useEffect(()=>{t&&!document.getElementById(t)&&console.error(o)},[o,t]),null},J=e=>{let{contentRef:t,descriptionId:n}=e,o=X("DialogDescriptionWarning"),i="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return r.useEffect(()=>{var e;let r=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");n&&r&&!document.getElementById(n)&&console.warn(i)},[i,t,n]),null},Q=w,$=C,ee=_,et=O,en=W,er=T,eo=S,ei=H}}]);