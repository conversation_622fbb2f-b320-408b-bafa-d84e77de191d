'use client';

import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  MoreHorizontal, 
  Play, 
  Edit, 
  Copy, 
  Trash2, 
  Calendar,
  Layers,
  Activity
} from 'lucide-react';
import Link from 'next/link';
import { formatDate } from '@/lib/utils';

import { FlowStatus } from '@flowwise-clone/shared';

interface FlowCardProps {
  flow: {
    id: string;
    name: string;
    description: string;
    status: FlowStatus;
    createdAt: string;
    updatedAt: string;
    nodeCount: number;
    tags: string[];
  };
  viewMode: 'grid' | 'list';
}

const statusColors = {
  [FlowStatus.ACTIVE]: 'bg-green-100 text-green-800',
  [FlowStatus.DRAFT]: 'bg-yellow-100 text-yellow-800',
  [FlowStatus.INACTIVE]: 'bg-gray-100 text-gray-800',
  [FlowStatus.ARCHIVED]: 'bg-red-100 text-red-800',
};

const statusIcons = {
  [FlowStatus.ACTIVE]: Activity,
  [FlowStatus.DRAFT]: Edit,
  [FlowStatus.INACTIVE]: Calendar,
  [FlowStatus.ARCHIVED]: Calendar,
};

export function FlowCard({ flow, viewMode }: FlowCardProps) {
  const StatusIcon = statusIcons[flow.status];

  if (viewMode === 'list') {
    return (
      <Card className="hover:shadow-md transition-shadow">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4 flex-1">
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-1">
                  <h3 className="font-semibold text-lg">{flow.name}</h3>
                  <Badge className={statusColors[flow.status]}>
                    <StatusIcon className="h-3 w-3 mr-1" />
                    {flow.status}
                  </Badge>
                </div>
                <p className="text-gray-600 text-sm mb-2">{flow.description}</p>
                <div className="flex items-center space-x-4 text-sm text-gray-500">
                  <div className="flex items-center">
                    <Layers className="h-4 w-4 mr-1" />
                    {flow.nodeCount} nodes
                  </div>
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-1" />
                    Updated {formatDate(flow.updatedAt)}
                  </div>
                </div>
              </div>
              <div className="flex flex-wrap gap-1">
                {flow.tags.slice(0, 3).map((tag) => (
                  <Badge key={tag} variant="secondary" className="text-xs">
                    {tag}
                  </Badge>
                ))}
                {flow.tags.length > 3 && (
                  <Badge variant="secondary" className="text-xs">
                    +{flow.tags.length - 3}
                  </Badge>
                )}
              </div>
            </div>
            <div className="flex items-center space-x-2 ml-4">
              <Button size="sm" asChild>
                <Link href={`/flows/${flow.id}`}>
                  <Edit className="h-4 w-4 mr-1" />
                  Edit
                </Link>
              </Button>
              <Button size="sm" variant="outline">
                <Play className="h-4 w-4 mr-1" />
                Test
              </Button>
              <Button size="sm" variant="ghost">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-1">
              <CardTitle className="text-lg">{flow.name}</CardTitle>
              <Badge className={statusColors[flow.status]}>
                <StatusIcon className="h-3 w-3 mr-1" />
                {flow.status}
              </Badge>
            </div>
            <CardDescription className="line-clamp-2">
              {flow.description}
            </CardDescription>
          </div>
          <Button size="sm" variant="ghost">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-4 text-sm text-gray-500">
            <div className="flex items-center">
              <Layers className="h-4 w-4 mr-1" />
              {flow.nodeCount} nodes
            </div>
            <div className="flex items-center">
              <Calendar className="h-4 w-4 mr-1" />
              {formatDate(flow.updatedAt)}
            </div>
          </div>
        </div>

        <div className="flex flex-wrap gap-1 mb-4">
          {flow.tags.slice(0, 3).map((tag) => (
            <Badge key={tag} variant="secondary" className="text-xs">
              {tag}
            </Badge>
          ))}
          {flow.tags.length > 3 && (
            <Badge variant="secondary" className="text-xs">
              +{flow.tags.length - 3}
            </Badge>
          )}
        </div>

        <div className="flex items-center space-x-2">
          <Button size="sm" className="flex-1" asChild>
            <Link href={`/flows/${flow.id}`}>
              <Edit className="h-4 w-4 mr-1" />
              Edit
            </Link>
          </Button>
          <Button size="sm" variant="outline">
            <Play className="h-4 w-4" />
          </Button>
          <Button size="sm" variant="outline">
            <Copy className="h-4 w-4" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
