"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[123],{4239:function(e,t,n){n.d(t,{AC:function(){return I},Fb:function(){return tO},HH:function(){return eW},Ly:function(){return p},OW:function(){return eg},RX:function(){return eP},VP:function(){return eR},XQ:function(){return nH},Z_:function(){return ek},_K:function(){return tb},jD:function(){return d},oI:function(){return V},oR:function(){return A},s_:function(){return P},tV:function(){return nR},u5:function(){return en},x$:function(){return nL},yn:function(){return tB}});var o,r,l,a,i,s,d,c,u,g,h,p,m=n(2208),f=n(2658),y=n(1720),x=n(1619),b=n(7853),S=n(1826),E=n(9199),v=n(1119),w=n(6186);let M=(0,m.createContext)(null),C=M.Provider,N={error001:()=>"[React Flow]: Seems like you have not used zustand provider as an ancestor. Help: https://reactflow.dev/error#001",error003:e=>`Node type "${e}" not found. Using fallback type "default".`,error004:()=>"The React Flow parent container needs a width and a height to render the graph.",error005:()=>"Only child nodes can use a parent extent.",error006:()=>"Can't create edge. An edge needs a source and a target.",error009:e=>`Marker type "${e}" doesn't exist.`,error008:(e,t)=>`Couldn't create edge for ${e?"target":"source"} handle id: "${e?t.targetHandle:t.sourceHandle}", edge id: ${t.id}.`,error010:()=>"Handle: No node id found. Make sure to only use a Handle inside a custom Node.",error011:e=>`Edge type "${e}" not found. Using fallback type "default".`,error012:e=>`Node with id "${e}" does not exist, it may have been removed. This can happen when a node is deleted before the "onNodeClick" handler is called.`},k=N.error001();function A(e,t){let n=(0,m.useContext)(M);if(null===n)throw Error(k);return(0,y.s)(n,e,t)}let I=()=>{let e=(0,m.useContext)(M);if(null===e)throw Error(k);return(0,m.useMemo)(()=>({getState:e.getState,setState:e.setState,subscribe:e.subscribe,destroy:e.destroy}),[e])},R=e=>e.userSelectionActive?"none":"all";function P({position:e,children:t,className:n,style:o,...r}){let l=A(R),a=`${e}`.split("-");return m.createElement("div",{className:(0,f.Z)(["react-flow__panel",n,...a]),style:{...o,pointerEvents:l},...r},t)}function _({proOptions:e,position:t="bottom-right"}){return e?.hideAttribution?null:m.createElement(P,{position:t,className:"react-flow__attribution","data-message":"Please only hide this attribution when you are subscribed to React Flow Pro: https://reactflow.dev/pro"},m.createElement("a",{href:"https://reactflow.dev",target:"_blank",rel:"noopener noreferrer","aria-label":"React Flow attribution"},"React Flow"))}var $=(0,m.memo)(({x:e,y:t,label:n,labelStyle:o={},labelShowBg:r=!0,labelBgStyle:l={},labelBgPadding:a=[2,4],labelBgBorderRadius:i=2,children:s,className:d,...c})=>{let u=(0,m.useRef)(null),[g,h]=(0,m.useState)({x:0,y:0,width:0,height:0}),p=(0,f.Z)(["react-flow__edge-textwrapper",d]);return((0,m.useEffect)(()=>{if(u.current){let e=u.current.getBBox();h({x:e.x,y:e.y,width:e.width,height:e.height})}},[n]),void 0!==n&&n)?m.createElement("g",{transform:`translate(${e-g.width/2} ${t-g.height/2})`,className:p,visibility:g.width?"visible":"hidden",...c},r&&m.createElement("rect",{width:g.width+2*a[0],x:-a[0],y:-a[1],height:g.height+2*a[1],className:"react-flow__edge-textbg",style:l,rx:i,ry:i}),m.createElement("text",{className:"react-flow__edge-text",y:g.height/2,dy:"0.3em",ref:u,style:o},n),s):null});let O=e=>({width:e.offsetWidth,height:e.offsetHeight}),B=(e,t=0,n=1)=>Math.min(Math.max(e,t),n),D=(e={x:0,y:0},t)=>({x:B(e.x,t[0][0],t[1][0]),y:B(e.y,t[0][1],t[1][1])}),z=(e,t,n)=>e<t?B(Math.abs(e-t),1,50)/50:e>n?-B(Math.abs(e-n),1,50)/50:0,L=(e,t)=>[20*z(e.x,35,t.width-35),20*z(e.y,35,t.height-35)],T=e=>e.getRootNode?.()||window?.document,H=(e,t)=>({x:Math.min(e.x,t.x),y:Math.min(e.y,t.y),x2:Math.max(e.x2,t.x2),y2:Math.max(e.y2,t.y2)}),F=({x:e,y:t,width:n,height:o})=>({x:e,y:t,x2:e+n,y2:t+o}),Z=({x:e,y:t,x2:n,y2:o})=>({x:e,y:t,width:n-e,height:o-t}),X=e=>({...e.positionAbsolute||{x:0,y:0},width:e.width||0,height:e.height||0}),V=(e,t)=>Z(H(F(e),F(t))),K=(e,t)=>Math.ceil(Math.max(0,Math.min(e.x+e.width,t.x+t.width)-Math.max(e.x,t.x))*Math.max(0,Math.min(e.y+e.height,t.y+t.height)-Math.max(e.y,t.y))),Y=e=>W(e.width)&&W(e.height)&&W(e.x)&&W(e.y),W=e=>!isNaN(e)&&isFinite(e),j=Symbol.for("internals"),U=["Enter"," ","Escape"],q=(e,t)=>{},G=e=>"nativeEvent"in e;function Q(e){let t=G(e)?e.nativeEvent:e,n=t.composedPath?.()?.[0]||e.target;return["INPUT","SELECT","TEXTAREA"].includes(n?.nodeName)||n?.hasAttribute("contenteditable")||!!n?.closest(".nokey")}let J=e=>"clientX"in e,ee=(e,t)=>{let n=J(e),o=n?e.clientX:e.touches?.[0].clientX,r=n?e.clientY:e.touches?.[0].clientY;return{x:o-(t?.left??0),y:r-(t?.top??0)}},et=()=>"undefined"!=typeof navigator&&navigator?.userAgent?.indexOf("Mac")>=0,en=({id:e,path:t,labelX:n,labelY:o,label:r,labelStyle:l,labelShowBg:a,labelBgStyle:i,labelBgPadding:s,labelBgBorderRadius:d,style:c,markerEnd:u,markerStart:g,interactionWidth:h=20})=>m.createElement(m.Fragment,null,m.createElement("path",{id:e,style:c,d:t,fill:"none",className:"react-flow__edge-path",markerEnd:u,markerStart:g}),h&&m.createElement("path",{d:t,fill:"none",strokeOpacity:0,strokeWidth:h,className:"react-flow__edge-interaction"}),r&&W(n)&&W(o)?m.createElement($,{x:n,y:o,label:r,labelStyle:l,labelShowBg:a,labelBgStyle:i,labelBgPadding:s,labelBgBorderRadius:d}):null);function eo(e,t,n){return void 0===n?n:o=>{let r=t().edges.find(t=>t.id===e);r&&n(o,{...r})}}function er({sourceX:e,sourceY:t,targetX:n,targetY:o}){let r=Math.abs(n-e)/2,l=Math.abs(o-t)/2;return[n<e?n+r:n-r,o<t?o+l:o-l,r,l]}function el({sourceX:e,sourceY:t,targetX:n,targetY:o,sourceControlX:r,sourceControlY:l,targetControlX:a,targetControlY:i}){let s=.125*e+.375*r+.375*a+.125*n,d=.125*t+.375*l+.375*i+.125*o;return[s,d,Math.abs(s-e),Math.abs(d-t)]}function ea({pos:e,x1:t,y1:n,x2:o,y2:r}){return e===p.Left||e===p.Right?[.5*(t+o),n]:[t,.5*(n+r)]}function ei({sourceX:e,sourceY:t,sourcePosition:n=p.Bottom,targetX:o,targetY:r,targetPosition:l=p.Top}){let[a,i]=ea({pos:n,x1:e,y1:t,x2:o,y2:r}),[s,d]=ea({pos:l,x1:o,y1:r,x2:e,y2:t}),[c,u,g,h]=el({sourceX:e,sourceY:t,targetX:o,targetY:r,sourceControlX:a,sourceControlY:i,targetControlX:s,targetControlY:d});return[`M${e},${t} C${a},${i} ${s},${d} ${o},${r}`,c,u,g,h]}en.displayName="BaseEdge",(o=d||(d={})).Strict="strict",o.Loose="loose",(r=c||(c={})).Free="free",r.Vertical="vertical",r.Horizontal="horizontal",(l=u||(u={})).Partial="partial",l.Full="full",(a=g||(g={})).Bezier="default",a.Straight="straight",a.Step="step",a.SmoothStep="smoothstep",a.SimpleBezier="simplebezier",(i=h||(h={})).Arrow="arrow",i.ArrowClosed="arrowclosed",(s=p||(p={})).Left="left",s.Top="top",s.Right="right",s.Bottom="bottom";let es=(0,m.memo)(({sourceX:e,sourceY:t,targetX:n,targetY:o,sourcePosition:r=p.Bottom,targetPosition:l=p.Top,label:a,labelStyle:i,labelShowBg:s,labelBgStyle:d,labelBgPadding:c,labelBgBorderRadius:u,style:g,markerEnd:h,markerStart:f,interactionWidth:y})=>{let[x,b,S]=ei({sourceX:e,sourceY:t,sourcePosition:r,targetX:n,targetY:o,targetPosition:l});return m.createElement(en,{path:x,labelX:b,labelY:S,label:a,labelStyle:i,labelShowBg:s,labelBgStyle:d,labelBgPadding:c,labelBgBorderRadius:u,style:g,markerEnd:h,markerStart:f,interactionWidth:y})});es.displayName="SimpleBezierEdge";let ed={[p.Left]:{x:-1,y:0},[p.Right]:{x:1,y:0},[p.Top]:{x:0,y:-1},[p.Bottom]:{x:0,y:1}},ec=({source:e,sourcePosition:t=p.Bottom,target:n})=>t===p.Left||t===p.Right?e.x<n.x?{x:1,y:0}:{x:-1,y:0}:e.y<n.y?{x:0,y:1}:{x:0,y:-1},eu=(e,t)=>Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2));function eg({sourceX:e,sourceY:t,sourcePosition:n=p.Bottom,targetX:o,targetY:r,targetPosition:l=p.Top,borderRadius:a=5,centerX:i,centerY:s,offset:d=20}){let[c,u,g,h,m]=function({source:e,sourcePosition:t=p.Bottom,target:n,targetPosition:o=p.Top,center:r,offset:l}){let a,i;let s=ed[t],d=ed[o],c={x:e.x+s.x*l,y:e.y+s.y*l},u={x:n.x+d.x*l,y:n.y+d.y*l},g=ec({source:c,sourcePosition:t,target:u}),h=0!==g.x?"x":"y",m=g[h],f=[],y={x:0,y:0},x={x:0,y:0},[b,S,E,v]=er({sourceX:e.x,sourceY:e.y,targetX:n.x,targetY:n.y});if(s[h]*d[h]==-1){a=r.x??b,i=r.y??S;let e=[{x:a,y:c.y},{x:a,y:u.y}],t=[{x:c.x,y:i},{x:u.x,y:i}];f=s[h]===m?"x"===h?e:t:"x"===h?t:e}else{let r=[{x:c.x,y:u.y}],g=[{x:u.x,y:c.y}];if(f="x"===h?s.x===m?g:r:s.y===m?r:g,t===o){let t=Math.abs(e[h]-n[h]);if(t<=l){let o=Math.min(l-1,l-t);s[h]===m?y[h]=(c[h]>e[h]?-1:1)*o:x[h]=(u[h]>n[h]?-1:1)*o}}if(t!==o){let e="x"===h?"y":"x",t=s[h]===d[e],n=c[e]>u[e],o=c[e]<u[e];(1===s[h]&&(!t&&n||t&&o)||1!==s[h]&&(!t&&o||t&&n))&&(f="x"===h?r:g)}let p={x:c.x+y.x,y:c.y+y.y},b={x:u.x+x.x,y:u.y+x.y};Math.max(Math.abs(p.x-f[0].x),Math.abs(b.x-f[0].x))>=Math.max(Math.abs(p.y-f[0].y),Math.abs(b.y-f[0].y))?(a=(p.x+b.x)/2,i=f[0].y):(a=f[0].x,i=(p.y+b.y)/2)}return[[e,{x:c.x+y.x,y:c.y+y.y},...f,{x:u.x+x.x,y:u.y+x.y},n],a,i,E,v]}({source:{x:e,y:t},sourcePosition:n,target:{x:o,y:r},targetPosition:l,center:{x:i,y:s},offset:d});return[c.reduce((e,t,n)=>e+(n>0&&n<c.length-1?function(e,t,n,o){let r=Math.min(eu(e,t)/2,eu(t,n)/2,o),{x:l,y:a}=t;if(e.x===l&&l===n.x||e.y===a&&a===n.y)return`L${l} ${a}`;if(e.y===a){let t=e.x<n.x?-1:1,o=e.y<n.y?1:-1;return`L ${l+r*t},${a}Q ${l},${a} ${l},${a+r*o}`}let i=e.x<n.x?1:-1,s=e.y<n.y?-1:1;return`L ${l},${a+r*s}Q ${l},${a} ${l+r*i},${a}`}(c[n-1],t,c[n+1],a):`${0===n?"M":"L"}${t.x} ${t.y}`),""),u,g,h,m]}let eh=(0,m.memo)(({sourceX:e,sourceY:t,targetX:n,targetY:o,label:r,labelStyle:l,labelShowBg:a,labelBgStyle:i,labelBgPadding:s,labelBgBorderRadius:d,style:c,sourcePosition:u=p.Bottom,targetPosition:g=p.Top,markerEnd:h,markerStart:f,pathOptions:y,interactionWidth:x})=>{let[b,S,E]=eg({sourceX:e,sourceY:t,sourcePosition:u,targetX:n,targetY:o,targetPosition:g,borderRadius:y?.borderRadius,offset:y?.offset});return m.createElement(en,{path:b,labelX:S,labelY:E,label:r,labelStyle:l,labelShowBg:a,labelBgStyle:i,labelBgPadding:s,labelBgBorderRadius:d,style:c,markerEnd:h,markerStart:f,interactionWidth:x})});eh.displayName="SmoothStepEdge";let ep=(0,m.memo)(e=>m.createElement(eh,{...e,pathOptions:(0,m.useMemo)(()=>({borderRadius:0,offset:e.pathOptions?.offset}),[e.pathOptions?.offset])}));ep.displayName="StepEdge";let em=(0,m.memo)(({sourceX:e,sourceY:t,targetX:n,targetY:o,label:r,labelStyle:l,labelShowBg:a,labelBgStyle:i,labelBgPadding:s,labelBgBorderRadius:d,style:c,markerEnd:u,markerStart:g,interactionWidth:h})=>{let[p,f,y]=function({sourceX:e,sourceY:t,targetX:n,targetY:o}){let[r,l,a,i]=er({sourceX:e,sourceY:t,targetX:n,targetY:o});return[`M ${e},${t}L ${n},${o}`,r,l,a,i]}({sourceX:e,sourceY:t,targetX:n,targetY:o});return m.createElement(en,{path:p,labelX:f,labelY:y,label:r,labelStyle:l,labelShowBg:a,labelBgStyle:i,labelBgPadding:s,labelBgBorderRadius:d,style:c,markerEnd:u,markerStart:g,interactionWidth:h})});function ef(e,t){return e>=0?.5*e:25*t*Math.sqrt(-e)}function ey({pos:e,x1:t,y1:n,x2:o,y2:r,c:l}){switch(e){case p.Left:return[t-ef(t-o,l),n];case p.Right:return[t+ef(o-t,l),n];case p.Top:return[t,n-ef(n-r,l)];case p.Bottom:return[t,n+ef(r-n,l)]}}function ex({sourceX:e,sourceY:t,sourcePosition:n=p.Bottom,targetX:o,targetY:r,targetPosition:l=p.Top,curvature:a=.25}){let[i,s]=ey({pos:n,x1:e,y1:t,x2:o,y2:r,c:a}),[d,c]=ey({pos:l,x1:o,y1:r,x2:e,y2:t,c:a}),[u,g,h,m]=el({sourceX:e,sourceY:t,targetX:o,targetY:r,sourceControlX:i,sourceControlY:s,targetControlX:d,targetControlY:c});return[`M${e},${t} C${i},${s} ${d},${c} ${o},${r}`,u,g,h,m]}em.displayName="StraightEdge";let eb=(0,m.memo)(({sourceX:e,sourceY:t,targetX:n,targetY:o,sourcePosition:r=p.Bottom,targetPosition:l=p.Top,label:a,labelStyle:i,labelShowBg:s,labelBgStyle:d,labelBgPadding:c,labelBgBorderRadius:u,style:g,markerEnd:h,markerStart:f,pathOptions:y,interactionWidth:x})=>{let[b,S,E]=ex({sourceX:e,sourceY:t,sourcePosition:r,targetX:n,targetY:o,targetPosition:l,curvature:y?.curvature});return m.createElement(en,{path:b,labelX:S,labelY:E,label:a,labelStyle:i,labelShowBg:s,labelBgStyle:d,labelBgPadding:c,labelBgBorderRadius:u,style:g,markerEnd:h,markerStart:f,interactionWidth:x})});eb.displayName="BezierEdge";let eS=(0,m.createContext)(null),eE=eS.Provider;eS.Consumer;let ev=()=>(0,m.useContext)(eS),ew=e=>"id"in e&&"source"in e&&"target"in e,eM=({source:e,sourceHandle:t,target:n,targetHandle:o})=>`reactflow__edge-${e}${t||""}-${n}${o||""}`,eC=(e,t)=>{if(void 0===e)return"";if("string"==typeof e)return e;let n=t?`${t}__`:"";return`${n}${Object.keys(e).sort().map(t=>`${t}=${e[t]}`).join("&")}`},eN=(e,t)=>t.some(t=>t.source===e.source&&t.target===e.target&&(t.sourceHandle===e.sourceHandle||!t.sourceHandle&&!e.sourceHandle)&&(t.targetHandle===e.targetHandle||!t.targetHandle&&!e.targetHandle)),ek=(e,t)=>{let n;return e.source&&e.target?eN(n=ew(e)?{...e}:{...e,id:eM(e)},t)?t:t.concat(n):(q("006",N.error006()),t)},eA=({x:e,y:t},[n,o,r],l,[a,i])=>{let s={x:(e-n)/r,y:(t-o)/r};return l?{x:a*Math.round(s.x/a),y:i*Math.round(s.y/i)}:s},eI=({x:e,y:t},[n,o,r])=>({x:e*r+n,y:t*r+o}),eR=(e,t=[0,0])=>{if(!e)return{x:0,y:0,positionAbsolute:{x:0,y:0}};let n=(e.width??0)*t[0],o=(e.height??0)*t[1],r={x:e.position.x-n,y:e.position.y-o};return{...r,positionAbsolute:e.positionAbsolute?{x:e.positionAbsolute.x-n,y:e.positionAbsolute.y-o}:r}},eP=(e,t=[0,0])=>0===e.length?{x:0,y:0,width:0,height:0}:Z(e.reduce((e,n)=>{let{x:o,y:r}=eR(n,t).positionAbsolute;return H(e,F({x:o,y:r,width:n.width||0,height:n.height||0}))},{x:1/0,y:1/0,x2:-1/0,y2:-1/0})),e_=(e,t,[n,o,r]=[0,0,1],l=!1,a=!1,i=[0,0])=>{let s={x:(t.x-n)/r,y:(t.y-o)/r,width:t.width/r,height:t.height/r},d=[];return e.forEach(e=>{let{width:t,height:n,selectable:o=!0,hidden:r=!1}=e;if(a&&!o||r)return!1;let{positionAbsolute:c}=eR(e,i),u=K(s,{x:c.x,y:c.y,width:t||0,height:n||0}),g=void 0===t||void 0===n||null===t||null===n,h=(t||0)*(n||0);(g||l&&u>0||u>=h||e.dragging)&&d.push(e)}),d},e$=(e,t)=>{let n=e.map(e=>e.id);return t.filter(e=>n.includes(e.source)||n.includes(e.target))},eO=(e,t,n,o,r,l=.1)=>{let a=B(Math.min(t/(e.width*(1+l)),n/(e.height*(1+l))),o,r);return{x:t/2-(e.x+e.width/2)*a,y:n/2-(e.y+e.height/2)*a,zoom:a}},eB=(e,t=0)=>e.transition().duration(t);function eD(e,t,n,o){return(t[n]||[]).reduce((t,r)=>(`${e.id}-${r.id}-${n}`!==o&&t.push({id:r.id||null,type:n,nodeId:e.id,x:(e.positionAbsolute?.x??0)+r.x+r.width/2,y:(e.positionAbsolute?.y??0)+r.y+r.height/2}),t),[])}let ez={source:null,target:null,sourceHandle:null,targetHandle:null},eL=()=>({handleDomNode:null,isValid:!1,connection:ez,endHandle:null});function eT(e,t,n,o,r,l,a){let i="target"===r,s=a.querySelector(`.react-flow__handle[data-id="${e?.nodeId}-${e?.id}-${e?.type}"]`),c={...eL(),handleDomNode:s};if(s){let e=eH(void 0,s),r=s.getAttribute("data-nodeid"),a=s.getAttribute("data-handleid"),u=s.classList.contains("connectable"),g=s.classList.contains("connectableend"),h={source:i?r:n,sourceHandle:i?a:o,target:i?n:r,targetHandle:i?o:a};c.connection=h,u&&g&&(t===d.Strict?i&&"source"===e||!i&&"target"===e:r!==n||a!==o)&&(c.endHandle={nodeId:r,handleId:a,type:e},c.isValid=l(h))}return c}function eH(e,t){return e||(t?.classList.contains("target")?"target":t?.classList.contains("source")?"source":null)}function eF(e){e?.classList.remove("valid","connecting","react-flow__handle-valid","react-flow__handle-connecting")}function eZ({event:e,handleId:t,nodeId:n,onConnect:o,isTarget:r,getState:l,setState:a,isValidConnection:i,edgeUpdaterType:s,onReconnectEnd:d}){let c,u;let g=T(e.target),{connectionMode:h,domNode:p,autoPanOnConnect:m,connectionRadius:f,onConnectStart:y,panBy:x,getNodes:b,cancelConnection:S}=l(),E=0,{x:v,y:w}=ee(e),M=eH(s,g?.elementFromPoint(v,w)),C=p?.getBoundingClientRect();if(!C||!M)return;let N=ee(e,C),k=!1,A=null,I=!1,R=null,P=function({nodes:e,nodeId:t,handleId:n,handleType:o}){return e.reduce((e,r)=>{if(r[j]){let{handleBounds:l}=r[j],a=[],i=[];l&&(a=eD(r,l,"source",`${t}-${n}-${o}`),i=eD(r,l,"target",`${t}-${n}-${o}`)),e.push(...a,...i)}return e},[])}({nodes:b(),nodeId:n,handleId:t,handleType:M}),_=()=>{if(!m)return;let[e,t]=L(N,C);x({x:e,y:t}),E=requestAnimationFrame(_)};function $(e){var o,s;let d;let{transform:p}=l();N=ee(e,C);let{handle:m,validHandleResult:y}=function(e,t,n,o,r,l){let{x:a,y:i}=ee(e),s=t.elementsFromPoint(a,i).find(e=>e.classList.contains("react-flow__handle"));if(s){let e=s.getAttribute("data-nodeid");if(e){let t=eH(void 0,s),o=s.getAttribute("data-handleid"),a=l({nodeId:e,id:o,type:t});if(a){let l=r.find(n=>n.nodeId===e&&n.type===t&&n.id===o);return{handle:{id:o,type:t,nodeId:e,x:l?.x||n.x,y:l?.y||n.y},validHandleResult:a}}}}let d=[],c=1/0;if(r.forEach(e=>{let t=Math.sqrt((e.x-n.x)**2+(e.y-n.y)**2);if(t<=o){let n=l(e);t<=c&&(t<c?d=[{handle:e,validHandleResult:n}]:t===c&&d.push({handle:e,validHandleResult:n}),c=t)}}),!d.length)return{handle:null,validHandleResult:eL()};if(1===d.length)return d[0];let u=d.some(({validHandleResult:e})=>e.isValid),g=d.some(({handle:e})=>"target"===e.type);return d.find(({handle:e,validHandleResult:t})=>g?"target"===e.type:!u||t.isValid)||d[0]}(e,g,eA(N,p,!1,[1,1]),f,P,e=>eT(e,h,n,t,r?"target":"source",i,g));if(c=m,k||(_(),k=!0),R=y.handleDomNode,A=y.connection,I=y.isValid,a({connectionPosition:c&&I?eI({x:c.x,y:c.y},p):N,connectionStatus:(o=!!c,d=null,(s=I)?d="valid":o&&!s&&(d="invalid"),d),connectionEndHandle:y.endHandle}),!c&&!I&&!R)return eF(u);A.source!==A.target&&R&&(eF(u),u=R,R.classList.add("connecting","react-flow__handle-connecting"),R.classList.toggle("valid",I),R.classList.toggle("react-flow__handle-valid",I))}function O(e){(c||R)&&A&&I&&o?.(A),l().onConnectEnd?.(e),s&&d?.(e),eF(u),S(),cancelAnimationFrame(E),k=!1,I=!1,A=null,R=null,g.removeEventListener("mousemove",$),g.removeEventListener("mouseup",O),g.removeEventListener("touchmove",$),g.removeEventListener("touchend",O)}a({connectionPosition:N,connectionStatus:null,connectionNodeId:n,connectionHandleId:t,connectionHandleType:M,connectionStartHandle:{nodeId:n,handleId:t,type:M},connectionEndHandle:null}),y?.(e,{nodeId:n,handleId:t,handleType:M}),g.addEventListener("mousemove",$),g.addEventListener("mouseup",O),g.addEventListener("touchmove",$),g.addEventListener("touchend",O)}let eX=()=>!0,eV=e=>({connectionStartHandle:e.connectionStartHandle,connectOnClick:e.connectOnClick,noPanClassName:e.noPanClassName}),eK=(e,t,n)=>o=>{let{connectionStartHandle:r,connectionEndHandle:l,connectionClickStartHandle:a}=o;return{connecting:r?.nodeId===e&&r?.handleId===t&&r?.type===n||l?.nodeId===e&&l?.handleId===t&&l?.type===n,clickConnecting:a?.nodeId===e&&a?.handleId===t&&a?.type===n}},eY=(0,m.forwardRef)(({type:e="source",position:t=p.Top,isValidConnection:n,isConnectable:o=!0,isConnectableStart:r=!0,isConnectableEnd:l=!0,id:a,onConnect:i,children:s,className:d,onMouseDown:c,onTouchStart:u,...g},h)=>{let y=a||null,b="target"===e,S=I(),E=ev(),{connectOnClick:v,noPanClassName:w}=A(eV,x.X),{connecting:M,clickConnecting:C}=A(eK(E,y,e),x.X);E||S.getState().onError?.("010",N.error010());let k=e=>{let{defaultEdgeOptions:t,onConnect:n,hasDefaultEdges:o}=S.getState(),r={...t,...e};if(o){let{edges:e,setEdges:t}=S.getState();t(ek(r,e))}n?.(r),i?.(r)},R=e=>{if(!E)return;let t=J(e);r&&(t&&0===e.button||!t)&&eZ({event:e,handleId:y,nodeId:E,onConnect:k,isTarget:b,getState:S.getState,setState:S.setState,isValidConnection:n||S.getState().isValidConnection||eX}),t?c?.(e):u?.(e)};return m.createElement("div",{"data-handleid":y,"data-nodeid":E,"data-handlepos":t,"data-id":`${E}-${y}-${e}`,className:(0,f.Z)(["react-flow__handle",`react-flow__handle-${t}`,"nodrag",w,d,{source:!b,target:b,connectable:o,connectablestart:r,connectableend:l,connecting:C,connectionindicator:o&&(r&&!M||l&&M)}]),onMouseDown:R,onTouchStart:R,onClick:v?t=>{let{onClickConnectStart:o,onClickConnectEnd:l,connectionClickStartHandle:a,connectionMode:i,isValidConnection:s}=S.getState();if(!E||!a&&!r)return;if(!a){o?.(t,{nodeId:E,handleId:y,handleType:e}),S.setState({connectionClickStartHandle:{nodeId:E,type:e,handleId:y}});return}let d=T(t.target),c=n||s||eX,{connection:u,isValid:g}=eT({nodeId:E,id:y,type:e},i,a.nodeId,a.handleId||null,a.type,c,d);g&&k(u),l?.(t),S.setState({connectionClickStartHandle:null})}:void 0,ref:h,...g},s)});eY.displayName="Handle";var eW=(0,m.memo)(eY);let ej=({data:e,isConnectable:t,targetPosition:n=p.Top,sourcePosition:o=p.Bottom})=>m.createElement(m.Fragment,null,m.createElement(eW,{type:"target",position:n,isConnectable:t}),e?.label,m.createElement(eW,{type:"source",position:o,isConnectable:t}));ej.displayName="DefaultNode";var eU=(0,m.memo)(ej);let eq=({data:e,isConnectable:t,sourcePosition:n=p.Bottom})=>m.createElement(m.Fragment,null,e?.label,m.createElement(eW,{type:"source",position:n,isConnectable:t}));eq.displayName="InputNode";var eG=(0,m.memo)(eq);let eQ=({data:e,isConnectable:t,targetPosition:n=p.Top})=>m.createElement(m.Fragment,null,m.createElement(eW,{type:"target",position:n,isConnectable:t}),e?.label);eQ.displayName="OutputNode";var eJ=(0,m.memo)(eQ);let e0=()=>null;e0.displayName="GroupNode";let e1=e=>({selectedNodes:e.getNodes().filter(e=>e.selected),selectedEdges:e.edges.filter(e=>e.selected).map(e=>({...e}))}),e2=e=>e.id;function e5(e,t){return(0,x.X)(e.selectedNodes.map(e2),t.selectedNodes.map(e2))&&(0,x.X)(e.selectedEdges.map(e2),t.selectedEdges.map(e2))}let e3=(0,m.memo)(({onSelectionChange:e})=>{let t=I(),{selectedNodes:n,selectedEdges:o}=A(e1,e5);return(0,m.useEffect)(()=>{let r={nodes:n,edges:o};e?.(r),t.getState().onSelectionChange.forEach(e=>e(r))},[n,o,e]),null});e3.displayName="SelectionListener";let e4=e=>!!e.onSelectionChange;function e9({onSelectionChange:e}){let t=A(e4);return e||t?m.createElement(e3,{onSelectionChange:e}):null}let e6=e=>({setNodes:e.setNodes,setEdges:e.setEdges,setDefaultNodesAndEdges:e.setDefaultNodesAndEdges,setMinZoom:e.setMinZoom,setMaxZoom:e.setMaxZoom,setTranslateExtent:e.setTranslateExtent,setNodeExtent:e.setNodeExtent,reset:e.reset});function e8(e,t){(0,m.useEffect)(()=>{void 0!==e&&t(e)},[e])}function e7(e,t,n){(0,m.useEffect)(()=>{void 0!==t&&n({[e]:t})},[t])}let te=({nodes:e,edges:t,defaultNodes:n,defaultEdges:o,onConnect:r,onConnectStart:l,onConnectEnd:a,onClickConnectStart:i,onClickConnectEnd:s,nodesDraggable:d,nodesConnectable:c,nodesFocusable:u,edgesFocusable:g,edgesUpdatable:h,elevateNodesOnSelect:p,minZoom:f,maxZoom:y,nodeExtent:b,onNodesChange:S,onEdgesChange:E,elementsSelectable:v,connectionMode:w,snapGrid:M,snapToGrid:C,translateExtent:N,connectOnClick:k,defaultEdgeOptions:R,fitView:P,fitViewOptions:_,onNodesDelete:$,onEdgesDelete:O,onNodeDrag:B,onNodeDragStart:D,onNodeDragStop:z,onSelectionDrag:L,onSelectionDragStart:T,onSelectionDragStop:H,noPanClassName:F,nodeOrigin:Z,rfId:X,autoPanOnConnect:V,autoPanOnNodeDrag:K,onError:Y,connectionRadius:W,isValidConnection:j,nodeDragThreshold:U})=>{let{setNodes:q,setEdges:G,setDefaultNodesAndEdges:Q,setMinZoom:J,setMaxZoom:ee,setTranslateExtent:et,setNodeExtent:en,reset:eo}=A(e6,x.X),er=I();return(0,m.useEffect)(()=>(Q(n,o?.map(e=>({...e,...R}))),()=>{eo()}),[]),e7("defaultEdgeOptions",R,er.setState),e7("connectionMode",w,er.setState),e7("onConnect",r,er.setState),e7("onConnectStart",l,er.setState),e7("onConnectEnd",a,er.setState),e7("onClickConnectStart",i,er.setState),e7("onClickConnectEnd",s,er.setState),e7("nodesDraggable",d,er.setState),e7("nodesConnectable",c,er.setState),e7("nodesFocusable",u,er.setState),e7("edgesFocusable",g,er.setState),e7("edgesUpdatable",h,er.setState),e7("elementsSelectable",v,er.setState),e7("elevateNodesOnSelect",p,er.setState),e7("snapToGrid",C,er.setState),e7("snapGrid",M,er.setState),e7("onNodesChange",S,er.setState),e7("onEdgesChange",E,er.setState),e7("connectOnClick",k,er.setState),e7("fitViewOnInit",P,er.setState),e7("fitViewOnInitOptions",_,er.setState),e7("onNodesDelete",$,er.setState),e7("onEdgesDelete",O,er.setState),e7("onNodeDrag",B,er.setState),e7("onNodeDragStart",D,er.setState),e7("onNodeDragStop",z,er.setState),e7("onSelectionDrag",L,er.setState),e7("onSelectionDragStart",T,er.setState),e7("onSelectionDragStop",H,er.setState),e7("noPanClassName",F,er.setState),e7("nodeOrigin",Z,er.setState),e7("rfId",X,er.setState),e7("autoPanOnConnect",V,er.setState),e7("autoPanOnNodeDrag",K,er.setState),e7("onError",Y,er.setState),e7("connectionRadius",W,er.setState),e7("isValidConnection",j,er.setState),e7("nodeDragThreshold",U,er.setState),e8(e,q),e8(t,G),e8(f,J),e8(y,ee),e8(N,et),e8(b,en),null},tt={display:"none"},tn={position:"absolute",width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0px, 0px, 0px, 0px)",clipPath:"inset(100%)"},to="react-flow__node-desc",tr="react-flow__edge-desc",tl=e=>e.ariaLiveMessage;function ta({rfId:e}){let t=A(tl);return m.createElement("div",{id:`react-flow__aria-live-${e}`,"aria-live":"assertive","aria-atomic":"true",style:tn},t)}function ti({rfId:e,disableKeyboardA11y:t}){return m.createElement(m.Fragment,null,m.createElement("div",{id:`${to}-${e}`,style:tt},"Press enter or space to select a node.",!t&&"You can then use the arrow keys to move the node around."," Press delete to remove it and escape to cancel."," "),m.createElement("div",{id:`${tr}-${e}`,style:tt},"Press enter or space to select an edge. You can then press delete to remove it or escape to cancel."),!t&&m.createElement(ta,{rfId:e}))}var ts=(e=null,t={actInsideInputWithModifier:!0})=>{let[n,o]=(0,m.useState)(!1),r=(0,m.useRef)(!1),l=(0,m.useRef)(new Set([])),[a,i]=(0,m.useMemo)(()=>{if(null!==e){let t=(Array.isArray(e)?e:[e]).filter(e=>"string"==typeof e).map(e=>e.split("+")),n=t.reduce((e,t)=>e.concat(...t),[]);return[t,n]}return[[],[]]},[e]);return(0,m.useEffect)(()=>{let n=t?.target||("undefined"!=typeof document?document:null);if(null!==e){let e=e=>{if(r.current=e.ctrlKey||e.metaKey||e.shiftKey,(!r.current||r.current&&!t.actInsideInputWithModifier)&&Q(e))return!1;let n=tc(e.code,i);l.current.add(e[n]),td(a,l.current,!1)&&(e.preventDefault(),o(!0))},s=e=>{if((!r.current||r.current&&!t.actInsideInputWithModifier)&&Q(e))return!1;let n=tc(e.code,i);td(a,l.current,!0)?(o(!1),l.current.clear()):l.current.delete(e[n]),"Meta"===e.key&&l.current.clear(),r.current=!1},d=()=>{l.current.clear(),o(!1)};return n?.addEventListener("keydown",e),n?.addEventListener("keyup",s),window.addEventListener("blur",d),()=>{n?.removeEventListener("keydown",e),n?.removeEventListener("keyup",s),window.removeEventListener("blur",d)}}},[e,o]),n};function td(e,t,n){return e.filter(e=>n||e.length===t.size).some(e=>e.every(e=>t.has(e)))}function tc(e,t){return t.includes(e)?"code":"key"}function tu(e,t,n){e.forEach(o=>{let r=o.parentNode||o.parentId;if(r&&!e.has(r))throw Error(`Parent node ${r} not found`);if(r||n?.[o.id]){let{x:r,y:l,z:a}=function e(t,n,o,r){let l=t.parentNode||t.parentId;if(!l)return o;let a=n.get(l),i=eR(a,r);return e(a,n,{x:(o.x??0)+i.x,y:(o.y??0)+i.y,z:(a[j]?.z??0)>(o.z??0)?a[j]?.z??0:o.z??0},r)}(o,e,{...o.position,z:o[j]?.z??0},t);o.positionAbsolute={x:r,y:l},o[j].z=a,n?.[o.id]&&(o[j].isParent=!0)}})}function tg(e,t,n,o){let r=new Map,l={},a=o?1e3:0;return e.forEach(e=>{let n=(W(e.zIndex)?e.zIndex:0)+(e.selected?a:0),o=t.get(e.id),i={...e,positionAbsolute:{x:e.position.x,y:e.position.y}},s=e.parentNode||e.parentId;s&&(l[s]=!0),Object.defineProperty(i,j,{enumerable:!1,value:{handleBounds:o?.type&&o?.type!==e.type?void 0:o?.[j]?.handleBounds,z:n}}),r.set(e.id,i)}),tu(r,n,l),r}function th(e,t={}){let{getNodes:n,width:o,height:r,minZoom:l,maxZoom:a,d3Zoom:i,d3Selection:s,fitViewOnInitDone:d,fitViewOnInit:c,nodeOrigin:u}=e(),g=t.initial&&!d&&c;if(i&&s&&(g||!t.initial)){let e=n().filter(e=>{let n=t.includeHiddenNodes?e.width&&e.height:!e.hidden;return t.nodes?.length?n&&t.nodes.some(t=>t.id===e.id):n}),d=e.every(e=>e.width&&e.height);if(e.length>0&&d){let{x:n,y:d,zoom:c}=eO(eP(e,u),o,r,t.minZoom??l,t.maxZoom??a,t.padding??.1),g=b.CR.translate(n,d).scale(c);return"number"==typeof t.duration&&t.duration>0?i.transform(eB(s,t.duration),g):i.transform(s,g),!0}}return!1}function tp({changedNodes:e,changedEdges:t,get:n,set:o}){let{nodeInternals:r,edges:l,onNodesChange:a,onEdgesChange:i,hasDefaultNodes:s,hasDefaultEdges:d}=n();e?.length&&(s&&o({nodeInternals:(e.forEach(e=>{let t=r.get(e.id);t&&r.set(t.id,{...t,[j]:t[j],selected:e.selected})}),new Map(r))}),a?.(e)),t?.length&&(d&&o({edges:l.map(e=>{let n=t.find(t=>t.id===e.id);return n&&(e.selected=n.selected),e})}),i?.(t))}let tm=()=>{},tf={zoomIn:tm,zoomOut:tm,zoomTo:tm,getZoom:()=>1,setViewport:tm,getViewport:()=>({x:0,y:0,zoom:1}),fitView:()=>!1,setCenter:tm,fitBounds:tm,project:e=>e,screenToFlowPosition:e=>e,flowToScreenPosition:e=>e,viewportInitialized:!1},ty=e=>({d3Zoom:e.d3Zoom,d3Selection:e.d3Selection}),tx=()=>{let e=I(),{d3Zoom:t,d3Selection:n}=A(ty,x.X);return(0,m.useMemo)(()=>n&&t?{zoomIn:e=>t.scaleBy(eB(n,e?.duration),1.2),zoomOut:e=>t.scaleBy(eB(n,e?.duration),1/1.2),zoomTo:(e,o)=>t.scaleTo(eB(n,o?.duration),e),getZoom:()=>e.getState().transform[2],setViewport:(o,r)=>{let[l,a,i]=e.getState().transform,s=b.CR.translate(o.x??l,o.y??a).scale(o.zoom??i);t.transform(eB(n,r?.duration),s)},getViewport:()=>{let[t,n,o]=e.getState().transform;return{x:t,y:n,zoom:o}},fitView:t=>th(e.getState,t),setCenter:(o,r,l)=>{let{width:a,height:i,maxZoom:s}=e.getState(),d=void 0!==l?.zoom?l.zoom:s,c=a/2-o*d,u=i/2-r*d,g=b.CR.translate(c,u).scale(d);t.transform(eB(n,l?.duration),g)},fitBounds:(o,r)=>{let{width:l,height:a,minZoom:i,maxZoom:s}=e.getState(),{x:d,y:c,zoom:u}=eO(o,l,a,i,s,r?.padding??.1),g=b.CR.translate(d,c).scale(u);t.transform(eB(n,r?.duration),g)},project:t=>{let{transform:n,snapToGrid:o,snapGrid:r}=e.getState();return console.warn("[DEPRECATED] `project` is deprecated. Instead use `screenToFlowPosition`. There is no need to subtract the react flow bounds anymore! https://reactflow.dev/api-reference/types/react-flow-instance#screen-to-flow-position"),eA(t,n,o,r)},screenToFlowPosition:t=>{let{transform:n,snapToGrid:o,snapGrid:r,domNode:l}=e.getState();if(!l)return t;let{x:a,y:i}=l.getBoundingClientRect();return eA({x:t.x-a,y:t.y-i},n,o,r)},flowToScreenPosition:t=>{let{transform:n,domNode:o}=e.getState();if(!o)return t;let{x:r,y:l}=o.getBoundingClientRect(),a=eI(t,n);return{x:a.x+r,y:a.y+l}},viewportInitialized:!0}:tf,[t,n])};function tb(){let e=tx(),t=I(),n=(0,m.useCallback)(()=>t.getState().getNodes().map(e=>({...e})),[]),o=(0,m.useCallback)(e=>t.getState().nodeInternals.get(e),[]),r=(0,m.useCallback)(()=>{let{edges:e=[]}=t.getState();return e.map(e=>({...e}))},[]),l=(0,m.useCallback)(e=>{let{edges:n=[]}=t.getState();return n.find(t=>t.id===e)},[]),a=(0,m.useCallback)(e=>{let{getNodes:n,setNodes:o,hasDefaultNodes:r,onNodesChange:l}=t.getState(),a=n(),i="function"==typeof e?e(a):e;r?o(i):l&&l(0===i.length?a.map(e=>({type:"remove",id:e.id})):i.map(e=>({item:e,type:"reset"})))},[]),i=(0,m.useCallback)(e=>{let{edges:n=[],setEdges:o,hasDefaultEdges:r,onEdgesChange:l}=t.getState(),a="function"==typeof e?e(n):e;r?o(a):l&&l(0===a.length?n.map(e=>({type:"remove",id:e.id})):a.map(e=>({item:e,type:"reset"})))},[]),s=(0,m.useCallback)(e=>{let n=Array.isArray(e)?e:[e],{getNodes:o,setNodes:r,hasDefaultNodes:l,onNodesChange:a}=t.getState();l?r([...o(),...n]):a&&a(n.map(e=>({item:e,type:"add"})))},[]),d=(0,m.useCallback)(e=>{let n=Array.isArray(e)?e:[e],{edges:o=[],setEdges:r,hasDefaultEdges:l,onEdgesChange:a}=t.getState();l?r([...o,...n]):a&&a(n.map(e=>({item:e,type:"add"})))},[]),c=(0,m.useCallback)(()=>{let{getNodes:e,edges:n=[],transform:o}=t.getState(),[r,l,a]=o;return{nodes:e().map(e=>({...e})),edges:n.map(e=>({...e})),viewport:{x:r,y:l,zoom:a}}},[]),u=(0,m.useCallback)(({nodes:e,edges:n})=>{let{nodeInternals:o,getNodes:r,edges:l,hasDefaultNodes:a,hasDefaultEdges:i,onNodesDelete:s,onEdgesDelete:d,onNodesChange:c,onEdgesChange:u}=t.getState(),g=(e||[]).map(e=>e.id),h=(n||[]).map(e=>e.id),p=r().reduce((e,t)=>{let n=t.parentNode||t.parentId,o=!g.includes(t.id)&&n&&e.find(e=>e.id===n);return("boolean"!=typeof t.deletable||t.deletable)&&(g.includes(t.id)||o)&&e.push(t),e},[]),m=l.filter(e=>"boolean"!=typeof e.deletable||e.deletable),f=m.filter(e=>h.includes(e.id));if(p||f){let e=[...f,...e$(p,m)],n=e.reduce((e,t)=>(e.includes(t.id)||e.push(t.id),e),[]);(i||a)&&(i&&t.setState({edges:l.filter(e=>!n.includes(e.id))}),a&&(p.forEach(e=>{o.delete(e.id)}),t.setState({nodeInternals:new Map(o)}))),n.length>0&&(d?.(e),u&&u(n.map(e=>({id:e,type:"remove"})))),p.length>0&&(s?.(p),c&&c(p.map(e=>({id:e.id,type:"remove"}))))}},[]),g=(0,m.useCallback)(e=>{let n=Y(e),o=n?null:t.getState().nodeInternals.get(e.id);return n||o?[n?e:X(o),o,n]:[null,null,n]},[]),h=(0,m.useCallback)((e,n=!0,o)=>{let[r,l,a]=g(e);return r?(o||t.getState().getNodes()).filter(e=>{if(!a&&(e.id===l.id||!e.positionAbsolute))return!1;let t=K(X(e),r);return n&&t>0||t>=r.width*r.height}):[]},[]),p=(0,m.useCallback)((e,t,n=!0)=>{let[o]=g(e);if(!o)return!1;let r=K(o,t);return n&&r>0||r>=o.width*o.height},[]);return(0,m.useMemo)(()=>({...e,getNodes:n,getNode:o,getEdges:r,getEdge:l,setNodes:a,setEdges:i,addNodes:s,addEdges:d,toObject:c,deleteElements:u,getIntersectingNodes:h,isNodeIntersecting:p}),[e,n,o,r,l,a,i,s,d,c,u,h,p])}let tS={actInsideInputWithModifier:!1};var tE=({deleteKeyCode:e,multiSelectionKeyCode:t})=>{let n=I(),{deleteElements:o}=tb(),r=ts(e,tS),l=ts(t);(0,m.useEffect)(()=>{if(r){let{edges:e,getNodes:t}=n.getState();o({nodes:t().filter(e=>e.selected),edges:e.filter(e=>e.selected)}),n.setState({nodesSelectionActive:!1})}},[r]),(0,m.useEffect)(()=>{n.setState({multiSelectionActive:l})},[l])};let tv={position:"absolute",width:"100%",height:"100%",top:0,left:0},tw=(e,t)=>e.x!==t.x||e.y!==t.y||e.zoom!==t.k,tM=e=>({x:e.x,y:e.y,zoom:e.k}),tC=(e,t)=>e.target.closest(`.${t}`),tN=(e,t)=>2===t&&Array.isArray(e)&&e.includes(2),tk=e=>{let t=e.ctrlKey&&et()?10:1;return-e.deltaY*(1===e.deltaMode?.05:e.deltaMode?1:.002)*t},tA=e=>({d3Zoom:e.d3Zoom,d3Selection:e.d3Selection,d3ZoomHandler:e.d3ZoomHandler,userSelectionActive:e.userSelectionActive}),tI=({onMove:e,onMoveStart:t,onMoveEnd:n,onPaneContextMenu:o,zoomOnScroll:r=!0,zoomOnPinch:l=!0,panOnScroll:a=!1,panOnScrollSpeed:i=.5,panOnScrollMode:s=c.Free,zoomOnDoubleClick:d=!0,elementsSelectable:u,panOnDrag:g=!0,defaultViewport:h,translateExtent:p,minZoom:f,maxZoom:y,zoomActivationKeyCode:v,preventScrolling:w=!0,children:M,noWheelClassName:C,noPanClassName:k})=>{let R=(0,m.useRef)(),P=I(),_=(0,m.useRef)(!1),$=(0,m.useRef)(!1),D=(0,m.useRef)(null),z=(0,m.useRef)({x:0,y:0,zoom:0}),{d3Zoom:L,d3Selection:T,d3ZoomHandler:H,userSelectionActive:F}=A(tA,x.X),Z=ts(v),X=(0,m.useRef)(0),V=(0,m.useRef)(!1),K=(0,m.useRef)();return!function(e){let t=I();(0,m.useEffect)(()=>{let n;let o=()=>{if(!e.current)return;let n=O(e.current);(0===n.height||0===n.width)&&t.getState().onError?.("004",N.error004()),t.setState({width:n.width||500,height:n.height||500})};return o(),window.addEventListener("resize",o),e.current&&(n=new ResizeObserver(()=>o())).observe(e.current),()=>{window.removeEventListener("resize",o),n&&e.current&&n.unobserve(e.current)}},[])}(D),(0,m.useEffect)(()=>{if(D.current){let e=D.current.getBoundingClientRect(),t=(0,b.sP)().scaleExtent([f,y]).translateExtent(p),n=(0,S.Z)(D.current).call(t),o=b.CR.translate(h.x,h.y).scale(B(h.zoom,f,y)),r=[[0,0],[e.width,e.height]],l=t.constrain()(o,r,p);t.transform(n,l),t.wheelDelta(tk),P.setState({d3Zoom:t,d3Selection:n,d3ZoomHandler:n.on("wheel.zoom"),transform:[l.x,l.y,l.k],domNode:D.current.closest(".react-flow")})}},[]),(0,m.useEffect)(()=>{T&&L&&(!a||Z||F?void 0!==H&&T.on("wheel.zoom",function(e,t){if(!w&&"wheel"===e.type&&!e.ctrlKey||tC(e,C))return null;e.preventDefault(),H.call(this,e,t)},{passive:!1}):T.on("wheel.zoom",o=>{if(tC(o,C))return!1;o.preventDefault(),o.stopImmediatePropagation();let r=T.property("__zoom").k||1;if(o.ctrlKey&&l){let e=(0,E.Z)(o),t=tk(o);L.scaleTo(T,r*Math.pow(2,t),e,o);return}let a=1===o.deltaMode?20:1,d=s===c.Vertical?0:o.deltaX*a,u=s===c.Horizontal?0:o.deltaY*a;!et()&&o.shiftKey&&s!==c.Vertical&&(d=o.deltaY*a,u=0),L.translateBy(T,-(d/r)*i,-(u/r)*i,{internal:!0});let g=tM(T.property("__zoom")),{onViewportChangeStart:h,onViewportChange:p,onViewportChangeEnd:m}=P.getState();clearTimeout(K.current),V.current||(V.current=!0,t?.(o,g),h?.(g)),V.current&&(e?.(o,g),p?.(g),K.current=setTimeout(()=>{n?.(o,g),m?.(g),V.current=!1},150))},{passive:!1}))},[F,a,s,T,L,H,Z,l,w,C,t,e,n]),(0,m.useEffect)(()=>{L&&L.on("start",e=>{if(!e.sourceEvent||e.sourceEvent.internal)return null;X.current=e.sourceEvent?.button;let{onViewportChangeStart:n}=P.getState(),o=tM(e.transform);_.current=!0,z.current=o,e.sourceEvent?.type==="mousedown"&&P.setState({paneDragging:!0}),n?.(o),t?.(e.sourceEvent,o)})},[L,t]),(0,m.useEffect)(()=>{L&&(F&&!_.current?L.on("zoom",null):F||L.on("zoom",t=>{let{onViewportChange:n}=P.getState();if(P.setState({transform:[t.transform.x,t.transform.y,t.transform.k]}),$.current=!!(o&&tN(g,X.current??0)),(e||n)&&!t.sourceEvent?.internal){let o=tM(t.transform);n?.(o),e?.(t.sourceEvent,o)}}))},[F,L,e,g,o]),(0,m.useEffect)(()=>{L&&L.on("end",e=>{if(!e.sourceEvent||e.sourceEvent.internal)return null;let{onViewportChangeEnd:t}=P.getState();if(_.current=!1,P.setState({paneDragging:!1}),o&&tN(g,X.current??0)&&!$.current&&o(e.sourceEvent),$.current=!1,(n||t)&&tw(z.current,e.transform)){let o=tM(e.transform);z.current=o,clearTimeout(R.current),R.current=setTimeout(()=>{t?.(o),n?.(e.sourceEvent,o)},a?150:0)}})},[L,a,g,n,o]),(0,m.useEffect)(()=>{L&&L.filter(e=>{let t=Z||r,n=l&&e.ctrlKey;if((!0===g||Array.isArray(g)&&g.includes(1))&&1===e.button&&"mousedown"===e.type&&(tC(e,"react-flow__node")||tC(e,"react-flow__edge")))return!0;if(!g&&!t&&!a&&!d&&!l||F||!d&&"dblclick"===e.type||tC(e,C)&&"wheel"===e.type||tC(e,k)&&("wheel"!==e.type||a&&"wheel"===e.type&&!Z)||!l&&e.ctrlKey&&"wheel"===e.type||!t&&!a&&!n&&"wheel"===e.type||!g&&("mousedown"===e.type||"touchstart"===e.type)||Array.isArray(g)&&!g.includes(e.button)&&"mousedown"===e.type)return!1;let o=Array.isArray(g)&&g.includes(e.button)||!e.button||e.button<=1;return(!e.ctrlKey||"wheel"===e.type)&&o})},[F,L,r,l,a,d,g,u,Z]),m.createElement("div",{className:"react-flow__renderer",ref:D,style:tv},M)},tR=e=>({userSelectionActive:e.userSelectionActive,userSelectionRect:e.userSelectionRect});function tP(){let{userSelectionActive:e,userSelectionRect:t}=A(tR,x.X);return e&&t?m.createElement("div",{className:"react-flow__selection react-flow__container",style:{width:t.width,height:t.height,transform:`translate(${t.x}px, ${t.y}px)`}}):null}function t_(e,t){let n=t.parentNode||t.parentId,o=e.find(e=>e.id===n);if(o){let e=t.position.x+t.width-o.width,n=t.position.y+t.height-o.height;if(e>0||n>0||t.position.x<0||t.position.y<0){if(o.style={...o.style},o.style.width=o.style.width??o.width,o.style.height=o.style.height??o.height,e>0&&(o.style.width+=e),n>0&&(o.style.height+=n),t.position.x<0){let e=Math.abs(t.position.x);o.position.x=o.position.x-e,o.style.width+=e,t.position.x=0}if(t.position.y<0){let e=Math.abs(t.position.y);o.position.y=o.position.y-e,o.style.height+=e,t.position.y=0}o.width=o.style.width,o.height=o.style.height}}}function t$(e,t){if(e.some(e=>"reset"===e.type))return e.filter(e=>"reset"===e.type).map(e=>e.item);let n=e.filter(e=>"add"===e.type).map(e=>e.item);return t.reduce((t,n)=>{let o=e.filter(e=>e.id===n.id);if(0===o.length)return t.push(n),t;let r={...n};for(let e of o)if(e)switch(e.type){case"select":r.selected=e.selected;break;case"position":void 0!==e.position&&(r.position=e.position),void 0!==e.positionAbsolute&&(r.positionAbsolute=e.positionAbsolute),void 0!==e.dragging&&(r.dragging=e.dragging),r.expandParent&&t_(t,r);break;case"dimensions":void 0!==e.dimensions&&(r.width=e.dimensions.width,r.height=e.dimensions.height),void 0!==e.updateStyle&&(r.style={...r.style||{},...e.dimensions}),"boolean"==typeof e.resizing&&(r.resizing=e.resizing),r.expandParent&&t_(t,r);break;case"remove":return t}return t.push(r),t},n)}function tO(e,t){return t$(e,t)}function tB(e,t){return t$(e,t)}let tD=(e,t)=>({id:e,type:"select",selected:t});function tz(e,t){return e.reduce((e,n)=>{let o=t.includes(n.id);return!n.selected&&o?(n.selected=!0,e.push(tD(n.id,!0))):n.selected&&!o&&(n.selected=!1,e.push(tD(n.id,!1))),e},[])}let tL=(e,t)=>n=>{n.target===t.current&&e?.(n)},tT=e=>({userSelectionActive:e.userSelectionActive,elementsSelectable:e.elementsSelectable,dragging:e.paneDragging}),tH=(0,m.memo)(({isSelecting:e,selectionMode:t=u.Full,panOnDrag:n,onSelectionStart:o,onSelectionEnd:r,onPaneClick:l,onPaneContextMenu:a,onPaneScroll:i,onPaneMouseEnter:s,onPaneMouseMove:d,onPaneMouseLeave:c,children:g})=>{let h=(0,m.useRef)(null),p=I(),y=(0,m.useRef)(0),b=(0,m.useRef)(0),S=(0,m.useRef)(),{userSelectionActive:E,elementsSelectable:v,dragging:w}=A(tT,x.X),M=()=>{p.setState({userSelectionActive:!1,userSelectionRect:null}),y.current=0,b.current=0},C=e=>{l?.(e),p.getState().resetSelectedElements(),p.setState({nodesSelectionActive:!1})},N=v&&(e||E);return m.createElement("div",{className:(0,f.Z)(["react-flow__pane",{dragging:w,selection:e}]),onClick:N?void 0:tL(C,h),onContextMenu:tL(e=>{if(Array.isArray(n)&&n?.includes(2)){e.preventDefault();return}a?.(e)},h),onWheel:tL(i?e=>i(e):void 0,h),onMouseEnter:N?void 0:s,onMouseDown:N?t=>{let{resetSelectedElements:n,domNode:r}=p.getState();if(S.current=r?.getBoundingClientRect(),!v||!e||0!==t.button||t.target!==h.current||!S.current)return;let{x:l,y:a}=ee(t,S.current);n(),p.setState({userSelectionRect:{width:0,height:0,startX:l,startY:a,x:l,y:a}}),o?.(t)}:void 0,onMouseMove:N?n=>{let{userSelectionRect:o,nodeInternals:r,edges:l,transform:a,onNodesChange:i,onEdgesChange:s,nodeOrigin:d,getNodes:c}=p.getState();if(!e||!S.current||!o)return;p.setState({userSelectionActive:!0,nodesSelectionActive:!1});let g=ee(n,S.current),h=o.startX??0,m=o.startY??0,f={...o,x:g.x<h?g.x:h,y:g.y<m?g.y:m,width:Math.abs(g.x-h),height:Math.abs(g.y-m)},x=c(),E=e_(r,f,a,t===u.Partial,!0,d),v=e$(E,l).map(e=>e.id),w=E.map(e=>e.id);if(y.current!==w.length){y.current=w.length;let e=tz(x,w);e.length&&i?.(e)}if(b.current!==v.length){b.current=v.length;let e=tz(l,v);e.length&&s?.(e)}p.setState({userSelectionRect:f})}:d,onMouseUp:N?e=>{if(0!==e.button)return;let{userSelectionRect:t}=p.getState();!E&&t&&e.target===h.current&&C?.(e),p.setState({nodesSelectionActive:y.current>0}),M(),r?.(e)}:void 0,onMouseLeave:N?e=>{E&&(p.setState({nodesSelectionActive:y.current>0}),r?.(e)),M()}:c,ref:h,style:tv},g,m.createElement(tP,null))});function tF(e,t,n){let o=e;do{if(o?.matches(t))return!0;if(o===n.current)break;o=o.parentElement}while(o);return!1}function tZ(e,t,n,o,r=[0,0],l){var a;let i=(a=e.extent||o)&&"parent"!==a?[a[0],[a[1][0]-(e.width||0),a[1][1]-(e.height||0)]]:a,s=i,d=e.parentNode||e.parentId;if("parent"!==e.extent||e.expandParent){if(e.extent&&d&&"parent"!==e.extent){let{x:t,y:o}=eR(n.get(d),r).positionAbsolute;s=[[e.extent[0][0]+t,e.extent[0][1]+o],[e.extent[1][0]+t,e.extent[1][1]+o]]}}else if(d&&e.width&&e.height){let t=n.get(d),{x:o,y:l}=eR(t,r).positionAbsolute;s=t&&W(o)&&W(l)&&W(t.width)&&W(t.height)?[[o+e.width*r[0],l+e.height*r[1]],[o+t.width-e.width+e.width*r[0],l+t.height-e.height+e.height*r[1]]]:s}else l?.("005",N.error005()),s=i;let c={x:0,y:0};d&&(c=eR(n.get(d),r).positionAbsolute);let u=s&&"parent"!==s?D(t,s):t;return{position:{x:u.x-c.x,y:u.y-c.y},positionAbsolute:u}}function tX({nodeId:e,dragItems:t,nodeInternals:n}){let o=t.map(e=>({...n.get(e.id),position:e.position,positionAbsolute:e.positionAbsolute}));return[e?o.find(t=>t.id===e):o[0],o]}tH.displayName="Pane";let tV=(e,t,n,o)=>{let r=t.querySelectorAll(e);if(!r||!r.length)return null;let l=Array.from(r),a=t.getBoundingClientRect(),i={x:a.width*o[0],y:a.height*o[1]};return l.map(e=>{let t=e.getBoundingClientRect();return{id:e.getAttribute("data-handleid"),position:e.getAttribute("data-handlepos"),x:(t.left-a.left-i.x)/n,y:(t.top-a.top-i.y)/n,...O(e)}})};function tK(e,t,n){return void 0===n?n:o=>{let r=t().nodeInternals.get(e);r&&n(o,{...r})}}function tY({id:e,store:t,unselect:n=!1,nodeRef:o}){let{addSelectedNodes:r,unselectNodesAndEdges:l,multiSelectionActive:a,nodeInternals:i,onError:s}=t.getState(),d=i.get(e);if(!d){s?.("012",N.error012(e));return}t.setState({nodesSelectionActive:!1}),d.selected?(n||d.selected&&a)&&(l({nodes:[d],edges:[]}),requestAnimationFrame(()=>o?.current?.blur())):r([e])}function tW(e){return(t,n,o)=>e?.(t,o)}function tj({nodeRef:e,disabled:t=!1,noDragClassName:n,handleSelector:o,nodeId:r,isSelectable:l,selectNodesOnDrag:a}){let i=I(),[s,d]=(0,m.useState)(!1),c=(0,m.useRef)([]),u=(0,m.useRef)({x:null,y:null}),g=(0,m.useRef)(0),h=(0,m.useRef)(null),p=(0,m.useRef)({x:0,y:0}),f=(0,m.useRef)(null),y=(0,m.useRef)(!1),x=(0,m.useRef)(!1),b=(0,m.useRef)(!1),E=function(){let e=I();return(0,m.useCallback)(({sourceEvent:t})=>{let{transform:n,snapGrid:o,snapToGrid:r}=e.getState(),l=t.touches?t.touches[0].clientX:t.clientX,a=t.touches?t.touches[0].clientY:t.clientY,i={x:(l-n[0])/n[2],y:(a-n[1])/n[2]};return{xSnapped:r?o[0]*Math.round(i.x/o[0]):i.x,ySnapped:r?o[1]*Math.round(i.y/o[1]):i.y,...i}},[])}();return(0,m.useEffect)(()=>{if(e?.current){let s=(0,S.Z)(e.current),m=({x:e,y:t})=>{let{nodeInternals:n,onNodeDrag:o,onSelectionDrag:l,updateNodePositions:a,nodeExtent:s,snapGrid:g,snapToGrid:h,nodeOrigin:p,onError:m}=i.getState();u.current={x:e,y:t};let y=!1,x={x:0,y:0,x2:0,y2:0};if(c.current.length>1&&s&&(x=F(eP(c.current,p))),c.current=c.current.map(o=>{let r={x:e-o.distance.x,y:t-o.distance.y};h&&(r.x=g[0]*Math.round(r.x/g[0]),r.y=g[1]*Math.round(r.y/g[1]));let l=[[s[0][0],s[0][1]],[s[1][0],s[1][1]]];c.current.length>1&&s&&!o.extent&&(l[0][0]=o.positionAbsolute.x-x.x+s[0][0],l[1][0]=o.positionAbsolute.x+(o.width??0)-x.x2+s[1][0],l[0][1]=o.positionAbsolute.y-x.y+s[0][1],l[1][1]=o.positionAbsolute.y+(o.height??0)-x.y2+s[1][1]);let a=tZ(o,r,n,l,p,m);return y=y||o.position.x!==a.position.x||o.position.y!==a.position.y,o.position=a.position,o.positionAbsolute=a.positionAbsolute,o}),!y)return;a(c.current,!0,!0),d(!0);let b=r?o:tW(l);if(b&&f.current){let[e,t]=tX({nodeId:r,dragItems:c.current,nodeInternals:n});b(f.current,e,t)}},w=()=>{if(!h.current)return;let[e,t]=L(p.current,h.current);if(0!==e||0!==t){let{transform:n,panBy:o}=i.getState();u.current.x=(u.current.x??0)-e/n[2],u.current.y=(u.current.y??0)-t/n[2],o({x:e,y:t})&&m(u.current)}g.current=requestAnimationFrame(w)},M=t=>{let{nodeInternals:n,multiSelectionActive:o,nodesDraggable:s,unselectNodesAndEdges:d,onNodeDragStart:g,onSelectionDragStart:h}=i.getState();x.current=!0;let p=r?g:tW(h);a&&l||o||!r||n.get(r)?.selected||d(),r&&l&&a&&tY({id:r,store:i,nodeRef:e});let m=E(t);if(u.current=m,c.current=Array.from(n.values()).filter(e=>(e.selected||e.id===r)&&(!e.parentNode||e.parentId||!function e(t,n){let o=t.parentNode||t.parentId;if(!o)return!1;let r=n.get(o);return!!r&&(!!r.selected||e(r,n))}(e,n))&&(e.draggable||s&&void 0===e.draggable)).map(e=>({id:e.id,position:e.position||{x:0,y:0},positionAbsolute:e.positionAbsolute||{x:0,y:0},distance:{x:m.x-(e.positionAbsolute?.x??0),y:m.y-(e.positionAbsolute?.y??0)},delta:{x:0,y:0},extent:e.extent,parentNode:e.parentNode||e.parentId,parentId:e.parentNode||e.parentId,width:e.width,height:e.height,expandParent:e.expandParent})),p&&c.current){let[e,o]=tX({nodeId:r,dragItems:c.current,nodeInternals:n});p(t.sourceEvent,e,o)}};if(t)s.on(".drag",null);else{let t=(0,v.Z)().on("start",e=>{let{domNode:t,nodeDragThreshold:n}=i.getState();0===n&&M(e),b.current=!1;let o=E(e);u.current=o,h.current=t?.getBoundingClientRect()||null,p.current=ee(e.sourceEvent,h.current)}).on("drag",e=>{let t=E(e),{autoPanOnNodeDrag:n,nodeDragThreshold:o}=i.getState();if("touchmove"===e.sourceEvent.type&&e.sourceEvent.touches.length>1&&(b.current=!0),!b.current){if(!y.current&&x.current&&n&&(y.current=!0,w()),!x.current){let n=t.xSnapped-(u?.current?.x??0),r=t.ySnapped-(u?.current?.y??0);Math.sqrt(n*n+r*r)>o&&M(e)}(u.current.x!==t.xSnapped||u.current.y!==t.ySnapped)&&c.current&&x.current&&(f.current=e.sourceEvent,p.current=ee(e.sourceEvent,h.current),m(t))}}).on("end",e=>{if(x.current&&!b.current&&(d(!1),y.current=!1,x.current=!1,cancelAnimationFrame(g.current),c.current)){let{updateNodePositions:t,nodeInternals:n,onNodeDragStop:o,onSelectionDragStop:l}=i.getState(),a=r?o:tW(l);if(t(c.current,!1,!1),a){let[t,o]=tX({nodeId:r,dragItems:c.current,nodeInternals:n});a(e.sourceEvent,t,o)}}}).filter(t=>{let r=t.target;return!t.button&&(!n||!tF(r,`.${n}`,e))&&(!o||tF(r,o,e))});return s.call(t),()=>{s.on(".drag",null)}}}},[e,t,n,o,l,i,r,a,E]),s}function tU(){let e=I();return(0,m.useCallback)(t=>{let{nodeInternals:n,nodeExtent:o,updateNodePositions:r,getNodes:l,snapToGrid:a,snapGrid:i,onError:s,nodesDraggable:d}=e.getState(),c=l().filter(e=>e.selected&&(e.draggable||d&&void 0===e.draggable)),u=a?i[0]:5,g=a?i[1]:5,h=t.isShiftPressed?4:1,p=t.x*u*h,m=t.y*g*h;r(c.map(e=>{if(e.positionAbsolute){let t={x:e.positionAbsolute.x+p,y:e.positionAbsolute.y+m};a&&(t.x=i[0]*Math.round(t.x/i[0]),t.y=i[1]*Math.round(t.y/i[1]));let{positionAbsolute:r,position:l}=tZ(e,t,n,o,void 0,s);e.position=l,e.positionAbsolute=r}return e}),!0,!1)},[])}let tq={ArrowUp:{x:0,y:-1},ArrowDown:{x:0,y:1},ArrowLeft:{x:-1,y:0},ArrowRight:{x:1,y:0}};var tG=e=>{let t=({id:t,type:n,data:o,xPos:r,yPos:l,xPosOrigin:a,yPosOrigin:i,selected:s,onClick:d,onMouseEnter:c,onMouseMove:u,onMouseLeave:g,onContextMenu:h,onDoubleClick:p,style:y,className:x,isDraggable:b,isSelectable:S,isConnectable:E,isFocusable:v,selectNodesOnDrag:w,sourcePosition:M,targetPosition:C,hidden:N,resizeObserver:k,dragHandle:A,zIndex:R,isParent:P,noDragClassName:_,noPanClassName:$,initialized:O,disableKeyboardA11y:B,ariaLabel:D,rfId:z,hasHandleBounds:L})=>{let T=I(),H=(0,m.useRef)(null),F=(0,m.useRef)(null),Z=(0,m.useRef)(M),X=(0,m.useRef)(C),V=(0,m.useRef)(n),K=S||b||d||c||u||g,Y=tU(),W=tK(t,T.getState,c),j=tK(t,T.getState,u),q=tK(t,T.getState,g),G=tK(t,T.getState,h),J=tK(t,T.getState,p);(0,m.useEffect)(()=>()=>{F.current&&(k?.unobserve(F.current),F.current=null)},[]),(0,m.useEffect)(()=>{if(H.current&&!N){let e=H.current;O&&L&&F.current===e||(F.current&&k?.unobserve(F.current),k?.observe(e),F.current=e)}},[N,O,L]),(0,m.useEffect)(()=>{let e=V.current!==n,o=Z.current!==M,r=X.current!==C;H.current&&(e||o||r)&&(e&&(V.current=n),o&&(Z.current=M),r&&(X.current=C),T.getState().updateNodeDimensions([{id:t,nodeElement:H.current,forceUpdate:!0}]))},[t,n,M,C]);let ee=tj({nodeRef:H,disabled:N||!b,noDragClassName:_,handleSelector:A,nodeId:t,isSelectable:S,selectNodesOnDrag:w});return N?null:m.createElement("div",{className:(0,f.Z)(["react-flow__node",`react-flow__node-${n}`,{[$]:b},x,{selected:s,selectable:S,parent:P,dragging:ee}]),ref:H,style:{zIndex:R,transform:`translate(${a}px,${i}px)`,pointerEvents:K?"all":"none",visibility:O?"visible":"hidden",...y},"data-id":t,"data-testid":`rf__node-${t}`,onMouseEnter:W,onMouseMove:j,onMouseLeave:q,onContextMenu:G,onClick:e=>{let{nodeDragThreshold:n}=T.getState();if(S&&(!w||!b||n>0)&&tY({id:t,store:T,nodeRef:H}),d){let n=T.getState().nodeInternals.get(t);n&&d(e,{...n})}},onDoubleClick:J,onKeyDown:v?e=>{!Q(e)&&!B&&(U.includes(e.key)&&S?tY({id:t,store:T,unselect:"Escape"===e.key,nodeRef:H}):b&&s&&Object.prototype.hasOwnProperty.call(tq,e.key)&&(T.setState({ariaLiveMessage:`Moved selected node ${e.key.replace("Arrow","").toLowerCase()}. New position, x: ${~~r}, y: ${~~l}`}),Y({x:tq[e.key].x,y:tq[e.key].y,isShiftPressed:e.shiftKey})))}:void 0,tabIndex:v?0:void 0,role:v?"button":void 0,"aria-describedby":B?void 0:`${to}-${z}`,"aria-label":D},m.createElement(eE,{value:t},m.createElement(e,{id:t,data:o,type:n,xPos:r,yPos:l,selected:s,isConnectable:E,sourcePosition:M,targetPosition:C,dragging:ee,dragHandle:A,zIndex:R})))};return t.displayName="NodeWrapper",(0,m.memo)(t)};let tQ=e=>({...eP(e.getNodes().filter(e=>e.selected),e.nodeOrigin),transformString:`translate(${e.transform[0]}px,${e.transform[1]}px) scale(${e.transform[2]})`,userSelectionActive:e.userSelectionActive});var tJ=(0,m.memo)(function({onSelectionContextMenu:e,noPanClassName:t,disableKeyboardA11y:n}){let o=I(),{width:r,height:l,x:a,y:i,transformString:s,userSelectionActive:d}=A(tQ,x.X),c=tU(),u=(0,m.useRef)(null);return((0,m.useEffect)(()=>{n||u.current?.focus({preventScroll:!0})},[n]),tj({nodeRef:u}),!d&&r&&l)?m.createElement("div",{className:(0,f.Z)(["react-flow__nodesselection","react-flow__container",t]),style:{transform:s}},m.createElement("div",{ref:u,className:"react-flow__nodesselection-rect",onContextMenu:e?t=>{e(t,o.getState().getNodes().filter(e=>e.selected))}:void 0,tabIndex:n?void 0:-1,onKeyDown:n?void 0:e=>{Object.prototype.hasOwnProperty.call(tq,e.key)&&c({x:tq[e.key].x,y:tq[e.key].y,isShiftPressed:e.shiftKey})},style:{width:r,height:l,top:i,left:a}})):null});let t0=e=>e.nodesSelectionActive,t1=({children:e,onPaneClick:t,onPaneMouseEnter:n,onPaneMouseMove:o,onPaneMouseLeave:r,onPaneContextMenu:l,onPaneScroll:a,deleteKeyCode:i,onMove:s,onMoveStart:d,onMoveEnd:c,selectionKeyCode:u,selectionOnDrag:g,selectionMode:h,onSelectionStart:p,onSelectionEnd:f,multiSelectionKeyCode:y,panActivationKeyCode:x,zoomActivationKeyCode:b,elementsSelectable:S,zoomOnScroll:E,zoomOnPinch:v,panOnScroll:w,panOnScrollSpeed:M,panOnScrollMode:C,zoomOnDoubleClick:N,panOnDrag:k,defaultViewport:I,translateExtent:R,minZoom:P,maxZoom:_,preventScrolling:$,onSelectionContextMenu:O,noWheelClassName:B,noPanClassName:D,disableKeyboardA11y:z})=>{let L=A(t0),T=ts(u),H=ts(x),F=H||k,Z=H||w,X=T||g&&!0!==F;return tE({deleteKeyCode:i,multiSelectionKeyCode:y}),m.createElement(tI,{onMove:s,onMoveStart:d,onMoveEnd:c,onPaneContextMenu:l,elementsSelectable:S,zoomOnScroll:E,zoomOnPinch:v,panOnScroll:Z,panOnScrollSpeed:M,panOnScrollMode:C,zoomOnDoubleClick:N,panOnDrag:!T&&F,defaultViewport:I,translateExtent:R,minZoom:P,maxZoom:_,zoomActivationKeyCode:b,preventScrolling:$,noWheelClassName:B,noPanClassName:D},m.createElement(tH,{onSelectionStart:p,onSelectionEnd:f,onPaneClick:t,onPaneMouseEnter:n,onPaneMouseMove:o,onPaneMouseLeave:r,onPaneContextMenu:l,onPaneScroll:a,panOnDrag:F,isSelecting:!!X,selectionMode:h},e,L&&m.createElement(tJ,{onSelectionContextMenu:O,noPanClassName:D,disableKeyboardA11y:z})))};t1.displayName="FlowRenderer";var t2=(0,m.memo)(t1);function t5(e){let t={input:tG(e.input||eG),default:tG(e.default||eU),output:tG(e.output||eJ),group:tG(e.group||e0)},n=Object.keys(e).filter(e=>!["input","default","output","group"].includes(e)).reduce((t,n)=>(t[n]=tG(e[n]||eU),t),{});return{...t,...n}}let t3=({x:e,y:t,width:n,height:o,origin:r})=>!n||!o||r[0]<0||r[1]<0||r[0]>1||r[1]>1?{x:e,y:t}:{x:e-n*r[0],y:t-o*r[1]},t4=e=>({nodesDraggable:e.nodesDraggable,nodesConnectable:e.nodesConnectable,nodesFocusable:e.nodesFocusable,elementsSelectable:e.elementsSelectable,updateNodeDimensions:e.updateNodeDimensions,onError:e.onError}),t9=e=>{var t;let{nodesDraggable:n,nodesConnectable:o,nodesFocusable:r,elementsSelectable:l,updateNodeDimensions:a,onError:i}=A(t4,x.X),s=(t=e.onlyRenderVisibleElements,A((0,m.useCallback)(e=>t?e_(e.nodeInternals,{x:0,y:0,width:e.width,height:e.height},e.transform,!0):e.getNodes(),[t]))),d=(0,m.useRef)(),c=(0,m.useMemo)(()=>{if("undefined"==typeof ResizeObserver)return null;let e=new ResizeObserver(e=>{a(e.map(e=>({id:e.target.getAttribute("data-id"),nodeElement:e.target,forceUpdate:!0})))});return d.current=e,e},[]);return(0,m.useEffect)(()=>()=>{d?.current?.disconnect()},[]),m.createElement("div",{className:"react-flow__nodes",style:tv},s.map(t=>{let a=t.type||"default";e.nodeTypes[a]||(i?.("003",N.error003(a)),a="default");let s=e.nodeTypes[a]||e.nodeTypes.default,d=!!(t.draggable||n&&void 0===t.draggable),u=!!(t.selectable||l&&void 0===t.selectable),g=!!(t.connectable||o&&void 0===t.connectable),h=!!(t.focusable||r&&void 0===t.focusable),f=e.nodeExtent?D(t.positionAbsolute,e.nodeExtent):t.positionAbsolute,y=f?.x??0,x=f?.y??0,b=t3({x:y,y:x,width:t.width??0,height:t.height??0,origin:e.nodeOrigin});return m.createElement(s,{key:t.id,id:t.id,className:t.className,style:t.style,type:a,data:t.data,sourcePosition:t.sourcePosition||p.Bottom,targetPosition:t.targetPosition||p.Top,hidden:t.hidden,xPos:y,yPos:x,xPosOrigin:b.x,yPosOrigin:b.y,selectNodesOnDrag:e.selectNodesOnDrag,onClick:e.onNodeClick,onMouseEnter:e.onNodeMouseEnter,onMouseMove:e.onNodeMouseMove,onMouseLeave:e.onNodeMouseLeave,onContextMenu:e.onNodeContextMenu,onDoubleClick:e.onNodeDoubleClick,selected:!!t.selected,isDraggable:d,isSelectable:u,isConnectable:g,isFocusable:h,resizeObserver:c,dragHandle:t.dragHandle,zIndex:t[j]?.z??0,isParent:!!t[j]?.isParent,noDragClassName:e.noDragClassName,noPanClassName:e.noPanClassName,initialized:!!t.width&&!!t.height,rfId:e.rfId,disableKeyboardA11y:e.disableKeyboardA11y,ariaLabel:t.ariaLabel,hasHandleBounds:!!t[j]?.handleBounds})}))};t9.displayName="NodeRenderer";var t6=(0,m.memo)(t9);let t8=(e,t,n)=>n===p.Left?e-t:n===p.Right?e+t:e,t7=(e,t,n)=>n===p.Top?e-t:n===p.Bottom?e+t:e,ne="react-flow__edgeupdater",nt=({position:e,centerX:t,centerY:n,radius:o=10,onMouseDown:r,onMouseEnter:l,onMouseOut:a,type:i})=>m.createElement("circle",{onMouseDown:r,onMouseEnter:l,onMouseOut:a,className:(0,f.Z)([ne,`${ne}-${i}`]),cx:t8(t,o,e),cy:t7(n,o,e),r:o,stroke:"transparent",fill:"transparent"}),nn=()=>!0;var no=e=>{let t=({id:t,className:n,type:o,data:r,onClick:l,onEdgeDoubleClick:a,selected:i,animated:s,label:d,labelStyle:c,labelShowBg:u,labelBgStyle:g,labelBgPadding:h,labelBgBorderRadius:p,style:y,source:x,target:b,sourceX:S,sourceY:E,targetX:v,targetY:w,sourcePosition:M,targetPosition:C,elementsSelectable:N,hidden:k,sourceHandleId:A,targetHandleId:R,onContextMenu:P,onMouseEnter:_,onMouseMove:$,onMouseLeave:O,reconnectRadius:B,onReconnect:D,onReconnectStart:z,onReconnectEnd:L,markerEnd:T,markerStart:H,rfId:F,ariaLabel:Z,isFocusable:X,isReconnectable:V,pathOptions:K,interactionWidth:Y,disableKeyboardA11y:W})=>{let j=(0,m.useRef)(null),[q,G]=(0,m.useState)(!1),[Q,J]=(0,m.useState)(!1),ee=I(),et=(0,m.useMemo)(()=>`url('#${eC(H,F)}')`,[H,F]),en=(0,m.useMemo)(()=>`url('#${eC(T,F)}')`,[T,F]);if(k)return null;let er=eo(t,ee.getState,a),el=eo(t,ee.getState,P),ea=eo(t,ee.getState,_),ei=eo(t,ee.getState,$),es=eo(t,ee.getState,O),ed=(e,n)=>{if(0!==e.button)return;let{edges:o,isValidConnection:r}=ee.getState(),l=n?b:x,a=(n?R:A)||null,i=n?"target":"source",s=r||nn,d=o.find(e=>e.id===t);J(!0),z?.(e,d,i),eZ({event:e,handleId:a,nodeId:l,onConnect:e=>D?.(d,e),isTarget:n,getState:ee.getState,setState:ee.setState,isValidConnection:s,edgeUpdaterType:i,onReconnectEnd:e=>{J(!1),L?.(e,d,i)}})},ec=()=>G(!0),eu=()=>G(!1);return m.createElement("g",{className:(0,f.Z)(["react-flow__edge",`react-flow__edge-${o}`,n,{selected:i,animated:s,inactive:!N&&!l,updating:q}]),onClick:e=>{let{edges:n,addSelectedEdges:o,unselectNodesAndEdges:r,multiSelectionActive:a}=ee.getState(),i=n.find(e=>e.id===t);i&&(N&&(ee.setState({nodesSelectionActive:!1}),i.selected&&a?(r({nodes:[],edges:[i]}),j.current?.blur()):o([t])),l&&l(e,i))},onDoubleClick:er,onContextMenu:el,onMouseEnter:ea,onMouseMove:ei,onMouseLeave:es,onKeyDown:X?e=>{if(!W&&U.includes(e.key)&&N){let{unselectNodesAndEdges:n,addSelectedEdges:o,edges:r}=ee.getState();"Escape"===e.key?(j.current?.blur(),n({edges:[r.find(e=>e.id===t)]})):o([t])}}:void 0,tabIndex:X?0:void 0,role:X?"button":"img","data-testid":`rf__edge-${t}`,"aria-label":null===Z?void 0:Z||`Edge from ${x} to ${b}`,"aria-describedby":X?`${tr}-${F}`:void 0,ref:j},!Q&&m.createElement(e,{id:t,source:x,target:b,selected:i,animated:s,label:d,labelStyle:c,labelShowBg:u,labelBgStyle:g,labelBgPadding:h,labelBgBorderRadius:p,data:r,style:y,sourceX:S,sourceY:E,targetX:v,targetY:w,sourcePosition:M,targetPosition:C,sourceHandleId:A,targetHandleId:R,markerStart:et,markerEnd:en,pathOptions:K,interactionWidth:Y}),V&&m.createElement(m.Fragment,null,("source"===V||!0===V)&&m.createElement(nt,{position:M,centerX:S,centerY:E,radius:B,onMouseDown:e=>ed(e,!0),onMouseEnter:ec,onMouseOut:eu,type:"source"}),("target"===V||!0===V)&&m.createElement(nt,{position:C,centerX:v,centerY:w,radius:B,onMouseDown:e=>ed(e,!1),onMouseEnter:ec,onMouseOut:eu,type:"target"})))};return t.displayName="EdgeWrapper",(0,m.memo)(t)};function nr(e){let t={default:no(e.default||eb),straight:no(e.bezier||em),step:no(e.step||ep),smoothstep:no(e.step||eh),simplebezier:no(e.simplebezier||es)},n=Object.keys(e).filter(e=>!["default","bezier"].includes(e)).reduce((t,n)=>(t[n]=no(e[n]||eb),t),{});return{...t,...n}}function nl(e,t,n=null){let o=(n?.x||0)+t.x,r=(n?.y||0)+t.y,l=n?.width||t.width,a=n?.height||t.height;switch(e){case p.Top:return{x:o+l/2,y:r};case p.Right:return{x:o+l,y:r+a/2};case p.Bottom:return{x:o+l/2,y:r+a};case p.Left:return{x:o,y:r+a/2}}}function na(e,t){return e?1!==e.length&&t?t&&e.find(e=>e.id===t)||null:e[0]:null}let ni=(e,t,n,o,r,l)=>{let a=nl(n,e,t),i=nl(l,o,r);return{sourceX:a.x,sourceY:a.y,targetX:i.x,targetY:i.y}};function ns(e){let t=e?.[j]?.handleBounds||null,n=t&&e?.width&&e?.height&&void 0!==e?.positionAbsolute?.x&&void 0!==e?.positionAbsolute?.y;return[{x:e?.positionAbsolute?.x||0,y:e?.positionAbsolute?.y||0,width:e?.width||0,height:e?.height||0},t,!!n]}let nd=[{level:0,isMaxLevel:!0,edges:[]}],nc={[h.Arrow]:({color:e="none",strokeWidth:t=1})=>m.createElement("polyline",{style:{stroke:e,strokeWidth:t},strokeLinecap:"round",strokeLinejoin:"round",fill:"none",points:"-5,-4 0,0 -5,4"}),[h.ArrowClosed]:({color:e="none",strokeWidth:t=1})=>m.createElement("polyline",{style:{stroke:e,fill:e,strokeWidth:t},strokeLinecap:"round",strokeLinejoin:"round",points:"-5,-4 0,0 -5,4 -5,-4"})},nu=({id:e,type:t,color:n,width:o=12.5,height:r=12.5,markerUnits:l="strokeWidth",strokeWidth:a,orient:i="auto-start-reverse"})=>{let s=function(e){let t=I();return(0,m.useMemo)(()=>Object.prototype.hasOwnProperty.call(nc,e)?nc[e]:(t.getState().onError?.("009",N.error009(e)),null),[e])}(t);return s?m.createElement("marker",{className:"react-flow__arrowhead",id:e,markerWidth:`${o}`,markerHeight:`${r}`,viewBox:"-10 -10 20 20",markerUnits:l,orient:i,refX:"0",refY:"0"},m.createElement(s,{color:n,strokeWidth:a})):null},ng=({defaultColor:e,rfId:t})=>n=>{let o=[];return n.edges.reduce((n,r)=>([r.markerStart,r.markerEnd].forEach(r=>{if(r&&"object"==typeof r){let l=eC(r,t);o.includes(l)||(n.push({id:l,color:r.color||e,...r}),o.push(l))}}),n),[]).sort((e,t)=>e.id.localeCompare(t.id))},nh=({defaultColor:e,rfId:t})=>{let n=A((0,m.useCallback)(ng({defaultColor:e,rfId:t}),[e,t]),(e,t)=>!(e.length!==t.length||e.some((e,n)=>e.id!==t[n].id)));return m.createElement("defs",null,n.map(e=>m.createElement(nu,{id:e.id,key:e.id,type:e.type,color:e.color,width:e.width,height:e.height,markerUnits:e.markerUnits,strokeWidth:e.strokeWidth,orient:e.orient})))};nh.displayName="MarkerDefinitions";var np=(0,m.memo)(nh);let nm=e=>({nodesConnectable:e.nodesConnectable,edgesFocusable:e.edgesFocusable,edgesUpdatable:e.edgesUpdatable,elementsSelectable:e.elementsSelectable,width:e.width,height:e.height,connectionMode:e.connectionMode,nodeInternals:e.nodeInternals,onError:e.onError}),nf=({defaultMarkerColor:e,onlyRenderVisibleElements:t,elevateEdgesOnSelect:n,rfId:o,edgeTypes:r,noPanClassName:l,onEdgeContextMenu:a,onEdgeMouseEnter:i,onEdgeMouseMove:s,onEdgeMouseLeave:c,onEdgeClick:u,onEdgeDoubleClick:g,onReconnect:h,onReconnectStart:y,onReconnectEnd:b,reconnectRadius:S,children:E,disableKeyboardA11y:v})=>{let{edgesFocusable:w,edgesUpdatable:M,elementsSelectable:C,width:k,height:I,connectionMode:R,nodeInternals:P,onError:_}=A(nm,x.X),$=function(e,t,n=!1){let o=-1,r=Object.entries(e.reduce((e,r)=>{let l=W(r.zIndex),a=l?r.zIndex:0;if(n){let e=t.get(r.target),n=t.get(r.source),o=r.selected||e?.selected||n?.selected,i=Math.max(n?.[j]?.z||0,e?.[j]?.z||0,1e3);a=(l?r.zIndex:0)+(o?i:0)}return e[a]?e[a].push(r):e[a]=[r],o=a>o?a:o,e},{})).map(([e,t])=>{let n=+e;return{edges:t,level:n,isMaxLevel:n===o}});return 0===r.length?nd:r}(A((0,m.useCallback)(e=>t?e.edges.filter(t=>{let n=P.get(t.source),o=P.get(t.target);return n?.width&&n?.height&&o?.width&&o?.height&&function({sourcePos:e,targetPos:t,sourceWidth:n,sourceHeight:o,targetWidth:r,targetHeight:l,width:a,height:i,transform:s}){let d={x:Math.min(e.x,t.x),y:Math.min(e.y,t.y),x2:Math.max(e.x+n,t.x+r),y2:Math.max(e.y+o,t.y+l)};d.x===d.x2&&(d.x2+=1),d.y===d.y2&&(d.y2+=1);let c=F({x:(0-s[0])/s[2],y:(0-s[1])/s[2],width:a/s[2],height:i/s[2]});return Math.ceil(Math.max(0,Math.min(c.x2,d.x2)-Math.max(c.x,d.x))*Math.max(0,Math.min(c.y2,d.y2)-Math.max(c.y,d.y)))>0}({sourcePos:n.positionAbsolute||{x:0,y:0},targetPos:o.positionAbsolute||{x:0,y:0},sourceWidth:n.width,sourceHeight:n.height,targetWidth:o.width,targetHeight:o.height,width:e.width,height:e.height,transform:e.transform})}):e.edges,[t,P])),P,n);return k?m.createElement(m.Fragment,null,$.map(({level:t,edges:n,isMaxLevel:x})=>m.createElement("svg",{key:t,style:{zIndex:t},width:k,height:I,className:"react-flow__edges react-flow__container"},x&&m.createElement(np,{defaultColor:e,rfId:o}),m.createElement("g",null,n.map(e=>{let[t,n,x]=ns(P.get(e.source)),[E,k,A]=ns(P.get(e.target));if(!x||!A)return null;let I=e.type||"default";r[I]||(_?.("011",N.error011(I)),I="default");let $=r[I]||r.default,O=R===d.Strict?k.target:(k.target??[]).concat(k.source??[]),B=na(n.source,e.sourceHandle),D=na(O,e.targetHandle),z=B?.position||p.Bottom,L=D?.position||p.Top,T=!!(e.focusable||w&&void 0===e.focusable),H=e.reconnectable||e.updatable;if(!B||!D)return _?.("008",N.error008(B,e)),null;let{sourceX:F,sourceY:Z,targetX:X,targetY:V}=ni(t,B,z,E,D,L);return m.createElement($,{key:e.id,id:e.id,className:(0,f.Z)([e.className,l]),type:I,data:e.data,selected:!!e.selected,animated:!!e.animated,hidden:!!e.hidden,label:e.label,labelStyle:e.labelStyle,labelShowBg:e.labelShowBg,labelBgStyle:e.labelBgStyle,labelBgPadding:e.labelBgPadding,labelBgBorderRadius:e.labelBgBorderRadius,style:e.style,source:e.source,target:e.target,sourceHandleId:e.sourceHandle,targetHandleId:e.targetHandle,markerEnd:e.markerEnd,markerStart:e.markerStart,sourceX:F,sourceY:Z,targetX:X,targetY:V,sourcePosition:z,targetPosition:L,elementsSelectable:C,onContextMenu:a,onMouseEnter:i,onMouseMove:s,onMouseLeave:c,onClick:u,onEdgeDoubleClick:g,onReconnect:h,onReconnectStart:y,onReconnectEnd:b,reconnectRadius:S,rfId:o,ariaLabel:e.ariaLabel,isFocusable:T,isReconnectable:void 0!==h&&(H||M&&void 0===H),pathOptions:"pathOptions"in e?e.pathOptions:void 0,interactionWidth:e.interactionWidth,disableKeyboardA11y:v})})))),E):null};nf.displayName="EdgeRenderer";var ny=(0,m.memo)(nf);let nx=e=>`translate(${e.transform[0]}px,${e.transform[1]}px) scale(${e.transform[2]})`;function nb({children:e}){let t=A(nx);return m.createElement("div",{className:"react-flow__viewport react-flow__container",style:{transform:t}},e)}let nS={[p.Left]:p.Right,[p.Right]:p.Left,[p.Top]:p.Bottom,[p.Bottom]:p.Top},nE=({nodeId:e,handleType:t,style:n,type:o=g.Bezier,CustomComponent:r,connectionStatus:l})=>{let{fromNode:a,handleId:i,toX:s,toY:c,connectionMode:u}=A((0,m.useCallback)(t=>({fromNode:t.nodeInternals.get(e),handleId:t.connectionHandleId,toX:(t.connectionPosition.x-t.transform[0])/t.transform[2],toY:(t.connectionPosition.y-t.transform[1])/t.transform[2],connectionMode:t.connectionMode}),[e]),x.X),h=a?.[j]?.handleBounds,p=h?.[t];if(u===d.Loose&&(p=p||h?.["source"===t?"target":"source"]),!a||!p)return null;let f=i?p.find(e=>e.id===i):p[0],y=f?f.x+f.width/2:(a.width??0)/2,b=f?f.y+f.height/2:a.height??0,S=(a.positionAbsolute?.x??0)+y,E=(a.positionAbsolute?.y??0)+b,v=f?.position,w=v?nS[v]:null;if(!v||!w)return null;if(r)return m.createElement(r,{connectionLineType:o,connectionLineStyle:n,fromNode:a,fromHandle:f,fromX:S,fromY:E,toX:s,toY:c,fromPosition:v,toPosition:w,connectionStatus:l});let M="",C={sourceX:S,sourceY:E,sourcePosition:v,targetX:s,targetY:c,targetPosition:w};return o===g.Bezier?[M]=ex(C):o===g.Step?[M]=eg({...C,borderRadius:0}):o===g.SmoothStep?[M]=eg(C):o===g.SimpleBezier?[M]=ei(C):M=`M${S},${E} ${s},${c}`,m.createElement("path",{d:M,fill:"none",className:"react-flow__connection-path",style:n})};nE.displayName="ConnectionLine";let nv=e=>({nodeId:e.connectionNodeId,handleType:e.connectionHandleType,nodesConnectable:e.nodesConnectable,connectionStatus:e.connectionStatus,width:e.width,height:e.height});function nw({containerStyle:e,style:t,type:n,component:o}){let{nodeId:r,handleType:l,nodesConnectable:a,width:i,height:s,connectionStatus:d}=A(nv,x.X);return r&&l&&i&&a?m.createElement("svg",{style:e,width:i,height:s,className:"react-flow__edges react-flow__connectionline react-flow__container"},m.createElement("g",{className:(0,f.Z)(["react-flow__connection",d])},m.createElement(nE,{nodeId:r,handleType:l,style:t,type:n,CustomComponent:o,connectionStatus:d}))):null}function nM(e,t){return(0,m.useRef)(null),I(),(0,m.useMemo)(()=>t(e),[e])}let nC=({nodeTypes:e,edgeTypes:t,onMove:n,onMoveStart:o,onMoveEnd:r,onInit:l,onNodeClick:a,onEdgeClick:i,onNodeDoubleClick:s,onEdgeDoubleClick:d,onNodeMouseEnter:c,onNodeMouseMove:u,onNodeMouseLeave:g,onNodeContextMenu:h,onSelectionContextMenu:p,onSelectionStart:f,onSelectionEnd:y,connectionLineType:x,connectionLineStyle:b,connectionLineComponent:S,connectionLineContainerStyle:E,selectionKeyCode:v,selectionOnDrag:w,selectionMode:M,multiSelectionKeyCode:C,panActivationKeyCode:N,zoomActivationKeyCode:k,deleteKeyCode:A,onlyRenderVisibleElements:I,elementsSelectable:R,selectNodesOnDrag:P,defaultViewport:_,translateExtent:$,minZoom:O,maxZoom:B,preventScrolling:D,defaultMarkerColor:z,zoomOnScroll:L,zoomOnPinch:T,panOnScroll:H,panOnScrollSpeed:F,panOnScrollMode:Z,zoomOnDoubleClick:X,panOnDrag:V,onPaneClick:K,onPaneMouseEnter:Y,onPaneMouseMove:W,onPaneMouseLeave:j,onPaneScroll:U,onPaneContextMenu:q,onEdgeContextMenu:G,onEdgeMouseEnter:Q,onEdgeMouseMove:J,onEdgeMouseLeave:ee,onReconnect:et,onReconnectStart:en,onReconnectEnd:eo,reconnectRadius:er,noDragClassName:el,noWheelClassName:ea,noPanClassName:ei,elevateEdgesOnSelect:es,disableKeyboardA11y:ed,nodeOrigin:ec,nodeExtent:eu,rfId:eg})=>{let eh=nM(e,t5),ep=nM(t,nr);return!function(e){let t=tb(),n=(0,m.useRef)(!1);(0,m.useEffect)(()=>{!n.current&&t.viewportInitialized&&e&&(setTimeout(()=>e(t),1),n.current=!0)},[e,t.viewportInitialized])}(l),m.createElement(t2,{onPaneClick:K,onPaneMouseEnter:Y,onPaneMouseMove:W,onPaneMouseLeave:j,onPaneContextMenu:q,onPaneScroll:U,deleteKeyCode:A,selectionKeyCode:v,selectionOnDrag:w,selectionMode:M,onSelectionStart:f,onSelectionEnd:y,multiSelectionKeyCode:C,panActivationKeyCode:N,zoomActivationKeyCode:k,elementsSelectable:R,onMove:n,onMoveStart:o,onMoveEnd:r,zoomOnScroll:L,zoomOnPinch:T,zoomOnDoubleClick:X,panOnScroll:H,panOnScrollSpeed:F,panOnScrollMode:Z,panOnDrag:V,defaultViewport:_,translateExtent:$,minZoom:O,maxZoom:B,onSelectionContextMenu:p,preventScrolling:D,noDragClassName:el,noWheelClassName:ea,noPanClassName:ei,disableKeyboardA11y:ed},m.createElement(nb,null,m.createElement(ny,{edgeTypes:ep,onEdgeClick:i,onEdgeDoubleClick:d,onlyRenderVisibleElements:I,onEdgeContextMenu:G,onEdgeMouseEnter:Q,onEdgeMouseMove:J,onEdgeMouseLeave:ee,onReconnect:et,onReconnectStart:en,onReconnectEnd:eo,reconnectRadius:er,defaultMarkerColor:z,noPanClassName:ei,elevateEdgesOnSelect:!!es,disableKeyboardA11y:ed,rfId:eg},m.createElement(nw,{style:b,type:x,component:S,containerStyle:E})),m.createElement("div",{className:"react-flow__edgelabel-renderer"}),m.createElement(t6,{nodeTypes:eh,onNodeClick:a,onNodeDoubleClick:s,onNodeMouseEnter:c,onNodeMouseMove:u,onNodeMouseLeave:g,onNodeContextMenu:h,selectNodesOnDrag:P,onlyRenderVisibleElements:I,noPanClassName:ei,noDragClassName:el,disableKeyboardA11y:ed,nodeOrigin:ec,nodeExtent:eu,rfId:eg})))};nC.displayName="GraphView";var nN=(0,m.memo)(nC);let nk=[[Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY],[Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY]],nA={rfId:"1",width:0,height:0,transform:[0,0,1],nodeInternals:new Map,edges:[],onNodesChange:null,onEdgesChange:null,hasDefaultNodes:!1,hasDefaultEdges:!1,d3Zoom:null,d3Selection:null,d3ZoomHandler:void 0,minZoom:.5,maxZoom:2,translateExtent:nk,nodeExtent:nk,nodesSelectionActive:!1,userSelectionActive:!1,userSelectionRect:null,connectionNodeId:null,connectionHandleId:null,connectionHandleType:"source",connectionPosition:{x:0,y:0},connectionStatus:null,connectionMode:d.Strict,domNode:null,paneDragging:!1,noPanClassName:"nopan",nodeOrigin:[0,0],nodeDragThreshold:0,snapGrid:[15,15],snapToGrid:!1,nodesDraggable:!0,nodesConnectable:!0,nodesFocusable:!0,edgesFocusable:!0,edgesUpdatable:!0,elementsSelectable:!0,elevateNodesOnSelect:!0,fitViewOnInit:!1,fitViewOnInitDone:!1,fitViewOnInitOptions:void 0,onSelectionChange:[],multiSelectionActive:!1,connectionStartHandle:null,connectionEndHandle:null,connectionClickStartHandle:null,connectOnClick:!0,ariaLiveMessage:"",autoPanOnConnect:!0,autoPanOnNodeDrag:!0,connectionRadius:20,onError:q,isValidConnection:void 0},nI=()=>(0,y.F)((e,t)=>({...nA,setNodes:n=>{let{nodeInternals:o,nodeOrigin:r,elevateNodesOnSelect:l}=t();e({nodeInternals:tg(n,o,r,l)})},getNodes:()=>Array.from(t().nodeInternals.values()),setEdges:n=>{let{defaultEdgeOptions:o={}}=t();e({edges:n.map(e=>({...o,...e}))})},setDefaultNodesAndEdges:(n,o)=>{let r=void 0!==n,l=void 0!==o;e({nodeInternals:r?tg(n,new Map,t().nodeOrigin,t().elevateNodesOnSelect):new Map,edges:l?o:[],hasDefaultNodes:r,hasDefaultEdges:l})},updateNodeDimensions:n=>{let{onNodesChange:o,nodeInternals:r,fitViewOnInit:l,fitViewOnInitDone:a,fitViewOnInitOptions:i,domNode:s,nodeOrigin:d}=t(),c=s?.querySelector(".react-flow__viewport");if(!c)return;let u=window.getComputedStyle(c),{m22:g}=new window.DOMMatrixReadOnly(u.transform),h=n.reduce((e,t)=>{let n=r.get(t.id);if(n?.hidden)r.set(n.id,{...n,[j]:{...n[j],handleBounds:void 0}});else if(n){let o=O(t.nodeElement);o.width&&o.height&&(n.width!==o.width||n.height!==o.height||t.forceUpdate)&&(r.set(n.id,{...n,[j]:{...n[j],handleBounds:{source:tV(".source",t.nodeElement,g,d),target:tV(".target",t.nodeElement,g,d)}},...o}),e.push({id:n.id,type:"dimensions",dimensions:o}))}return e},[]);tu(r,d);let p=a||l&&!a&&th(t,{initial:!0,...i});e({nodeInternals:new Map(r),fitViewOnInitDone:p}),h?.length>0&&o?.(h)},updateNodePositions:(e,n=!0,o=!1)=>{let{triggerNodeChanges:r}=t();r(e.map(e=>{let t={id:e.id,type:"position",dragging:o};return n&&(t.positionAbsolute=e.positionAbsolute,t.position=e.position),t}))},triggerNodeChanges:n=>{let{onNodesChange:o,nodeInternals:r,hasDefaultNodes:l,nodeOrigin:a,getNodes:i,elevateNodesOnSelect:s}=t();n?.length&&(l&&e({nodeInternals:tg(tO(n,i()),r,a,s)}),o?.(n))},addSelectedNodes:n=>{let o;let{multiSelectionActive:r,edges:l,getNodes:a}=t(),i=null;r?o=n.map(e=>tD(e,!0)):(o=tz(a(),n),i=tz(l,[])),tp({changedNodes:o,changedEdges:i,get:t,set:e})},addSelectedEdges:n=>{let o;let{multiSelectionActive:r,edges:l,getNodes:a}=t(),i=null;r?o=n.map(e=>tD(e,!0)):(o=tz(l,n),i=tz(a(),[])),tp({changedNodes:i,changedEdges:o,get:t,set:e})},unselectNodesAndEdges:({nodes:n,edges:o}={})=>{let{edges:r,getNodes:l}=t();tp({changedNodes:(n||l()).map(e=>(e.selected=!1,tD(e.id,!1))),changedEdges:(o||r).map(e=>tD(e.id,!1)),get:t,set:e})},setMinZoom:n=>{let{d3Zoom:o,maxZoom:r}=t();o?.scaleExtent([n,r]),e({minZoom:n})},setMaxZoom:n=>{let{d3Zoom:o,minZoom:r}=t();o?.scaleExtent([r,n]),e({maxZoom:n})},setTranslateExtent:n=>{t().d3Zoom?.translateExtent(n),e({translateExtent:n})},resetSelectedElements:()=>{let{edges:n,getNodes:o}=t();tp({changedNodes:o().filter(e=>e.selected).map(e=>tD(e.id,!1)),changedEdges:n.filter(e=>e.selected).map(e=>tD(e.id,!1)),get:t,set:e})},setNodeExtent:n=>{let{nodeInternals:o}=t();o.forEach(e=>{e.positionAbsolute=D(e.position,n)}),e({nodeExtent:n,nodeInternals:new Map(o)})},panBy:e=>{let{transform:n,width:o,height:r,d3Zoom:l,d3Selection:a,translateExtent:i}=t();if(!l||!a||!e.x&&!e.y)return!1;let s=b.CR.translate(n[0]+e.x,n[1]+e.y).scale(n[2]),d=l?.constrain()(s,[[0,0],[o,r]],i);return l.transform(a,d),n[0]!==d.x||n[1]!==d.y||n[2]!==d.k},cancelConnection:()=>e({connectionNodeId:nA.connectionNodeId,connectionHandleId:nA.connectionHandleId,connectionHandleType:nA.connectionHandleType,connectionStatus:nA.connectionStatus,connectionStartHandle:nA.connectionStartHandle,connectionEndHandle:nA.connectionEndHandle}),reset:()=>e({...nA})}),Object.is),nR=({children:e})=>{let t=(0,m.useRef)(null);return t.current||(t.current=nI()),m.createElement(C,{value:t.current},e)};nR.displayName="ReactFlowProvider";let nP=({children:e})=>(0,m.useContext)(M)?m.createElement(m.Fragment,null,e):m.createElement(nR,null,e);nP.displayName="ReactFlowWrapper";let n_={input:eG,default:eU,output:eJ,group:e0},n$={default:eb,straight:em,step:ep,smoothstep:eh,simplebezier:es},nO=[0,0],nB=[15,15],nD={x:0,y:0,zoom:1},nz={width:"100%",height:"100%",overflow:"hidden",position:"relative",zIndex:0},nL=(0,m.forwardRef)(({nodes:e,edges:t,defaultNodes:n,defaultEdges:o,className:r,nodeTypes:l=n_,edgeTypes:a=n$,onNodeClick:i,onEdgeClick:s,onInit:h,onMove:p,onMoveStart:y,onMoveEnd:x,onConnect:b,onConnectStart:S,onConnectEnd:E,onClickConnectStart:v,onClickConnectEnd:w,onNodeMouseEnter:M,onNodeMouseMove:C,onNodeMouseLeave:N,onNodeContextMenu:k,onNodeDoubleClick:A,onNodeDragStart:I,onNodeDrag:R,onNodeDragStop:P,onNodesDelete:$,onEdgesDelete:O,onSelectionChange:B,onSelectionDragStart:D,onSelectionDrag:z,onSelectionDragStop:L,onSelectionContextMenu:T,onSelectionStart:H,onSelectionEnd:F,connectionMode:Z=d.Strict,connectionLineType:X=g.Bezier,connectionLineStyle:V,connectionLineComponent:K,connectionLineContainerStyle:Y,deleteKeyCode:W="Backspace",selectionKeyCode:j="Shift",selectionOnDrag:U=!1,selectionMode:q=u.Full,panActivationKeyCode:G="Space",multiSelectionKeyCode:Q=et()?"Meta":"Control",zoomActivationKeyCode:J=et()?"Meta":"Control",snapToGrid:ee=!1,snapGrid:en=nB,onlyRenderVisibleElements:eo=!1,selectNodesOnDrag:er=!0,nodesDraggable:el,nodesConnectable:ea,nodesFocusable:ei,nodeOrigin:es=nO,edgesFocusable:ed,edgesUpdatable:ec,elementsSelectable:eu,defaultViewport:eg=nD,minZoom:eh=.5,maxZoom:ep=2,translateExtent:em=nk,preventScrolling:ef=!0,nodeExtent:ey,defaultMarkerColor:ex="#b1b1b7",zoomOnScroll:eb=!0,zoomOnPinch:eS=!0,panOnScroll:eE=!1,panOnScrollSpeed:ev=.5,panOnScrollMode:ew=c.Free,zoomOnDoubleClick:eM=!0,panOnDrag:eC=!0,onPaneClick:eN,onPaneMouseEnter:ek,onPaneMouseMove:eA,onPaneMouseLeave:eI,onPaneScroll:eR,onPaneContextMenu:eP,children:e_,onEdgeContextMenu:e$,onEdgeDoubleClick:eO,onEdgeMouseEnter:eB,onEdgeMouseMove:eD,onEdgeMouseLeave:ez,onEdgeUpdate:eL,onEdgeUpdateStart:eT,onEdgeUpdateEnd:eH,onReconnect:eF,onReconnectStart:eZ,onReconnectEnd:eX,reconnectRadius:eV=10,edgeUpdaterRadius:eK=10,onNodesChange:eY,onEdgesChange:eW,noDragClassName:ej="nodrag",noWheelClassName:eU="nowheel",noPanClassName:eq="nopan",fitView:eG=!1,fitViewOptions:eQ,connectOnClick:eJ=!0,attributionPosition:e0,proOptions:e1,defaultEdgeOptions:e2,elevateNodesOnSelect:e5=!0,elevateEdgesOnSelect:e3=!1,disableKeyboardA11y:e4=!1,autoPanOnConnect:e6=!0,autoPanOnNodeDrag:e8=!0,connectionRadius:e7=20,isValidConnection:tt,onError:tn,style:to,id:tr,nodeDragThreshold:tl,...ta},ts)=>{let td=tr||"1";return m.createElement("div",{...ta,style:{...to,...nz},ref:ts,className:(0,f.Z)(["react-flow",r]),"data-testid":"rf__wrapper",id:tr},m.createElement(nP,null,m.createElement(nN,{onInit:h,onMove:p,onMoveStart:y,onMoveEnd:x,onNodeClick:i,onEdgeClick:s,onNodeMouseEnter:M,onNodeMouseMove:C,onNodeMouseLeave:N,onNodeContextMenu:k,onNodeDoubleClick:A,nodeTypes:l,edgeTypes:a,connectionLineType:X,connectionLineStyle:V,connectionLineComponent:K,connectionLineContainerStyle:Y,selectionKeyCode:j,selectionOnDrag:U,selectionMode:q,deleteKeyCode:W,multiSelectionKeyCode:Q,panActivationKeyCode:G,zoomActivationKeyCode:J,onlyRenderVisibleElements:eo,selectNodesOnDrag:er,defaultViewport:eg,translateExtent:em,minZoom:eh,maxZoom:ep,preventScrolling:ef,zoomOnScroll:eb,zoomOnPinch:eS,zoomOnDoubleClick:eM,panOnScroll:eE,panOnScrollSpeed:ev,panOnScrollMode:ew,panOnDrag:eC,onPaneClick:eN,onPaneMouseEnter:ek,onPaneMouseMove:eA,onPaneMouseLeave:eI,onPaneScroll:eR,onPaneContextMenu:eP,onSelectionContextMenu:T,onSelectionStart:H,onSelectionEnd:F,onEdgeContextMenu:e$,onEdgeDoubleClick:eO,onEdgeMouseEnter:eB,onEdgeMouseMove:eD,onEdgeMouseLeave:ez,onReconnect:eF??eL,onReconnectStart:eZ??eT,onReconnectEnd:eX??eH,reconnectRadius:eV??eK,defaultMarkerColor:ex,noDragClassName:ej,noWheelClassName:eU,noPanClassName:eq,elevateEdgesOnSelect:e3,rfId:td,disableKeyboardA11y:e4,nodeOrigin:es,nodeExtent:ey}),m.createElement(te,{nodes:e,edges:t,defaultNodes:n,defaultEdges:o,onConnect:b,onConnectStart:S,onConnectEnd:E,onClickConnectStart:v,onClickConnectEnd:w,nodesDraggable:el,nodesConnectable:ea,nodesFocusable:ei,edgesFocusable:ed,edgesUpdatable:ec,elementsSelectable:eu,elevateNodesOnSelect:e5,minZoom:eh,maxZoom:ep,nodeExtent:ey,onNodesChange:eY,onEdgesChange:eW,snapToGrid:ee,snapGrid:en,connectionMode:Z,translateExtent:em,connectOnClick:eJ,defaultEdgeOptions:e2,fitView:eG,fitViewOptions:eQ,onNodesDelete:$,onEdgesDelete:O,onNodeDragStart:I,onNodeDrag:R,onNodeDragStop:P,onSelectionDrag:z,onSelectionDragStart:D,onSelectionDragStop:L,noPanClassName:eq,nodeOrigin:es,rfId:td,autoPanOnConnect:e6,autoPanOnNodeDrag:e8,onError:tn,connectionRadius:e7,isValidConnection:tt,nodeDragThreshold:tl}),m.createElement(e9,{onSelectionChange:B}),e_,m.createElement(_,{proOptions:e1,position:e0}),m.createElement(ti,{rfId:td,disableKeyboardA11y:e4})))});nL.displayName="ReactFlow";let nT=e=>e.domNode?.querySelector(".react-flow__edgelabel-renderer");function nH({children:e}){let t=A(nT);return t?(0,w.createPortal)(e,t):null}function nF(e){return t=>{let[n,o]=(0,m.useState)(t),r=(0,m.useCallback)(t=>o(n=>e(t,n)),[]);return[n,o,r]}}nF(tO),nF(tB)}}]);