'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Plus, Search, Filter, Grid, List } from 'lucide-react';
import Link from 'next/link';
import { FlowCard } from '@/components/flow/FlowCard';
import { CreateFlowDialog } from '@/components/flow/CreateFlowDialog';
import { FlowStatus } from '@flowwise-clone/shared';

// Mock data - replace with actual API calls
const mockFlows = [
  {
    id: '1',
    name: 'Customer Support Bot',
    description: 'AI-powered customer support chatbot with knowledge base integration',
    status: FlowStatus.ACTIVE,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-20T15:30:00Z',
    nodeCount: 8,
    tags: ['chatbot', 'support', 'rag'],
  },
  {
    id: '2',
    name: 'Document Analyzer',
    description: 'Multi-agent system for analyzing and summarizing documents',
    status: FlowStatus.DRAFT,
    createdAt: '2024-01-18T09:15:00Z',
    updatedAt: '2024-01-18T16:45:00Z',
    nodeCount: 12,
    tags: ['document', 'analysis', 'multi-agent'],
  },
  {
    id: '3',
    name: 'Code Review Assistant',
    description: 'AI agent that reviews code and provides suggestions',
    status: FlowStatus.ACTIVE,
    createdAt: '2024-01-10T14:20:00Z',
    updatedAt: '2024-01-22T11:10:00Z',
    nodeCount: 6,
    tags: ['code', 'review', 'development'],
  },
];

export default function FlowsPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showCreateDialog, setShowCreateDialog] = useState(false);

  const filteredFlows = mockFlows.filter(flow =>
    flow.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    flow.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    flow.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Flows</h1>
          <p className="text-gray-600 mt-1">
            Build and manage your AI workflows
          </p>
        </div>
        <Button onClick={() => setShowCreateDialog(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Create Flow
        </Button>
      </div>

      {/* Search and Filters */}
      <div className="flex items-center space-x-4 mb-6">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search flows..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        <Button variant="outline" size="sm">
          <Filter className="h-4 w-4 mr-2" />
          Filter
        </Button>
        <div className="flex items-center border border-gray-300 rounded-lg">
          <Button
            variant={viewMode === 'grid' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setViewMode('grid')}
            className="rounded-r-none"
          >
            <Grid className="h-4 w-4" />
          </Button>
          <Button
            variant={viewMode === 'list' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setViewMode('list')}
            className="rounded-l-none"
          >
            <List className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Empty State */}
      {filteredFlows.length === 0 && searchQuery === '' && (
        <div className="text-center py-12">
          <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
            <Plus className="h-8 w-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No flows yet
          </h3>
          <p className="text-gray-600 mb-4">
            Get started by creating your first AI workflow
          </p>
          <Button onClick={() => setShowCreateDialog(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Create Your First Flow
          </Button>
        </div>
      )}

      {/* No Search Results */}
      {filteredFlows.length === 0 && searchQuery !== '' && (
        <div className="text-center py-12">
          <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
            <Search className="h-8 w-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No flows found
          </h3>
          <p className="text-gray-600">
            Try adjusting your search terms
          </p>
        </div>
      )}

      {/* Flows Grid */}
      {filteredFlows.length > 0 && (
        <div className={
          viewMode === 'grid' 
            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
            : 'space-y-4'
        }>
          {filteredFlows.map((flow) => (
            <FlowCard
              key={flow.id}
              flow={flow}
              viewMode={viewMode}
            />
          ))}
        </div>
      )}

      {/* Create Flow Dialog */}
      <CreateFlowDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
      />
    </div>
  );
}
