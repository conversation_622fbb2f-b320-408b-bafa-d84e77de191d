'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Save, 
  Play, 
  Share, 
  Settings, 
  MessageSquare, 
  PanelRight,
  ArrowLeft,
  MoreHorizontal,
  Download,
  Upload,
  Copy,
  Trash2
} from 'lucide-react';
import { Flow, FlowStatus } from '@flowwise-clone/shared';
import { useFlowStore } from '@/store/flowStore';
import toast from 'react-hot-toast';

interface FlowToolbarProps {
  flow: Flow;
  onToggleChat: () => void;
  onToggleProperties: () => void;
  showChat: boolean;
  showProperties: boolean;
}

export function FlowToolbar({ 
  flow, 
  onToggleChat, 
  onToggleProperties,
  showChat,
  showProperties 
}: FlowToolbarProps) {
  const router = useRouter();
  const { isDirty, setDirty } = useFlowStore();
  const [isSaving, setIsSaving] = useState(false);
  const [isTesting, setIsTesting] = useState(false);

  const handleSave = async () => {
    setIsSaving(true);
    try {
      // TODO: Implement actual save logic
      await new Promise(resolve => setTimeout(resolve, 1000));
      setDirty(false);
      toast.success('Flow saved successfully');
    } catch (error) {
      toast.error('Failed to save flow');
    } finally {
      setIsSaving(false);
    }
  };

  const handleTest = async () => {
    setIsTesting(true);
    try {
      // TODO: Implement actual test logic
      await new Promise(resolve => setTimeout(resolve, 2000));
      toast.success('Flow test completed');
    } catch (error) {
      toast.error('Flow test failed');
    } finally {
      setIsTesting(false);
    }
  };

  const handleShare = () => {
    // TODO: Implement share functionality
    navigator.clipboard.writeText(window.location.href);
    toast.success('Flow URL copied to clipboard');
  };

  const handleDuplicate = () => {
    // TODO: Implement duplicate functionality
    toast.success('Flow duplicated');
  };

  const handleDelete = () => {
    // TODO: Implement delete functionality with confirmation
    if (confirm('Are you sure you want to delete this flow?')) {
      toast.success('Flow deleted');
      router.push('/flows');
    }
  };

  const statusColors = {
    [FlowStatus.ACTIVE]: 'bg-green-100 text-green-800',
    [FlowStatus.DRAFT]: 'bg-yellow-100 text-yellow-800',
    [FlowStatus.INACTIVE]: 'bg-gray-100 text-gray-800',
    [FlowStatus.ARCHIVED]: 'bg-red-100 text-red-800',
  };

  return (
    <div className="h-14 bg-white border-b border-gray-200 flex items-center justify-between px-4">
      {/* Left Section */}
      <div className="flex items-center space-x-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.push('/flows')}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        
        <div className="h-6 w-px bg-gray-300" />
        
        <div className="flex items-center space-x-3">
          <div>
            <h1 className="font-semibold text-gray-900">{flow.name}</h1>
            <div className="flex items-center space-x-2 text-sm text-gray-500">
              <span>Flow ID: {flow.id}</span>
              <Badge className={statusColors[flow.status]}>
                {flow.status}
              </Badge>
              {isDirty && (
                <Badge variant="outline" className="text-orange-600 border-orange-600">
                  Unsaved changes
                </Badge>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Center Section */}
      <div className="flex items-center space-x-2">
        <Button
          variant="outline"
          size="sm"
          onClick={handleSave}
          disabled={isSaving || !isDirty}
        >
          <Save className="h-4 w-4 mr-2" />
          {isSaving ? 'Saving...' : 'Save'}
        </Button>
        
        <Button
          variant="outline"
          size="sm"
          onClick={handleTest}
          disabled={isTesting}
        >
          <Play className="h-4 w-4 mr-2" />
          {isTesting ? 'Testing...' : 'Test'}
        </Button>
        
        <Button
          variant="outline"
          size="sm"
          onClick={handleShare}
        >
          <Share className="h-4 w-4 mr-2" />
          Share
        </Button>
      </div>

      {/* Right Section */}
      <div className="flex items-center space-x-2">
        <Button
          variant={showChat ? "default" : "outline"}
          size="sm"
          onClick={onToggleChat}
        >
          <MessageSquare className="h-4 w-4 mr-2" />
          Chat
        </Button>
        
        <Button
          variant={showProperties ? "default" : "outline"}
          size="sm"
          onClick={onToggleProperties}
        >
          <PanelRight className="h-4 w-4 mr-2" />
          Properties
        </Button>
        
        <div className="h-6 w-px bg-gray-300" />
        
        <Button variant="ghost" size="sm">
          <Settings className="h-4 w-4" />
        </Button>
        
        {/* More Actions Dropdown */}
        <div className="relative">
          <Button variant="ghost" size="sm">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
          {/* TODO: Add dropdown menu with more actions */}
        </div>
      </div>
    </div>
  );
}
