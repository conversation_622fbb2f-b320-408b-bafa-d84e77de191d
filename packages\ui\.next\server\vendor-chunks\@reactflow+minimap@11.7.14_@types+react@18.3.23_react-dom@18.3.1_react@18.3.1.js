"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@reactflow+minimap@11.7.14_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1";
exports.ids = ["vendor-chunks/@reactflow+minimap@11.7.14_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@reactflow+minimap@11.7.14_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@reactflow/minimap/dist/esm/index.mjs":
/*!*****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@reactflow+minimap@11.7.14_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@reactflow/minimap/dist/esm/index.mjs ***!
  \*****************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MiniMap: () => (/* binding */ MiniMap$1)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var classcat__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classcat */ \"(ssr)/../../node_modules/.pnpm/classcat@5.0.5/node_modules/classcat/index.js\");\n/* harmony import */ var zustand_shallow__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zustand/shallow */ \"(ssr)/../../node_modules/.pnpm/zustand@4.5.7_@types+react@18.3.23_react@18.3.1/node_modules/zustand/esm/shallow.mjs\");\n/* harmony import */ var d3_zoom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3-zoom */ \"(ssr)/../../node_modules/.pnpm/d3-zoom@3.0.0/node_modules/d3-zoom/src/index.js\");\n/* harmony import */ var d3_selection__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! d3-selection */ \"(ssr)/../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/select.js\");\n/* harmony import */ var d3_selection__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! d3-selection */ \"(ssr)/../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/pointer.js\");\n/* harmony import */ var _reactflow_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @reactflow/core */ \"(ssr)/../../node_modules/.pnpm/@reactflow+core@11.11.4_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@reactflow/core/dist/esm/index.mjs\");\n\n\n\n\n\n\n\nconst MiniMapNode = ({ id, x, y, width, height, style, color, strokeColor, strokeWidth, className, borderRadius, shapeRendering, onClick, selected, }) => {\n    const { background, backgroundColor } = style || {};\n    const fill = (color || background || backgroundColor);\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"rect\", { className: (0,classcat__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(['react-flow__minimap-node', { selected }, className]), x: x, y: y, rx: borderRadius, ry: borderRadius, width: width, height: height, fill: fill, stroke: strokeColor, strokeWidth: strokeWidth, shapeRendering: shapeRendering, onClick: onClick ? (event) => onClick(event, id) : undefined }));\n};\nMiniMapNode.displayName = 'MiniMapNode';\nvar MiniMapNode$1 = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(MiniMapNode);\n\n/* eslint-disable @typescript-eslint/ban-ts-comment */\nconst selector$1 = (s) => s.nodeOrigin;\nconst selectorNodes = (s) => s.getNodes().filter((node) => !node.hidden && node.width && node.height);\nconst getAttrFunction = (func) => (func instanceof Function ? func : () => func);\nfunction MiniMapNodes({ nodeStrokeColor = 'transparent', nodeColor = '#e2e2e2', nodeClassName = '', nodeBorderRadius = 5, nodeStrokeWidth = 2, \n// We need to rename the prop to be `CapitalCase` so that JSX will render it as\n// a component properly.\nnodeComponent: NodeComponent = MiniMapNode$1, onClick, }) {\n    const nodes = (0,_reactflow_core__WEBPACK_IMPORTED_MODULE_3__.useStore)(selectorNodes, zustand_shallow__WEBPACK_IMPORTED_MODULE_4__.shallow);\n    const nodeOrigin = (0,_reactflow_core__WEBPACK_IMPORTED_MODULE_3__.useStore)(selector$1);\n    const nodeColorFunc = getAttrFunction(nodeColor);\n    const nodeStrokeColorFunc = getAttrFunction(nodeStrokeColor);\n    const nodeClassNameFunc = getAttrFunction(nodeClassName);\n    const shapeRendering = typeof window === 'undefined' || !!window.chrome ? 'crispEdges' : 'geometricPrecision';\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, nodes.map((node) => {\n        const { x, y } = (0,_reactflow_core__WEBPACK_IMPORTED_MODULE_3__.getNodePositionWithOrigin)(node, nodeOrigin).positionAbsolute;\n        return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(NodeComponent, { key: node.id, x: x, y: y, width: node.width, height: node.height, style: node.style, selected: node.selected, className: nodeClassNameFunc(node), color: nodeColorFunc(node), borderRadius: nodeBorderRadius, strokeColor: nodeStrokeColorFunc(node), strokeWidth: nodeStrokeWidth, shapeRendering: shapeRendering, onClick: onClick, id: node.id }));\n    })));\n}\nvar MiniMapNodes$1 = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(MiniMapNodes);\n\n/* eslint-disable @typescript-eslint/ban-ts-comment */\nconst defaultWidth = 200;\nconst defaultHeight = 150;\nconst selector = (s) => {\n    const nodes = s.getNodes();\n    const viewBB = {\n        x: -s.transform[0] / s.transform[2],\n        y: -s.transform[1] / s.transform[2],\n        width: s.width / s.transform[2],\n        height: s.height / s.transform[2],\n    };\n    return {\n        viewBB,\n        boundingRect: nodes.length > 0 ? (0,_reactflow_core__WEBPACK_IMPORTED_MODULE_3__.getBoundsOfRects)((0,_reactflow_core__WEBPACK_IMPORTED_MODULE_3__.getNodesBounds)(nodes, s.nodeOrigin), viewBB) : viewBB,\n        rfId: s.rfId,\n    };\n};\nconst ARIA_LABEL_KEY = 'react-flow__minimap-desc';\nfunction MiniMap({ style, className, nodeStrokeColor = 'transparent', nodeColor = '#e2e2e2', nodeClassName = '', nodeBorderRadius = 5, nodeStrokeWidth = 2, \n// We need to rename the prop to be `CapitalCase` so that JSX will render it as\n// a component properly.\nnodeComponent, maskColor = 'rgb(240, 240, 240, 0.6)', maskStrokeColor = 'none', maskStrokeWidth = 1, position = 'bottom-right', onClick, onNodeClick, pannable = false, zoomable = false, ariaLabel = 'React Flow mini map', inversePan = false, zoomStep = 10, offsetScale = 5, }) {\n    const store = (0,_reactflow_core__WEBPACK_IMPORTED_MODULE_3__.useStoreApi)();\n    const svg = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const { boundingRect, viewBB, rfId } = (0,_reactflow_core__WEBPACK_IMPORTED_MODULE_3__.useStore)(selector, zustand_shallow__WEBPACK_IMPORTED_MODULE_4__.shallow);\n    const elementWidth = style?.width ?? defaultWidth;\n    const elementHeight = style?.height ?? defaultHeight;\n    const scaledWidth = boundingRect.width / elementWidth;\n    const scaledHeight = boundingRect.height / elementHeight;\n    const viewScale = Math.max(scaledWidth, scaledHeight);\n    const viewWidth = viewScale * elementWidth;\n    const viewHeight = viewScale * elementHeight;\n    const offset = offsetScale * viewScale;\n    const x = boundingRect.x - (viewWidth - boundingRect.width) / 2 - offset;\n    const y = boundingRect.y - (viewHeight - boundingRect.height) / 2 - offset;\n    const width = viewWidth + offset * 2;\n    const height = viewHeight + offset * 2;\n    const labelledBy = `${ARIA_LABEL_KEY}-${rfId}`;\n    const viewScaleRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    viewScaleRef.current = viewScale;\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        if (svg.current) {\n            const selection = (0,d3_selection__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(svg.current);\n            const zoomHandler = (event) => {\n                const { transform, d3Selection, d3Zoom } = store.getState();\n                if (event.sourceEvent.type !== 'wheel' || !d3Selection || !d3Zoom) {\n                    return;\n                }\n                const pinchDelta = -event.sourceEvent.deltaY *\n                    (event.sourceEvent.deltaMode === 1 ? 0.05 : event.sourceEvent.deltaMode ? 1 : 0.002) *\n                    zoomStep;\n                const zoom = transform[2] * Math.pow(2, pinchDelta);\n                d3Zoom.scaleTo(d3Selection, zoom);\n            };\n            const panHandler = (event) => {\n                const { transform, d3Selection, d3Zoom, translateExtent, width, height } = store.getState();\n                if (event.sourceEvent.type !== 'mousemove' || !d3Selection || !d3Zoom) {\n                    return;\n                }\n                // @TODO: how to calculate the correct next position? Math.max(1, transform[2]) is a workaround.\n                const moveScale = viewScaleRef.current * Math.max(1, transform[2]) * (inversePan ? -1 : 1);\n                const position = {\n                    x: transform[0] - event.sourceEvent.movementX * moveScale,\n                    y: transform[1] - event.sourceEvent.movementY * moveScale,\n                };\n                const extent = [\n                    [0, 0],\n                    [width, height],\n                ];\n                const nextTransform = d3_zoom__WEBPACK_IMPORTED_MODULE_2__.zoomIdentity.translate(position.x, position.y).scale(transform[2]);\n                const constrainedTransform = d3Zoom.constrain()(nextTransform, extent, translateExtent);\n                d3Zoom.transform(d3Selection, constrainedTransform);\n            };\n            const zoomAndPanHandler = (0,d3_zoom__WEBPACK_IMPORTED_MODULE_2__.zoom)()\n                // @ts-ignore\n                .on('zoom', pannable ? panHandler : null)\n                // @ts-ignore\n                .on('zoom.wheel', zoomable ? zoomHandler : null);\n            selection.call(zoomAndPanHandler);\n            return () => {\n                selection.on('zoom', null);\n            };\n        }\n    }, [pannable, zoomable, inversePan, zoomStep]);\n    const onSvgClick = onClick\n        ? (event) => {\n            const rfCoord = (0,d3_selection__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(event);\n            onClick(event, { x: rfCoord[0], y: rfCoord[1] });\n        }\n        : undefined;\n    const onSvgNodeClick = onNodeClick\n        ? (event, nodeId) => {\n            const node = store.getState().nodeInternals.get(nodeId);\n            onNodeClick(event, node);\n        }\n        : undefined;\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(_reactflow_core__WEBPACK_IMPORTED_MODULE_3__.Panel, { position: position, style: style, className: (0,classcat__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(['react-flow__minimap', className]), \"data-testid\": \"rf__minimap\" },\n        react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", { width: elementWidth, height: elementHeight, viewBox: `${x} ${y} ${width} ${height}`, role: \"img\", \"aria-labelledby\": labelledBy, ref: svg, onClick: onSvgClick },\n            ariaLabel && react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", { id: labelledBy }, ariaLabel),\n            react__WEBPACK_IMPORTED_MODULE_0__.createElement(MiniMapNodes$1, { onClick: onSvgNodeClick, nodeColor: nodeColor, nodeStrokeColor: nodeStrokeColor, nodeBorderRadius: nodeBorderRadius, nodeClassName: nodeClassName, nodeStrokeWidth: nodeStrokeWidth, nodeComponent: nodeComponent }),\n            react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", { className: \"react-flow__minimap-mask\", d: `M${x - offset},${y - offset}h${width + offset * 2}v${height + offset * 2}h${-width - offset * 2}z\n        M${viewBB.x},${viewBB.y}h${viewBB.width}v${viewBB.height}h${-viewBB.width}z`, fill: maskColor, fillRule: \"evenodd\", stroke: maskStrokeColor, strokeWidth: maskStrokeWidth, pointerEvents: \"none\" }))));\n}\nMiniMap.displayName = 'MiniMap';\nvar MiniMap$1 = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(MiniMap);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@reactflow+minimap@11.7.14_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@reactflow/minimap/dist/esm/index.mjs\n");

/***/ })

};
;