/**
 * Formats a timestamp to a human-readable string
 */
export declare function formatTimestamp(timestamp: string): string;
/**
 * Formats execution time in milliseconds to a human-readable string
 */
export declare function formatExecutionTime(ms: number): string;
/**
 * Truncates text to a specified length with ellipsis
 */
export declare function truncateText(text: string, maxLength: number): string;
/**
 * Capitalizes the first letter of a string
 */
export declare function capitalize(str: string): string;
/**
 * Converts camelCase to Title Case
 */
export declare function camelToTitle(str: string): string;
/**
 * Generates a random color for nodes
 */
export declare function generateNodeColor(): string;
//# sourceMappingURL=formatting.d.ts.map