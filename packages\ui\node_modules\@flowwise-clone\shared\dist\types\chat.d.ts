import { z } from 'zod';
export declare enum MessageRole {
    USER = "user",
    ASSISTANT = "assistant",
    SYSTEM = "system",
    FUNCTION = "function",
    TOOL = "tool"
}
export declare const MessageSchema: z.ZodObject<{
    id: z.ZodString;
    createdAt: z.ZodString;
    updatedAt: z.ZodString;
} & {
    role: z.ZodNativeEnum<typeof MessageRole>;
    content: z.ZodString;
    name: z.ZodOptional<z.ZodString>;
    functionCall: z.ZodOptional<z.ZodObject<{
        name: z.ZodString;
        arguments: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        name: string;
        arguments: string;
    }, {
        name: string;
        arguments: string;
    }>>;
    toolCalls: z.Zod<PERSON>ptional<z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        type: z.ZodLiteral<"function">;
        function: z.ZodObject<{
            name: z.ZodString;
            arguments: z.ZodString;
        }, "strip", z.<PERSON>, {
            name: string;
            arguments: string;
        }, {
            name: string;
            arguments: string;
        }>;
    }, "strip", z.<PERSON>od<PERSON>ype<PERSON>, {
        function: {
            name: string;
            arguments: string;
        };
        type: "function";
        id: string;
    }, {
        function: {
            name: string;
            arguments: string;
        };
        type: "function";
        id: string;
    }>, "many">>;
    toolCallId: z.ZodOptional<z.ZodString>;
    metadata: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: string;
    updatedAt: string;
    role: MessageRole;
    content: string;
    name?: string | undefined;
    metadata?: Record<string, any> | undefined;
    functionCall?: {
        name: string;
        arguments: string;
    } | undefined;
    toolCalls?: {
        function: {
            name: string;
            arguments: string;
        };
        type: "function";
        id: string;
    }[] | undefined;
    toolCallId?: string | undefined;
}, {
    id: string;
    createdAt: string;
    updatedAt: string;
    role: MessageRole;
    content: string;
    name?: string | undefined;
    metadata?: Record<string, any> | undefined;
    functionCall?: {
        name: string;
        arguments: string;
    } | undefined;
    toolCalls?: {
        function: {
            name: string;
            arguments: string;
        };
        type: "function";
        id: string;
    }[] | undefined;
    toolCallId?: string | undefined;
}>;
export type Message = z.infer<typeof MessageSchema>;
export declare const ChatSessionSchema: z.ZodObject<{
    id: z.ZodString;
    createdAt: z.ZodString;
    updatedAt: z.ZodString;
} & {
    flowId: z.ZodString;
    userId: z.ZodOptional<z.ZodString>;
    sessionId: z.ZodString;
    title: z.ZodOptional<z.ZodString>;
    messages: z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        createdAt: z.ZodString;
        updatedAt: z.ZodString;
    } & {
        role: z.ZodNativeEnum<typeof MessageRole>;
        content: z.ZodString;
        name: z.ZodOptional<z.ZodString>;
        functionCall: z.ZodOptional<z.ZodObject<{
            name: z.ZodString;
            arguments: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            name: string;
            arguments: string;
        }, {
            name: string;
            arguments: string;
        }>>;
        toolCalls: z.ZodOptional<z.ZodArray<z.ZodObject<{
            id: z.ZodString;
            type: z.ZodLiteral<"function">;
            function: z.ZodObject<{
                name: z.ZodString;
                arguments: z.ZodString;
            }, "strip", z.ZodTypeAny, {
                name: string;
                arguments: string;
            }, {
                name: string;
                arguments: string;
            }>;
        }, "strip", z.ZodTypeAny, {
            function: {
                name: string;
                arguments: string;
            };
            type: "function";
            id: string;
        }, {
            function: {
                name: string;
                arguments: string;
            };
            type: "function";
            id: string;
        }>, "many">>;
        toolCallId: z.ZodOptional<z.ZodString>;
        metadata: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        createdAt: string;
        updatedAt: string;
        role: MessageRole;
        content: string;
        name?: string | undefined;
        metadata?: Record<string, any> | undefined;
        functionCall?: {
            name: string;
            arguments: string;
        } | undefined;
        toolCalls?: {
            function: {
                name: string;
                arguments: string;
            };
            type: "function";
            id: string;
        }[] | undefined;
        toolCallId?: string | undefined;
    }, {
        id: string;
        createdAt: string;
        updatedAt: string;
        role: MessageRole;
        content: string;
        name?: string | undefined;
        metadata?: Record<string, any> | undefined;
        functionCall?: {
            name: string;
            arguments: string;
        } | undefined;
        toolCalls?: {
            function: {
                name: string;
                arguments: string;
            };
            type: "function";
            id: string;
        }[] | undefined;
        toolCallId?: string | undefined;
    }>, "many">;
    isActive: z.ZodDefault<z.ZodBoolean>;
    metadata: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: string;
    updatedAt: string;
    flowId: string;
    sessionId: string;
    messages: {
        id: string;
        createdAt: string;
        updatedAt: string;
        role: MessageRole;
        content: string;
        name?: string | undefined;
        metadata?: Record<string, any> | undefined;
        functionCall?: {
            name: string;
            arguments: string;
        } | undefined;
        toolCalls?: {
            function: {
                name: string;
                arguments: string;
            };
            type: "function";
            id: string;
        }[] | undefined;
        toolCallId?: string | undefined;
    }[];
    isActive: boolean;
    userId?: string | undefined;
    metadata?: Record<string, any> | undefined;
    title?: string | undefined;
}, {
    id: string;
    createdAt: string;
    updatedAt: string;
    flowId: string;
    sessionId: string;
    messages: {
        id: string;
        createdAt: string;
        updatedAt: string;
        role: MessageRole;
        content: string;
        name?: string | undefined;
        metadata?: Record<string, any> | undefined;
        functionCall?: {
            name: string;
            arguments: string;
        } | undefined;
        toolCalls?: {
            function: {
                name: string;
                arguments: string;
            };
            type: "function";
            id: string;
        }[] | undefined;
        toolCallId?: string | undefined;
    }[];
    userId?: string | undefined;
    metadata?: Record<string, any> | undefined;
    title?: string | undefined;
    isActive?: boolean | undefined;
}>;
export type ChatSession = z.infer<typeof ChatSessionSchema>;
export declare const ChatRequestSchema: z.ZodObject<{
    message: z.ZodString;
    sessionId: z.ZodOptional<z.ZodString>;
    flowId: z.ZodString;
    userId: z.ZodOptional<z.ZodString>;
    streaming: z.ZodDefault<z.ZodBoolean>;
    metadata: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
}, "strip", z.ZodTypeAny, {
    message: string;
    flowId: string;
    streaming: boolean;
    userId?: string | undefined;
    metadata?: Record<string, any> | undefined;
    sessionId?: string | undefined;
}, {
    message: string;
    flowId: string;
    userId?: string | undefined;
    metadata?: Record<string, any> | undefined;
    sessionId?: string | undefined;
    streaming?: boolean | undefined;
}>;
export type ChatRequest = z.infer<typeof ChatRequestSchema>;
export declare const ChatResponseSchema: z.ZodObject<{
    message: z.ZodObject<{
        id: z.ZodString;
        createdAt: z.ZodString;
        updatedAt: z.ZodString;
    } & {
        role: z.ZodNativeEnum<typeof MessageRole>;
        content: z.ZodString;
        name: z.ZodOptional<z.ZodString>;
        functionCall: z.ZodOptional<z.ZodObject<{
            name: z.ZodString;
            arguments: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            name: string;
            arguments: string;
        }, {
            name: string;
            arguments: string;
        }>>;
        toolCalls: z.ZodOptional<z.ZodArray<z.ZodObject<{
            id: z.ZodString;
            type: z.ZodLiteral<"function">;
            function: z.ZodObject<{
                name: z.ZodString;
                arguments: z.ZodString;
            }, "strip", z.ZodTypeAny, {
                name: string;
                arguments: string;
            }, {
                name: string;
                arguments: string;
            }>;
        }, "strip", z.ZodTypeAny, {
            function: {
                name: string;
                arguments: string;
            };
            type: "function";
            id: string;
        }, {
            function: {
                name: string;
                arguments: string;
            };
            type: "function";
            id: string;
        }>, "many">>;
        toolCallId: z.ZodOptional<z.ZodString>;
        metadata: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        createdAt: string;
        updatedAt: string;
        role: MessageRole;
        content: string;
        name?: string | undefined;
        metadata?: Record<string, any> | undefined;
        functionCall?: {
            name: string;
            arguments: string;
        } | undefined;
        toolCalls?: {
            function: {
                name: string;
                arguments: string;
            };
            type: "function";
            id: string;
        }[] | undefined;
        toolCallId?: string | undefined;
    }, {
        id: string;
        createdAt: string;
        updatedAt: string;
        role: MessageRole;
        content: string;
        name?: string | undefined;
        metadata?: Record<string, any> | undefined;
        functionCall?: {
            name: string;
            arguments: string;
        } | undefined;
        toolCalls?: {
            function: {
                name: string;
                arguments: string;
            };
            type: "function";
            id: string;
        }[] | undefined;
        toolCallId?: string | undefined;
    }>;
    sessionId: z.ZodString;
    isComplete: z.ZodDefault<z.ZodBoolean>;
    metadata: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
}, "strip", z.ZodTypeAny, {
    message: {
        id: string;
        createdAt: string;
        updatedAt: string;
        role: MessageRole;
        content: string;
        name?: string | undefined;
        metadata?: Record<string, any> | undefined;
        functionCall?: {
            name: string;
            arguments: string;
        } | undefined;
        toolCalls?: {
            function: {
                name: string;
                arguments: string;
            };
            type: "function";
            id: string;
        }[] | undefined;
        toolCallId?: string | undefined;
    };
    sessionId: string;
    isComplete: boolean;
    metadata?: Record<string, any> | undefined;
}, {
    message: {
        id: string;
        createdAt: string;
        updatedAt: string;
        role: MessageRole;
        content: string;
        name?: string | undefined;
        metadata?: Record<string, any> | undefined;
        functionCall?: {
            name: string;
            arguments: string;
        } | undefined;
        toolCalls?: {
            function: {
                name: string;
                arguments: string;
            };
            type: "function";
            id: string;
        }[] | undefined;
        toolCallId?: string | undefined;
    };
    sessionId: string;
    metadata?: Record<string, any> | undefined;
    isComplete?: boolean | undefined;
}>;
export type ChatResponse = z.infer<typeof ChatResponseSchema>;
export declare const StreamingChatResponseSchema: z.ZodObject<{
    delta: z.ZodString;
    sessionId: z.ZodString;
    isComplete: z.ZodBoolean;
    metadata: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
}, "strip", z.ZodTypeAny, {
    sessionId: string;
    isComplete: boolean;
    delta: string;
    metadata?: Record<string, any> | undefined;
}, {
    sessionId: string;
    isComplete: boolean;
    delta: string;
    metadata?: Record<string, any> | undefined;
}>;
export type StreamingChatResponse = z.infer<typeof StreamingChatResponseSchema>;
//# sourceMappingURL=chat.d.ts.map