"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FlowValidationResultSchema = exports.FlowTemplateSchema = exports.FlowExecutionSchema = exports.FlowSchema = void 0;
const zod_1 = require("zod");
const common_1 = require("./common");
const node_1 = require("./node");
// Flow schema
exports.FlowSchema = common_1.BaseEntitySchema.extend({
    name: zod_1.z.string(),
    description: zod_1.z.string().optional(),
    status: zod_1.z.nativeEnum(common_1.FlowStatus),
    nodes: zod_1.z.array(node_1.NodeSchema),
    connections: zod_1.z.array(node_1.FlowConnectionSchema),
    userId: zod_1.z.string().uuid(),
    workspaceId: zod_1.z.string().uuid().optional(),
    isPublic: zod_1.z.boolean().default(false),
    tags: zod_1.z.array(zod_1.z.string()).default([]),
    version: zod_1.z.string().default('1.0.0'),
    metadata: zod_1.z.record(zod_1.z.any()).optional()
});
// Flow execution schema
exports.FlowExecutionSchema = common_1.BaseEntitySchema.extend({
    flowId: zod_1.z.string().uuid(),
    status: zod_1.z.nativeEnum(common_1.ExecutionStatus),
    input: zod_1.z.any().optional(),
    output: zod_1.z.any().optional(),
    error: zod_1.z.string().optional(),
    startTime: zod_1.z.string().datetime(),
    endTime: zod_1.z.string().datetime().optional(),
    executionTime: zod_1.z.number().optional(),
    nodeResults: zod_1.z.array(node_1.NodeExecutionResultSchema),
    userId: zod_1.z.string().uuid().optional(),
    sessionId: zod_1.z.string().optional(),
    metadata: zod_1.z.record(zod_1.z.any()).optional()
});
// Flow template schema
exports.FlowTemplateSchema = zod_1.z.object({
    name: zod_1.z.string(),
    description: zod_1.z.string(),
    category: zod_1.z.string(),
    tags: zod_1.z.array(zod_1.z.string()),
    nodes: zod_1.z.array(node_1.NodeSchema),
    connections: zod_1.z.array(node_1.FlowConnectionSchema),
    thumbnail: zod_1.z.string().optional(),
    isOfficial: zod_1.z.boolean().default(false),
    difficulty: zod_1.z.enum(['beginner', 'intermediate', 'advanced']).default('beginner')
});
// Flow validation result
exports.FlowValidationResultSchema = zod_1.z.object({
    isValid: zod_1.z.boolean(),
    errors: zod_1.z.array(zod_1.z.object({
        nodeId: zod_1.z.string().optional(),
        connectionId: zod_1.z.string().optional(),
        message: zod_1.z.string(),
        type: zod_1.z.enum(['error', 'warning'])
    }))
});
//# sourceMappingURL=flow.js.map