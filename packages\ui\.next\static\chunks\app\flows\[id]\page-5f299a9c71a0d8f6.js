(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[978],{6520:function(e,s,t){Promise.resolve().then(t.bind(t,2608))},2608:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return eZ}});var a=t(8980),r=t(2208),l=t(9211),n=t(4239),i=t(246),o=t(4024),c=t(4769);t(6609);var d=t(3589),m=t(3157);let u=[],x=[],p=(0,d.Ue)()((0,m.mW)((0,m.XR)((e,s)=>({currentFlow:null,nodes:u,edges:x,isLoading:!1,selectedNodeId:null,selectedEdgeId:null,isDirty:!1,setCurrentFlow:s=>{e({currentFlow:s}),s&&e({nodes:s.nodes.map(e=>({id:e.id,type:e.type,position:e.position,data:{label:e.label,config:e.config,inputs:e.inputs,outputs:e.outputs,category:e.category,description:e.description,icon:e.icon,color:e.color}})),edges:s.connections.map(e=>({id:e.id,source:e.sourceNodeId,target:e.targetNodeId,sourceHandle:e.sourceHandle,targetHandle:e.targetHandle,type:"smoothstep"})),isDirty:!1})},setNodes:s=>e({nodes:s,isDirty:!0}),setEdges:s=>e({edges:s,isDirty:!0}),onNodesChange:t=>{e({nodes:(0,n.Fb)(t,s().nodes),isDirty:!0})},onEdgesChange:t=>{e({edges:(0,n.yn)(t,s().edges),isDirty:!0})},onConnect:t=>{e({edges:(0,n.Z_)({...t,id:"edge-".concat(Date.now()),type:"smoothstep"},s().edges),isDirty:!0})},addNode:t=>{let a={id:"node-".concat(Date.now()),type:t.type||"default",position:t.position||{x:100,y:100},data:t.data||{},...t};e({nodes:[...s().nodes,a],isDirty:!0})},updateNode:(t,a)=>{e({nodes:s().nodes.map(e=>e.id===t?{...e,...a}:e),isDirty:!0})},deleteNode:t=>{e({nodes:s().nodes.filter(e=>e.id!==t),edges:s().edges.filter(e=>e.source!==t&&e.target!==t),selectedNodeId:s().selectedNodeId===t?null:s().selectedNodeId,isDirty:!0})},duplicateNode:t=>{let a=s().nodes.find(e=>e.id===t);if(a){let t={...a,id:"node-".concat(Date.now()),position:{x:a.position.x+50,y:a.position.y+50}};e({nodes:[...s().nodes,t],isDirty:!0})}},selectNode:s=>{e({selectedNodeId:s,selectedEdgeId:null})},selectEdge:s=>{e({selectedEdgeId:s,selectedNodeId:null})},setLoading:s=>e({isLoading:s}),setDirty:s=>e({isDirty:s}),resetFlow:()=>{e({currentFlow:null,nodes:[],edges:[],selectedNodeId:null,selectedEdgeId:null,isDirty:!1})}})),{name:"flow-store"}));var h=t(3815),f=t(9713),g=t(9838),y=t(5207),j=t(3389),v=t(4279),b=t(4838),N=t(860),w=t(4385),C=t(4507),Z=t(7343),k=t(199),S=t(3454),I=t(9808),D=t(2424);let z={start:y.Z,llm:j.Z,tool:v.Z,memory:b.Z,vectorStore:b.Z,documentLoader:N.Z,textSplitter:w.Z,embeddings:C.Z,retriever:C.Z,chain:Z.Z,agent:j.Z,outputParser:k.Z,promptTemplate:N.Z,conditional:S.Z,loop:S.Z,webhook:Z.Z,apiCall:Z.Z,custom:k.Z,default:k.Z},T={start:"bg-green-500",llm:"bg-blue-500",tool:"bg-orange-500",memory:"bg-purple-500",vectorStore:"bg-purple-500",documentLoader:"bg-pink-500",textSplitter:"bg-cyan-500",embeddings:"bg-lime-500",retriever:"bg-red-500",chain:"bg-gray-500",agent:"bg-orange-500",outputParser:"bg-teal-500",promptTemplate:"bg-violet-500",conditional:"bg-yellow-500",loop:"bg-yellow-500",webhook:"bg-indigo-500",apiCall:"bg-indigo-500",custom:"bg-gray-700",default:"bg-gray-500"},P=(0,r.memo)(e=>{let{data:s,selected:t,type:r="default"}=e,l=z[r]||z.default,i=T[r]||T.default,o=s.inputs||[],c=s.outputs||[];return(0,a.jsxs)(h.Zb,{className:(0,D.cn)("min-w-[200px] transition-all duration-200",t&&"ring-2 ring-blue-500 ring-offset-2","hover:shadow-md"),children:[o.map((e,s)=>(0,a.jsx)(n.HH,{type:"target",position:n.Ly.Left,id:e.id,style:{top:"".concat((s+1)/(o.length+1)*100,"%"),background:"#6b7280"},className:"w-3 h-3 border-2 border-white"},e.id)),c.map((e,s)=>(0,a.jsx)(n.HH,{type:"source",position:n.Ly.Right,id:e.id,style:{top:"".concat((s+1)/(c.length+1)*100,"%"),background:"#6b7280"},className:"w-3 h-3 border-2 border-white"},e.id)),(0,a.jsx)(h.Ol,{className:"pb-2",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:(0,D.cn)("p-1.5 rounded text-white",i),children:(0,a.jsx)(l,{className:"h-4 w-4"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-sm",children:s.label||"Node"}),s.category&&(0,a.jsx)(f.C,{variant:"secondary",className:"text-xs mt-1",children:s.category})]})]}),(0,a.jsx)(g.z,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0",children:(0,a.jsx)(I.Z,{className:"h-3 w-3"})})]})}),(0,a.jsxs)(h.aY,{className:"pt-0 pb-3",children:[s.description&&(0,a.jsx)("p",{className:"text-xs text-gray-600 mb-2 line-clamp-2",children:s.description}),s.config&&Object.keys(s.config).length>0&&(0,a.jsxs)("div",{className:"space-y-1",children:[Object.entries(s.config).slice(0,2).map(e=>{let[s,t]=e;return(0,a.jsxs)("div",{className:"flex justify-between text-xs",children:[(0,a.jsxs)("span",{className:"text-gray-500 capitalize",children:[s,":"]}),(0,a.jsx)("span",{className:"text-gray-700 truncate ml-2",children:"object"==typeof t?JSON.stringify(t):String(t)})]},s)}),Object.keys(s.config).length>2&&(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:["+",Object.keys(s.config).length-2," more"]})]}),(0,a.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mt-2 pt-2 border-t",children:[(0,a.jsxs)("span",{children:[o.length," inputs"]}),(0,a.jsxs)("span",{children:[c.length," outputs"]})]})]})]})});var L=t(4164);let O=(0,r.memo)(e=>{let{id:s,sourceX:t,sourceY:r,targetX:l,targetY:i,sourcePosition:o,targetPosition:c,style:d={},data:m,selected:u}=e,[x,p,h]=(0,n.OW)({sourceX:t,sourceY:r,sourcePosition:o,targetX:l,targetY:i,targetPosition:c});return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.u5,{path:x,style:{...d,strokeWidth:u?3:2,stroke:u?"#3B82F6":"#6B7280"}}),u&&(0,a.jsx)(n.XQ,{children:(0,a.jsx)("div",{style:{position:"absolute",transform:"translate(-50%, -50%) translate(".concat(p,"px,").concat(h,"px)"),fontSize:12,pointerEvents:"all"},className:"nodrag nopan",children:(0,a.jsx)(g.z,{variant:"destructive",size:"sm",className:"h-6 w-6 p-0 rounded-full",onClick:()=>{console.log("Edge clicked:",s)},children:(0,a.jsx)(L.Z,{className:"h-3 w-3"})})})})]})});var R=t(6984),_=t(5317),E=t(805),F=t(2820),M=t(4784);let A={default:P,start:P,llm:P,tool:P,memory:P,vectorStore:P,documentLoader:P,textSplitter:P,embeddings:P,retriever:P,chain:P,agent:P,outputParser:P,promptTemplate:P,conditional:P,loop:P,webhook:P,apiCall:P,custom:P},B={default:O,smoothstep:O};function V(){let e=(0,r.useRef)(null),{nodes:s,edges:t,onNodesChange:l,onEdgesChange:d,onConnect:m,selectNode:u,selectEdge:x}=p(),{zoomIn:h,zoomOut:f,fitView:y,getViewport:j,setViewport:v}=(0,n._K)(),b=(0,r.useCallback)((e,s)=>{u(s.id)},[u]),N=(0,r.useCallback)((e,s)=>{x(s.id)},[x]),w=(0,r.useCallback)(()=>{u(null),x(null)},[u,x]),C=(0,r.useCallback)(e=>{e.preventDefault(),e.dataTransfer.dropEffect="move"},[]),Z=(0,r.useCallback)(s=>{var t;s.preventDefault();let a=null===(t=e.current)||void 0===t?void 0:t.getBoundingClientRect(),r=s.dataTransfer.getData("application/reactflow");void 0!==r&&r&&a&&console.log("Drop node:",{type:r,position:{x:s.clientX-a.left,y:s.clientY-a.top}})},[]),k=(0,r.useCallback)(()=>{let e="data:application/json;charset=utf-8,"+encodeURIComponent(JSON.stringify({nodes:s,edges:t,viewport:j()},null,2)),a=document.createElement("a");a.setAttribute("href",e),a.setAttribute("download","flow.json"),a.click()},[s,t,j]),S=(0,r.useCallback)(()=>{let e=document.createElement("input");e.type="file",e.accept=".json",e.onchange=e=>{var s;let t=null===(s=e.target.files)||void 0===s?void 0:s[0];if(t){let e=new FileReader;e.onload=e=>{try{var s;let t=JSON.parse(null===(s=e.target)||void 0===s?void 0:s.result);console.log("Import flow:",t)}catch(e){console.error("Failed to import flow:",e)}},e.readAsText(t)}},e.click()},[]);return(0,a.jsx)("div",{className:"w-full h-full",ref:e,children:(0,a.jsxs)(n.x$,{nodes:s,edges:t,onNodesChange:l,onEdgesChange:d,onConnect:m,onNodeClick:b,onEdgeClick:N,onPaneClick:w,onDrop:Z,onDragOver:C,nodeTypes:A,edgeTypes:B,connectionMode:n.jD.Loose,fitView:!0,attributionPosition:"bottom-left",className:"bg-gray-50",children:[(0,a.jsx)(i.A,{color:"#e5e7eb",gap:20}),(0,a.jsx)(o.Z,{className:"bg-white border border-gray-200 rounded-lg shadow-sm",showInteractive:!1}),(0,a.jsx)(c.a,{className:"bg-white border border-gray-200 rounded-lg",nodeColor:e=>{var s;return(null===(s=e.data)||void 0===s?void 0:s.color)||"#3B82F6"},maskColor:"rgba(0, 0, 0, 0.1)"}),(0,a.jsxs)(n.s_,{position:"top-right",className:"space-y-2",children:[(0,a.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg shadow-sm p-2 space-y-1",children:[(0,a.jsxs)(g.z,{variant:"ghost",size:"sm",onClick:()=>h(),className:"w-full justify-start",children:[(0,a.jsx)(R.Z,{className:"h-4 w-4 mr-2"}),"Zoom In"]}),(0,a.jsxs)(g.z,{variant:"ghost",size:"sm",onClick:()=>f(),className:"w-full justify-start",children:[(0,a.jsx)(_.Z,{className:"h-4 w-4 mr-2"}),"Zoom Out"]}),(0,a.jsxs)(g.z,{variant:"ghost",size:"sm",onClick:()=>y(),className:"w-full justify-start",children:[(0,a.jsx)(E.Z,{className:"h-4 w-4 mr-2"}),"Fit View"]})]}),(0,a.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg shadow-sm p-2 space-y-1",children:[(0,a.jsxs)(g.z,{variant:"ghost",size:"sm",onClick:k,className:"w-full justify-start",children:[(0,a.jsx)(F.Z,{className:"h-4 w-4 mr-2"}),"Export"]}),(0,a.jsxs)(g.z,{variant:"ghost",size:"sm",onClick:S,className:"w-full justify-start",children:[(0,a.jsx)(M.Z,{className:"h-4 w-4 mr-2"}),"Import"]})]})]})]})})}function H(){return(0,a.jsx)(n.tV,{children:(0,a.jsx)(V,{})})}var Q=t(2315),Y=t(7107),G=t(9483),J=t(3659),U=t(6767),K=t(7474);function W(e){let{flow:s,onToggleChat:t,onToggleProperties:n,showChat:i,showProperties:o}=e,c=(0,l.useRouter)(),{isDirty:d,setDirty:m}=p(),[u,x]=(0,r.useState)(!1),[h,j]=(0,r.useState)(!1),v=async()=>{x(!0);try{await new Promise(e=>setTimeout(e,1e3)),m(!1),K.ZP.success("Flow saved successfully")}catch(e){K.ZP.error("Failed to save flow")}finally{x(!1)}},b=async()=>{j(!0);try{await new Promise(e=>setTimeout(e,2e3)),K.ZP.success("Flow test completed")}catch(e){K.ZP.error("Flow test failed")}finally{j(!1)}};return(0,a.jsxs)("div",{className:"h-14 bg-white border-b border-gray-200 flex items-center justify-between px-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)(g.z,{variant:"ghost",size:"sm",onClick:()=>c.push("/flows"),children:[(0,a.jsx)(Q.Z,{className:"h-4 w-4 mr-2"}),"Back"]}),(0,a.jsx)("div",{className:"h-6 w-px bg-gray-300"}),(0,a.jsx)("div",{className:"flex items-center space-x-3",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"font-semibold text-gray-900",children:s.name}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-500",children:[(0,a.jsxs)("span",{children:["Flow ID: ",s.id]}),(0,a.jsx)(f.C,{className:{active:"bg-green-100 text-green-800",draft:"bg-yellow-100 text-yellow-800",inactive:"bg-gray-100 text-gray-800"}[s.status],children:s.status}),d&&(0,a.jsx)(f.C,{variant:"outline",className:"text-orange-600 border-orange-600",children:"Unsaved changes"})]})]})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(g.z,{variant:"outline",size:"sm",onClick:v,disabled:u||!d,children:[(0,a.jsx)(Y.Z,{className:"h-4 w-4 mr-2"}),u?"Saving...":"Save"]}),(0,a.jsxs)(g.z,{variant:"outline",size:"sm",onClick:b,disabled:h,children:[(0,a.jsx)(y.Z,{className:"h-4 w-4 mr-2"}),h?"Testing...":"Test"]}),(0,a.jsxs)(g.z,{variant:"outline",size:"sm",onClick:()=>{navigator.clipboard.writeText(window.location.href),K.ZP.success("Flow URL copied to clipboard")},children:[(0,a.jsx)(G.Z,{className:"h-4 w-4 mr-2"}),"Share"]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(g.z,{variant:i?"default":"outline",size:"sm",onClick:t,children:[(0,a.jsx)(J.Z,{className:"h-4 w-4 mr-2"}),"Chat"]}),(0,a.jsxs)(g.z,{variant:o?"default":"outline",size:"sm",onClick:n,children:[(0,a.jsx)(U.Z,{className:"h-4 w-4 mr-2"}),"Properties"]}),(0,a.jsx)("div",{className:"h-6 w-px bg-gray-300"}),(0,a.jsx)(g.z,{variant:"ghost",size:"sm",children:(0,a.jsx)(k.Z,{className:"h-4 w-4"})}),(0,a.jsx)("div",{className:"relative",children:(0,a.jsx)(g.z,{variant:"ghost",size:"sm",children:(0,a.jsx)(I.Z,{className:"h-4 w-4"})})})]})]})}var q=t(9906),X=t(5837),$=t(6844),ee=t(9008);let es=[{id:"openai-gpt4",type:"llm",name:"OpenAI GPT-4",description:"OpenAI GPT-4 language model",category:"LLM",icon:j.Z,color:"#3B82F6"},{id:"anthropic-claude",type:"llm",name:"Anthropic Claude",description:"Anthropic Claude language model",category:"LLM",icon:j.Z,color:"#3B82F6"},{id:"google-palm",type:"llm",name:"Google PaLM",description:"Google PaLM language model",category:"LLM",icon:j.Z,color:"#3B82F6"},{id:"conversation-memory",type:"memory",name:"Conversation Memory",description:"Store conversation history",category:"Memory",icon:b.Z,color:"#10B981"},{id:"buffer-memory",type:"memory",name:"Buffer Memory",description:"Simple buffer memory",category:"Memory",icon:b.Z,color:"#10B981"},{id:"web-search",type:"tool",name:"Web Search",description:"Search the web for information",category:"Tools",icon:v.Z,color:"#F59E0B"},{id:"calculator",type:"tool",name:"Calculator",description:"Perform mathematical calculations",category:"Tools",icon:v.Z,color:"#F59E0B"},{id:"pinecone",type:"vectorStore",name:"Pinecone",description:"Pinecone vector database",category:"Vector Stores",icon:b.Z,color:"#8B5CF6"},{id:"chroma",type:"vectorStore",name:"Chroma",description:"Chroma vector database",category:"Vector Stores",icon:b.Z,color:"#8B5CF6"},{id:"pdf-loader",type:"documentLoader",name:"PDF Loader",description:"Load PDF documents",category:"Document Loaders",icon:N.Z,color:"#EC4899"},{id:"text-loader",type:"documentLoader",name:"Text Loader",description:"Load text documents",category:"Document Loaders",icon:N.Z,color:"#EC4899"},{id:"recursive-splitter",type:"textSplitter",name:"Recursive Text Splitter",description:"Split text recursively",category:"Text Splitters",icon:w.Z,color:"#06B6D4"},{id:"llm-chain",type:"chain",name:"LLM Chain",description:"Simple LLM chain",category:"Chains",icon:Z.Z,color:"#6B7280"},{id:"retrieval-qa",type:"chain",name:"Retrieval QA",description:"Question answering with retrieval",category:"Chains",icon:Z.Z,color:"#6B7280"}];function et(){let[e,s]=(0,r.useState)(""),[t,l]=(0,r.useState)(new Set(["LLM","Memory","Tools"])),n=es.filter(s=>s.name.toLowerCase().includes(e.toLowerCase())||s.description.toLowerCase().includes(e.toLowerCase())||s.category.toLowerCase().includes(e.toLowerCase())).reduce((e,s)=>(e[s.category]||(e[s.category]=[]),e[s.category].push(s),e),{}),i=e=>{let s=new Set(t);s.has(e)?s.delete(e):s.add(e),l(s)},o=(e,s)=>{e.dataTransfer.setData("application/reactflow",s),e.dataTransfer.effectAllowed="move"};return(0,a.jsxs)("div",{className:"w-80 bg-white border-r border-gray-200 flex flex-col",children:[(0,a.jsxs)("div",{className:"p-4 border-b border-gray-200",children:[(0,a.jsx)("h2",{className:"font-semibold text-gray-900 mb-3",children:"Node Library"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(C.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,a.jsx)(q.I,{placeholder:"Search nodes...",value:e,onChange:e=>s(e.target.value),className:"pl-10"})]})]}),(0,a.jsxs)("div",{className:"flex-1 overflow-y-auto p-4 space-y-4",children:[Object.entries(n).map(e=>{let[s,r]=e;return(0,a.jsxs)("div",{children:[(0,a.jsx)(g.z,{variant:"ghost",className:"w-full justify-between p-2 h-auto",onClick:()=>i(s),children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[t.has(s)?(0,a.jsx)(X.Z,{className:"h-4 w-4"}):(0,a.jsx)($.Z,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"font-medium",children:s}),(0,a.jsx)(f.C,{variant:"secondary",className:"text-xs",children:r.length})]})}),t.has(s)&&(0,a.jsx)("div",{className:"mt-2 space-y-2",children:r.map(e=>{let s=e.icon;return(0,a.jsx)(h.Zb,{className:"cursor-grab hover:shadow-md transition-shadow",draggable:!0,onDragStart:s=>o(s,e.type),children:(0,a.jsx)(h.aY,{className:"p-3",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"p-2 rounded text-white flex-shrink-0",style:{backgroundColor:e.color},children:(0,a.jsx)(s,{className:"h-4 w-4"})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h4",{className:"font-medium text-sm text-gray-900 truncate",children:e.name}),(0,a.jsx)("p",{className:"text-xs text-gray-600 mt-1 line-clamp-2",children:e.description})]})]})})},e.id)})})]},s)}),0===Object.keys(n).length&&(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(C.Z,{className:"h-8 w-8 text-gray-400 mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"No nodes found"}),(0,a.jsx)("p",{className:"text-gray-500 text-xs",children:"Try adjusting your search"})]})]}),(0,a.jsx)("div",{className:"p-4 border-t border-gray-200",children:(0,a.jsxs)(g.z,{variant:"outline",className:"w-full",size:"sm",children:[(0,a.jsx)(ee.Z,{className:"h-4 w-4 mr-2"}),"Create Custom Node"]})})]})}var ea=t(4740),er=t(2594),el=t(7731),en=t(7565);let ei=en.fC,eo=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(en.aV,{ref:s,className:(0,D.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",t),...r})});eo.displayName=en.aV.displayName;let ec=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(en.xz,{ref:s,className:(0,D.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",t),...r})});ec.displayName=en.xz.displayName;let ed=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(en.VY,{ref:s,className:(0,D.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",t),...r})});ed.displayName=en.VY.displayName;var em=t(3744),eu=t(2183),ex=t(679),ep=t(3814);function eh(){var e,s,t,l,n;let{selectedNodeId:i,selectedEdgeId:o,nodes:c,edges:d,updateNode:m}=p(),[u,x]=(0,r.useState)({}),y=i?c.find(e=>e.id===i):null,j=o?d.find(e=>e.id===o):null;(0,r.useEffect)(()=>{if(y){var e,s,t;x({label:(null===(e=y.data)||void 0===e?void 0:e.label)||"",description:(null===(s=y.data)||void 0===s?void 0:s.description)||"",config:(null===(t=y.data)||void 0===t?void 0:t.config)||{}})}else x({})},[y]);let v=(e,s)=>{x(t=>({...t,config:{...t.config,[e]:s}}))};return y||j?j?(0,a.jsxs)("div",{className:"h-full flex flex-col",children:[(0,a.jsx)("div",{className:"p-4 border-b border-gray-200",children:(0,a.jsx)("h2",{className:"font-semibold text-gray-900",children:"Edge Properties"})}),(0,a.jsx)("div",{className:"flex-1 p-4",children:(0,a.jsxs)(h.Zb,{children:[(0,a.jsx)(h.Ol,{children:(0,a.jsx)(h.ll,{className:"text-sm",children:"Connection Details"})}),(0,a.jsxs)(h.aY,{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(er._,{className:"text-xs",children:"Source Node"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:j.source})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(er._,{className:"text-xs",children:"Target Node"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:j.target})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(er._,{className:"text-xs",children:"Source Handle"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:j.sourceHandle})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(er._,{className:"text-xs",children:"Target Handle"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:j.targetHandle})]})]})]})})]}):(0,a.jsxs)("div",{className:"h-full flex flex-col",children:[(0,a.jsxs)("div",{className:"p-4 border-b border-gray-200",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h2",{className:"font-semibold text-gray-900",children:"Node Properties"}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(g.z,{variant:"ghost",size:"sm",onClick:()=>{y&&console.log("Duplicate node:",y.id)},children:(0,a.jsx)(em.Z,{className:"h-4 w-4"})}),(0,a.jsx)(g.z,{variant:"ghost",size:"sm",onClick:()=>{y&&console.log("Delete node:",y.id)},children:(0,a.jsx)(eu.Z,{className:"h-4 w-4"})})]})]}),y&&(0,a.jsx)("div",{className:"mt-2",children:(0,a.jsx)(f.C,{variant:"secondary",className:"text-xs",children:y.type})})]}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto",children:(0,a.jsxs)(ei,{defaultValue:"general",className:"h-full",children:[(0,a.jsxs)(eo,{className:"grid w-full grid-cols-3 m-4",children:[(0,a.jsxs)(ec,{value:"general",className:"text-xs",children:[(0,a.jsx)(ex.Z,{className:"h-3 w-3 mr-1"}),"General"]}),(0,a.jsxs)(ec,{value:"config",className:"text-xs",children:[(0,a.jsx)(k.Z,{className:"h-3 w-3 mr-1"}),"Config"]}),(0,a.jsxs)(ec,{value:"advanced",className:"text-xs",children:[(0,a.jsx)(ep.Z,{className:"h-3 w-3 mr-1"}),"Advanced"]})]}),(0,a.jsxs)(ed,{value:"general",className:"p-4 space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(er._,{htmlFor:"label",children:"Label"}),(0,a.jsx)(q.I,{id:"label",value:u.label||"",onChange:e=>x(s=>({...s,label:e.target.value})),placeholder:"Node label"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(er._,{htmlFor:"description",children:"Description"}),(0,a.jsx)(ea.g,{id:"description",value:u.description||"",onChange:e=>x(s=>({...s,description:e.target.value})),placeholder:"Node description",rows:3})]}),y&&(0,a.jsxs)(h.Zb,{children:[(0,a.jsx)(h.Ol,{children:(0,a.jsx)(h.ll,{className:"text-sm",children:"Node Information"})}),(0,a.jsxs)(h.aY,{className:"space-y-2 text-xs",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-500",children:"ID:"}),(0,a.jsx)("span",{className:"font-mono",children:y.id})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-500",children:"Type:"}),(0,a.jsx)("span",{children:y.type})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-500",children:"Category:"}),(0,a.jsx)("span",{children:null===(e=y.data)||void 0===e?void 0:e.category})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-500",children:"Inputs:"}),(0,a.jsx)("span",{children:(null===(t=y.data)||void 0===t?void 0:null===(s=t.inputs)||void 0===s?void 0:s.length)||0})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-500",children:"Outputs:"}),(0,a.jsx)("span",{children:(null===(n=y.data)||void 0===n?void 0:null===(l=n.outputs)||void 0===l?void 0:l.length)||0})]})]})]})]}),(0,a.jsxs)(ed,{value:"config",className:"p-4 space-y-4",children:[(null==y?void 0:y.type)==="llm"&&(0,a.jsx)(ef,{config:u.config||{},onChange:v}),(null==y?void 0:y.type)==="tool"&&(0,a.jsx)(eg,{config:u.config||{},onChange:v}),(null==y?void 0:y.type)==="memory"&&(0,a.jsx)(ey,{config:u.config||{},onChange:v}),!["llm","tool","memory"].includes((null==y?void 0:y.type)||"")&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Configuration options for this node type are not yet implemented."}),(0,a.jsx)("pre",{className:"text-xs bg-gray-100 p-3 rounded",children:JSON.stringify(u.config,null,2)})]})]}),(0,a.jsx)(ed,{value:"advanced",className:"p-4 space-y-4",children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(er._,{children:"Raw Configuration"}),(0,a.jsx)(ea.g,{value:JSON.stringify(u.config,null,2),onChange:e=>{try{let s=JSON.parse(e.target.value);x(e=>({...e,config:s}))}catch(e){}},rows:10,className:"font-mono text-xs"})]})})]})}),(0,a.jsx)("div",{className:"p-4 border-t border-gray-200",children:(0,a.jsx)(g.z,{onClick:()=>{y&&m(y.id,{data:{...y.data,label:u.label,description:u.description,config:u.config}})},className:"w-full",size:"sm",children:"Save Changes"})})]}):(0,a.jsxs)("div",{className:"h-full flex flex-col",children:[(0,a.jsx)("div",{className:"p-4 border-b border-gray-200",children:(0,a.jsx)("h2",{className:"font-semibold text-gray-900",children:"Properties"})}),(0,a.jsx)("div",{className:"flex-1 flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(k.Z,{className:"h-8 w-8 text-gray-400 mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Select a node or edge"}),(0,a.jsx)("p",{className:"text-gray-500 text-xs",children:"to view its properties"})]})})]})}function ef(e){let{config:s,onChange:t}=e;return(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(er._,{children:"Model"}),(0,a.jsxs)(el.Ph,{value:s.model||"gpt-4",onValueChange:e=>t("model",e),children:[(0,a.jsx)(el.i4,{children:(0,a.jsx)(el.ki,{})}),(0,a.jsxs)(el.Bw,{children:[(0,a.jsx)(el.Ql,{value:"gpt-4",children:"GPT-4"}),(0,a.jsx)(el.Ql,{value:"gpt-3.5-turbo",children:"GPT-3.5 Turbo"}),(0,a.jsx)(el.Ql,{value:"claude-3-opus",children:"Claude 3 Opus"}),(0,a.jsx)(el.Ql,{value:"claude-3-sonnet",children:"Claude 3 Sonnet"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(er._,{children:"Temperature"}),(0,a.jsx)(q.I,{type:"number",min:"0",max:"2",step:"0.1",value:s.temperature||.7,onChange:e=>t("temperature",parseFloat(e.target.value))})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(er._,{children:"Max Tokens"}),(0,a.jsx)(q.I,{type:"number",min:"1",max:"4000",value:s.maxTokens||1e3,onChange:e=>t("maxTokens",parseInt(e.target.value))})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(er._,{children:"System Message"}),(0,a.jsx)(ea.g,{value:s.systemMessage||"",onChange:e=>t("systemMessage",e.target.value),placeholder:"You are a helpful assistant...",rows:3})]})]})}function eg(e){let{config:s,onChange:t}=e;return(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(er._,{children:"Tool Type"}),(0,a.jsxs)(el.Ph,{value:s.toolType||"web-search",onValueChange:e=>t("toolType",e),children:[(0,a.jsx)(el.i4,{children:(0,a.jsx)(el.ki,{})}),(0,a.jsxs)(el.Bw,{children:[(0,a.jsx)(el.Ql,{value:"web-search",children:"Web Search"}),(0,a.jsx)(el.Ql,{value:"calculator",children:"Calculator"}),(0,a.jsx)(el.Ql,{value:"api-call",children:"API Call"}),(0,a.jsx)(el.Ql,{value:"custom",children:"Custom"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(er._,{children:"API Endpoint"}),(0,a.jsx)(q.I,{value:s.apiEndpoint||"",onChange:e=>t("apiEndpoint",e.target.value),placeholder:"https://api.example.com/search"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(er._,{children:"API Key"}),(0,a.jsx)(q.I,{type:"password",value:s.apiKey||"",onChange:e=>t("apiKey",e.target.value),placeholder:"Your API key"})]})]})}function ey(e){let{config:s,onChange:t}=e;return(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(er._,{children:"Memory Type"}),(0,a.jsxs)(el.Ph,{value:s.memoryType||"conversation",onValueChange:e=>t("memoryType",e),children:[(0,a.jsx)(el.i4,{children:(0,a.jsx)(el.ki,{})}),(0,a.jsxs)(el.Bw,{children:[(0,a.jsx)(el.Ql,{value:"conversation",children:"Conversation Memory"}),(0,a.jsx)(el.Ql,{value:"buffer",children:"Buffer Memory"}),(0,a.jsx)(el.Ql,{value:"summary",children:"Summary Memory"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(er._,{children:"Max Messages"}),(0,a.jsx)(q.I,{type:"number",min:"1",max:"100",value:s.maxMessages||10,onChange:e=>t("maxMessages",parseInt(e.target.value))})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(er._,{children:"Return Messages"}),(0,a.jsx)(q.I,{type:"number",min:"1",max:"20",value:s.returnMessages||5,onChange:e=>t("returnMessages",parseInt(e.target.value))})]})]})}var ej=t(4723),ev=t(3250),eb=t(8323);function eN(e){let{flowId:s}=e,[t,l]=(0,r.useState)([{id:"1",role:"system",content:"Chat session started. You can now test your flow by sending messages.",timestamp:new Date}]),[n,i]=(0,r.useState)(""),[o,c]=(0,r.useState)(!1),[d,m]=(0,r.useState)(""),u=(0,r.useRef)(null);(0,r.useEffect)(()=>{m("session-".concat(Date.now()))},[]),(0,r.useEffect)(()=>{x()},[t]);let x=()=>{var e;null===(e=u.current)||void 0===e||e.scrollIntoView({behavior:"smooth"})},p=async()=>{if(!n.trim()||o)return;let e={id:"msg-".concat(Date.now()),role:"user",content:n.trim(),timestamp:new Date};l(s=>[...s,e]),i(""),c(!0);try{await new Promise(e=>setTimeout(e,2e3));let s={id:"msg-".concat(Date.now(),"-response"),role:"assistant",content:'This is a mock response to: "'.concat(e.content,'". In a real implementation, this would be the output from your flow execution.'),timestamp:new Date};l(e=>[...e,s])}catch(s){let e={id:"msg-".concat(Date.now(),"-error"),role:"assistant",content:"Sorry, there was an error processing your message. Please try again.",timestamp:new Date};l(s=>[...s,e])}finally{c(!1)}};return(0,a.jsxs)("div",{className:"h-full flex flex-col bg-white",children:[(0,a.jsxs)("div",{className:"p-4 border-b border-gray-200",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(J.Z,{className:"h-5 w-5 text-blue-600"}),(0,a.jsx)("h2",{className:"font-semibold text-gray-900",children:"Test Chat"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(g.z,{variant:"ghost",size:"sm",onClick:()=>{let e="data:application/json;charset=utf-8,"+encodeURIComponent(JSON.stringify({flowId:s,sessionId:d,messages:t,exportedAt:new Date().toISOString()},null,2)),a="chat-".concat(d,".json"),r=document.createElement("a");r.setAttribute("href",e),r.setAttribute("download",a),r.click()},children:(0,a.jsx)(F.Z,{className:"h-4 w-4"})}),(0,a.jsx)(g.z,{variant:"ghost",size:"sm",onClick:()=>{l([{id:"1",role:"system",content:"Chat session cleared. You can start a new conversation.",timestamp:new Date}]),m("session-".concat(Date.now()))},children:(0,a.jsx)(eu.Z,{className:"h-4 w-4"})}),(0,a.jsx)(g.z,{variant:"ghost",size:"sm",children:(0,a.jsx)(k.Z,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{className:"mt-2 flex items-center space-x-2",children:[(0,a.jsxs)(f.C,{variant:"secondary",className:"text-xs",children:["Session: ",d.slice(-8)]}),(0,a.jsxs)(f.C,{variant:"outline",className:"text-xs",children:[t.filter(e=>"system"!==e.role).length," messages"]})]})]}),(0,a.jsxs)("div",{className:"flex-1 overflow-y-auto p-4 space-y-4",children:[t.map(e=>(0,a.jsx)(ew,{message:e},e.id)),o&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-gray-500",children:[(0,a.jsx)(j.Z,{className:"h-4 w-4"}),(0,a.jsxs)("div",{className:"flex space-x-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce"}),(0,a.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,a.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})]}),(0,a.jsx)("div",{ref:u})]}),(0,a.jsxs)("div",{className:"p-4 border-t border-gray-200",children:[(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(q.I,{value:n,onChange:e=>i(e.target.value),onKeyPress:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),p())},placeholder:"Type your message...",disabled:o,className:"flex-1"}),(0,a.jsx)(g.z,{onClick:p,disabled:!n.trim()||o,size:"sm",children:o?(0,a.jsx)(ej.Z,{className:"h-4 w-4"}):(0,a.jsx)(ev.Z,{className:"h-4 w-4"})})]}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:"Press Enter to send, Shift+Enter for new line"})]})]})}function ew(e){let{message:s}=e,t="user"===s.role;return"system"===s.role?(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)("div",{className:"bg-gray-100 text-gray-600 text-xs px-3 py-1 rounded-full",children:s.content})}):(0,a.jsx)("div",{className:(0,D.cn)("flex",t?"justify-end":"justify-start"),children:(0,a.jsxs)("div",{className:(0,D.cn)("flex space-x-2 max-w-[80%]",t&&"flex-row-reverse space-x-reverse"),children:[(0,a.jsx)("div",{className:(0,D.cn)("w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0",t?"bg-blue-600 text-white":"bg-gray-200 text-gray-600"),children:t?(0,a.jsx)(eb.Z,{className:"h-4 w-4"}):(0,a.jsx)(j.Z,{className:"h-4 w-4"})}),(0,a.jsxs)("div",{className:(0,D.cn)("rounded-lg px-3 py-2 text-sm",t?"bg-blue-600 text-white":"bg-gray-100 text-gray-900"),children:[(0,a.jsx)("p",{className:"whitespace-pre-wrap",children:s.content}),(0,a.jsx)("p",{className:(0,D.cn)("text-xs mt-1 opacity-70",t?"text-blue-100":"text-gray-500"),children:s.timestamp.toLocaleTimeString()})]})]})})}let eC={id:"1",name:"Customer Support Bot",description:"AI-powered customer support chatbot",status:"active",nodes:[{id:"start-node",type:"start",name:"Start",label:"Start",category:"System",position:{x:100,y:100},inputs:[],outputs:[{id:"output",type:"output",dataType:"string",label:"Output",required:!1,multiple:!1}],config:{},flowId:"1",isCustom:!1,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},{id:"llm-node",type:"llm",name:"OpenAI GPT-4",label:"OpenAI GPT-4",category:"LLM",position:{x:300,y:100},inputs:[{id:"prompt",type:"input",dataType:"string",label:"Prompt",required:!0,multiple:!1}],outputs:[{id:"response",type:"output",dataType:"string",label:"Response",required:!1,multiple:!1}],config:{model:"gpt-4",temperature:.7,maxTokens:1e3},flowId:"1",isCustom:!1,color:"#3B82F6",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()}],connections:[{id:"connection-1",sourceNodeId:"start-node",sourceHandle:"output",targetNodeId:"llm-node",targetHandle:"prompt",flowId:"1"}],userId:"user-1",isPublic:!1,tags:["chatbot","support"],version:"1.0.0",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};function eZ(){let e=(0,l.useParams)().id,{setCurrentFlow:s,currentFlow:t,isLoading:n,setLoading:i}=p(),[o,c]=(0,r.useState)(!1),[d,m]=(0,r.useState)(!0);return((0,r.useEffect)(()=>{(async()=>{i(!0);try{await new Promise(e=>setTimeout(e,1e3)),s(eC)}catch(e){console.error("Failed to load flow:",e)}finally{i(!1)}})()},[e,s,i]),n)?(0,a.jsx)("div",{className:"h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading flow..."})]})}):t?(0,a.jsxs)("div",{className:"h-screen flex flex-col bg-gray-50",children:[(0,a.jsx)(W,{flow:t,onToggleChat:()=>c(!o),onToggleProperties:()=>m(!d),showChat:o,showProperties:d}),(0,a.jsxs)("div",{className:"flex-1 flex overflow-hidden",children:[(0,a.jsx)(et,{}),(0,a.jsx)("div",{className:"flex-1 relative",children:(0,a.jsx)(H,{})}),d&&(0,a.jsx)("div",{className:"w-80 border-l bg-white",children:(0,a.jsx)(eh,{})}),o&&(0,a.jsx)("div",{className:"w-96 border-l bg-white",children:(0,a.jsx)(eN,{flowId:e})})]})]}):(0,a.jsx)("div",{className:"h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Flow not found"}),(0,a.jsx)("p",{className:"text-gray-600",children:"The requested flow could not be loaded."})]})})}},9713:function(e,s,t){"use strict";t.d(s,{C:function(){return i}});var a=t(8980);t(2208);var r=t(3868),l=t(2424);let n=(0,r.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:s,variant:t,...r}=e;return(0,a.jsx)("div",{className:(0,l.cn)(n({variant:t}),s),...r})}},9838:function(e,s,t){"use strict";t.d(s,{z:function(){return c}});var a=t(8980),r=t(2208),l=t(9382),n=t(3868),i=t(2424);let o=(0,n.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=r.forwardRef((e,s)=>{let{className:t,variant:r,size:n,asChild:c=!1,...d}=e,m=c?l.g7:"button";return(0,a.jsx)(m,{className:(0,i.cn)(o({variant:r,size:n,className:t})),ref:s,...d})});c.displayName="Button"},3815:function(e,s,t){"use strict";t.d(s,{Ol:function(){return i},SZ:function(){return c},Zb:function(){return n},aY:function(){return d},ll:function(){return o}});var a=t(8980),r=t(2208),l=t(2424);let n=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...r})});n.displayName="Card";let i=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",t),...r})});i.displayName="CardHeader";let o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("h3",{ref:s,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",t),...r})});o.displayName="CardTitle";let c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("p",{ref:s,className:(0,l.cn)("text-sm text-muted-foreground",t),...r})});c.displayName="CardDescription";let d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("p-6 pt-0",t),...r})});d.displayName="CardContent",r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("flex items-center p-6 pt-0",t),...r})}).displayName="CardFooter"},9906:function(e,s,t){"use strict";t.d(s,{I:function(){return n}});var a=t(8980),r=t(2208),l=t(2424);let n=r.forwardRef((e,s)=>{let{className:t,type:r,...n}=e;return(0,a.jsx)("input",{type:r,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...n})});n.displayName="Input"},2594:function(e,s,t){"use strict";t.d(s,{_:function(){return c}});var a=t(8980),r=t(2208),l=t(3747),n=t(3868),i=t(2424);let o=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.f,{ref:s,className:(0,i.cn)(o(),t),...r})});c.displayName=l.f.displayName},7731:function(e,s,t){"use strict";t.d(s,{Bw:function(){return h},Ph:function(){return d},Ql:function(){return f},i4:function(){return u},ki:function(){return m}});var a=t(8980),r=t(2208),l=t(242),n=t(5837),i=t(6887),o=t(4362),c=t(2424);let d=l.fC;l.ZA;let m=l.B4,u=r.forwardRef((e,s)=>{let{className:t,children:r,...i}=e;return(0,a.jsxs)(l.xz,{ref:s,className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),...i,children:[r,(0,a.jsx)(l.JO,{asChild:!0,children:(0,a.jsx)(n.Z,{className:"h-4 w-4 opacity-50"})})]})});u.displayName=l.xz.displayName;let x=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.u_,{ref:s,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",t),...r,children:(0,a.jsx)(i.Z,{className:"h-4 w-4"})})});x.displayName=l.u_.displayName;let p=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.$G,{ref:s,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",t),...r,children:(0,a.jsx)(n.Z,{className:"h-4 w-4"})})});p.displayName=l.$G.displayName;let h=r.forwardRef((e,s)=>{let{className:t,children:r,position:n="popper",...i}=e;return(0,a.jsx)(l.h_,{children:(0,a.jsxs)(l.VY,{ref:s,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:n,...i,children:[(0,a.jsx)(x,{}),(0,a.jsx)(l.l_,{className:(0,c.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:r}),(0,a.jsx)(p,{})]})})});h.displayName=l.VY.displayName,r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.__,{ref:s,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",t),...r})}).displayName=l.__.displayName;let f=r.forwardRef((e,s)=>{let{className:t,children:r,...n}=e;return(0,a.jsxs)(l.ck,{ref:s,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...n,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(l.wU,{children:(0,a.jsx)(o.Z,{className:"h-4 w-4"})})}),(0,a.jsx)(l.eT,{children:r})]})});f.displayName=l.ck.displayName,r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.Z0,{ref:s,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",t),...r})}).displayName=l.Z0.displayName},4740:function(e,s,t){"use strict";t.d(s,{g:function(){return n}});var a=t(8980),r=t(2208),l=t(2424);let n=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...r})});n.displayName="Textarea"},2424:function(e,s,t){"use strict";t.d(s,{Ox:function(){return i},cn:function(){return l},p6:function(){return n}});var a=t(2240),r=t(3946);function l(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,r.m6)((0,a.W)(s))}function n(e){return new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})}function i(){return Math.random().toString(36).substr(2,9)}}},function(e){e.O(0,[400,123,720,606,474,437,14,512,744],function(){return e(e.s=6520)}),_N_E=e.O()}]);