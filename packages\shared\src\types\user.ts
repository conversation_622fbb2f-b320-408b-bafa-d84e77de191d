import { z } from 'zod';
import { BaseEntitySchema } from './common';

// User role enum
export enum UserRole {
  ADMIN = 'admin',
  USER = 'user',
  VIEWER = 'viewer'
}

// User schema
export const UserSchema = BaseEntitySchema.extend({
  email: z.string().email(),
  name: z.string(),
  avatar: z.string().optional(),
  role: z.nativeEnum(UserRole),
  isActive: z.boolean().default(true),
  lastLoginAt: z.string().datetime().optional(),
  preferences: z.record(z.any()).optional()
});

export type User = z.infer<typeof UserSchema>;

// Workspace schema
export const WorkspaceSchema = BaseEntitySchema.extend({
  name: z.string(),
  description: z.string().optional(),
  ownerId: z.string().uuid(),
  isActive: z.boolean().default(true),
  settings: z.record(z.any()).optional()
});

export type Workspace = z.infer<typeof WorkspaceSchema>;

// Workspace member schema
export const WorkspaceMemberSchema = BaseEntitySchema.extend({
  workspaceId: z.string().uuid(),
  userId: z.string().uuid(),
  role: z.nativeEnum(UserRole),
  joinedAt: z.string().datetime()
});

export type WorkspaceMember = z.infer<typeof WorkspaceMemberSchema>;

// API key schema
export const ApiKeySchema = BaseEntitySchema.extend({
  name: z.string(),
  key: z.string(),
  userId: z.string().uuid(),
  workspaceId: z.string().uuid().optional(),
  isActive: z.boolean().default(true),
  expiresAt: z.string().datetime().optional(),
  lastUsedAt: z.string().datetime().optional(),
  permissions: z.array(z.string()).default([])
});

export type ApiKey = z.infer<typeof ApiKeySchema>;
