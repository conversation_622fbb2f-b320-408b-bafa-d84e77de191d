"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LIMITS = exports.SUPPORTED_FILE_TYPES = exports.VECTOR_DATABASES = exports.LLM_PROVIDERS = exports.NODE_COLORS = exports.NODE_CATEGORIES = exports.API_ENDPOINTS = void 0;
// API endpoints
exports.API_ENDPOINTS = {
    FLOWS: '/api/flows',
    NODES: '/api/nodes',
    CHAT: '/api/chat',
    AGENTS: '/api/agents',
    USERS: '/api/users',
    WORKSPACES: '/api/workspaces',
    EXECUTIONS: '/api/executions',
    TEMPLATES: '/api/templates'
};
// Node categories
exports.NODE_CATEGORIES = {
    LLM: 'Large Language Models',
    MEMORY: 'Memory',
    TOOLS: 'Tools',
    VECTOR_STORES: 'Vector Stores',
    DOCUMENT_LOADERS: 'Document Loaders',
    TEXT_SPLITTERS: 'Text Splitters',
    EMBEDDINGS: 'Embeddings',
    RETRIEVERS: 'Retrievers',
    CHAINS: 'Chains',
    AGENTS: 'Agents',
    OUTPUT_PARSERS: 'Output Parsers',
    PROMPT_TEMPLATES: 'Prompt Templates',
    UTILITIES: 'Utilities',
    CUSTOM: 'Custom'
};
// Default node colors
exports.NODE_COLORS = {
    LLM: '#3B82F6',
    MEMORY: '#10B981',
    TOOLS: '#F59E0B',
    VECTOR_STORES: '#8B5CF6',
    DOCUMENT_LOADERS: '#EC4899',
    TEXT_SPLITTERS: '#06B6D4',
    EMBEDDINGS: '#84CC16',
    RETRIEVERS: '#EF4444',
    CHAINS: '#6B7280',
    AGENTS: '#F97316',
    OUTPUT_PARSERS: '#14B8A6',
    PROMPT_TEMPLATES: '#A855F7',
    UTILITIES: '#64748B',
    CUSTOM: '#1F2937'
};
// Supported LLM providers
exports.LLM_PROVIDERS = {
    OPENAI: 'OpenAI',
    ANTHROPIC: 'Anthropic',
    GOOGLE: 'Google',
    AZURE_OPENAI: 'Azure OpenAI',
    HUGGING_FACE: 'Hugging Face',
    COHERE: 'Cohere',
    REPLICATE: 'Replicate',
    LOCAL: 'Local Models'
};
// Supported vector databases
exports.VECTOR_DATABASES = {
    PINECONE: 'Pinecone',
    CHROMA: 'Chroma',
    WEAVIATE: 'Weaviate',
    QDRANT: 'Qdrant',
    MILVUS: 'Milvus',
    FAISS: 'FAISS',
    REDIS: 'Redis',
    ELASTICSEARCH: 'Elasticsearch'
};
// File types supported for document loading
exports.SUPPORTED_FILE_TYPES = {
    PDF: '.pdf',
    DOCX: '.docx',
    DOC: '.doc',
    TXT: '.txt',
    MD: '.md',
    CSV: '.csv',
    JSON: '.json',
    XML: '.xml',
    HTML: '.html',
    RTF: '.rtf'
};
// Default limits
exports.LIMITS = {
    MAX_NODES_PER_FLOW: 100,
    MAX_CONNECTIONS_PER_NODE: 20,
    MAX_MESSAGE_LENGTH: 10000,
    MAX_FILE_SIZE: 50 * 1024 * 1024, // 50MB
    MAX_EXECUTION_TIME: 300000, // 5 minutes
    MAX_CHAT_HISTORY: 100
};
//# sourceMappingURL=index.js.map