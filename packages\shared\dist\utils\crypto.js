"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateUUID = generateUUID;
exports.generateSessionId = generateSessionId;
exports.generateApiKey = generateApiKey;
exports.hashString = hashString;
exports.generateRandomString = generateRandomString;
const crypto_1 = require("crypto");
/**
 * Generates a random UUID v4
 */
function generateUUID() {
    const bytes = (0, crypto_1.randomBytes)(16);
    bytes[6] = (bytes[6] & 0x0f) | 0x40; // Version 4
    bytes[8] = (bytes[8] & 0x3f) | 0x80; // Variant 10
    const hex = bytes.toString('hex');
    return [
        hex.substring(0, 8),
        hex.substring(8, 12),
        hex.substring(12, 16),
        hex.substring(16, 20),
        hex.substring(20, 32)
    ].join('-');
}
/**
 * Generates a random session ID
 */
function generateSessionId() {
    return (0, crypto_1.randomBytes)(16).toString('hex');
}
/**
 * Generates a random API key
 */
function generateApiKey() {
    return 'fc_' + (0, crypto_1.randomBytes)(32).toString('hex');
}
/**
 * Hashes a string using SHA-256
 */
function hashString(input) {
    return (0, crypto_1.createHash)('sha256').update(input).digest('hex');
}
/**
 * Generates a random string of specified length
 */
function generateRandomString(length) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}
//# sourceMappingURL=crypto.js.map