import { Request, Response } from 'express';
import { FlowService } from '../services/FlowService';

export class FlowController {
  private flowService: FlowService;

  constructor() {
    this.flowService = new FlowService();
  }

  getFlows = async (req: Request, res: Response) => {
    try {
      const userId = req.user?.id;
      if (!userId) {
        return res.status(401).json({ error: 'Unauthorized' });
      }
      const flows = await this.flowService.getFlowsByUser(userId);
      res.json(flows);
    } catch (error) {
      res.status(500).json({ error: 'Failed to get flows' });
    }
  };

  getFlow = async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const userId = req.user?.id;
      if (!userId) {
        return res.status(401).json({ error: 'Unauthorized' });
      }
      const flow = await this.flowService.getFlow(id, userId);

      if (!flow) {
        return res.status(404).json({ error: 'Flow not found' });
      }

      res.json(flow);
    } catch (error) {
      res.status(500).json({ error: 'Failed to get flow' });
    }
  };

  createFlow = async (req: Request, res: Response) => {
    try {
      const userId = req.user?.id;
      if (!userId) {
        return res.status(401).json({ error: 'Unauthorized' });
      }
      const flowData = req.body;
      const flow = await this.flowService.createFlow({ ...flowData, userId });
      res.status(201).json(flow);
    } catch (error) {
      res.status(500).json({ error: 'Failed to create flow' });
    }
  };

  updateFlow = async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const userId = req.user?.id;
      if (!userId) {
        return res.status(401).json({ error: 'Unauthorized' });
      }
      const updates = req.body;

      const flow = await this.flowService.updateFlow(id, userId, updates);

      if (!flow) {
        return res.status(404).json({ error: 'Flow not found' });
      }

      res.json(flow);
    } catch (error) {
      res.status(500).json({ error: 'Failed to update flow' });
    }
  };

  deleteFlow = async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const userId = req.user?.id;
      if (!userId) {
        return res.status(401).json({ error: 'Unauthorized' });
      }

      const success = await this.flowService.deleteFlow(id, userId);

      if (!success) {
        return res.status(404).json({ error: 'Flow not found' });
      }

      res.status(204).send();
    } catch (error) {
      res.status(500).json({ error: 'Failed to delete flow' });
    }
  };

  executeFlow = async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const { input, sessionId } = req.body;
      const userId = req.user?.id;
      if (!userId) {
        return res.status(401).json({ error: 'Unauthorized' });
      }

      const result = await this.flowService.executeFlow(id, userId, input, sessionId);
      res.json(result);
    } catch (error) {
      res.status(500).json({ error: 'Failed to execute flow' });
    }
  };

  validateFlow = async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const userId = req.user?.id;
      if (!userId) {
        return res.status(401).json({ error: 'Unauthorized' });
      }

      const validation = await this.flowService.validateFlow(id, userId);
      res.json(validation);
    } catch (error) {
      res.status(500).json({ error: 'Failed to validate flow' });
    }
  };

  duplicateFlow = async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const userId = req.user?.id;
      if (!userId) {
        return res.status(401).json({ error: 'Unauthorized' });
      }
      const { name } = req.body;

      const duplicatedFlow = await this.flowService.duplicateFlow(id, userId, name);

      if (!duplicatedFlow) {
        return res.status(404).json({ error: 'Flow not found' });
      }

      res.status(201).json(duplicatedFlow);
    } catch (error) {
      res.status(500).json({ error: 'Failed to duplicate flow' });
    }
  };
}
