"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-roving-focus@1.1.10_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1";
exports.ids = ["vendor-chunks/@radix-ui+react-roving-focus@1.1.10_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@radix-ui+react-roving-focus@1.1.10_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@radix-ui/react-roving-focus/dist/index.mjs":
/*!********************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@radix-ui+react-roving-focus@1.1.10_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@radix-ui/react-roving-focus/dist/index.mjs ***!
  \********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Item: () => (/* binding */ Item),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   RovingFocusGroup: () => (/* binding */ RovingFocusGroup),\n/* harmony export */   RovingFocusGroupItem: () => (/* binding */ RovingFocusGroupItem),\n/* harmony export */   createRovingFocusGroupScope: () => (/* binding */ createRovingFocusGroupScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+primitive@1.1.2/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-collection@1.1.7_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-compose-refs@1.1.2_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-context@1.1.2_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-id@1.1.1_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-primitive@2.1.3_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-use-callback-ref@1.1.1_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-use-controllable-state@1.2.2_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-direction@1.1.1_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Item,Root,RovingFocusGroup,RovingFocusGroupItem,createRovingFocusGroupScope auto */ // src/roving-focus-group.tsx\n\n\n\n\n\n\n\n\n\n\n\nvar ENTRY_FOCUS = \"rovingFocusGroup.onEntryFocus\";\nvar EVENT_OPTIONS = {\n    bubbles: false,\n    cancelable: true\n};\nvar GROUP_NAME = \"RovingFocusGroup\";\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_2__.createCollection)(GROUP_NAME);\nvar [createRovingFocusGroupContext, createRovingFocusGroupScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_3__.createContextScope)(GROUP_NAME, [\n    createCollectionScope\n]);\nvar [RovingFocusProvider, useRovingFocusContext] = createRovingFocusGroupContext(GROUP_NAME);\nvar RovingFocusGroup = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Provider, {\n        scope: props.__scopeRovingFocusGroup,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Slot, {\n            scope: props.__scopeRovingFocusGroup,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(RovingFocusGroupImpl, {\n                ...props,\n                ref: forwardedRef\n            })\n        })\n    });\n});\nRovingFocusGroup.displayName = GROUP_NAME;\nvar RovingFocusGroupImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeRovingFocusGroup, orientation, loop = false, dir, currentTabStopId: currentTabStopIdProp, defaultCurrentTabStopId, onCurrentTabStopIdChange, onEntryFocus, preventScrollOnEntryFocus = false, ...groupProps } = props;\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, ref);\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_5__.useDirection)(dir);\n    const [currentTabStopId, setCurrentTabStopId] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_6__.useControllableState)({\n        prop: currentTabStopIdProp,\n        defaultProp: defaultCurrentTabStopId ?? null,\n        onChange: onCurrentTabStopIdChange,\n        caller: GROUP_NAME\n    });\n    const [isTabbingBackOut, setIsTabbingBackOut] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const handleEntryFocus = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_7__.useCallbackRef)(onEntryFocus);\n    const getItems = useCollection(__scopeRovingFocusGroup);\n    const isClickFocusRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const [focusableItemsCount, setFocusableItemsCount] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const node = ref.current;\n        if (node) {\n            node.addEventListener(ENTRY_FOCUS, handleEntryFocus);\n            return ()=>node.removeEventListener(ENTRY_FOCUS, handleEntryFocus);\n        }\n    }, [\n        handleEntryFocus\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(RovingFocusProvider, {\n        scope: __scopeRovingFocusGroup,\n        orientation,\n        dir: direction,\n        loop,\n        currentTabStopId,\n        onItemFocus: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((tabStopId)=>setCurrentTabStopId(tabStopId), [\n            setCurrentTabStopId\n        ]),\n        onItemShiftTab: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>setIsTabbingBackOut(true), []),\n        onFocusableItemAdd: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>setFocusableItemsCount((prevCount)=>prevCount + 1), []),\n        onFocusableItemRemove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>setFocusableItemsCount((prevCount)=>prevCount - 1), []),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.div, {\n            tabIndex: isTabbingBackOut || focusableItemsCount === 0 ? -1 : 0,\n            \"data-orientation\": orientation,\n            ...groupProps,\n            ref: composedRefs,\n            style: {\n                outline: \"none\",\n                ...props.style\n            },\n            onMouseDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__.composeEventHandlers)(props.onMouseDown, ()=>{\n                isClickFocusRef.current = true;\n            }),\n            onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__.composeEventHandlers)(props.onFocus, (event)=>{\n                const isKeyboardFocus = !isClickFocusRef.current;\n                if (event.target === event.currentTarget && isKeyboardFocus && !isTabbingBackOut) {\n                    const entryFocusEvent = new CustomEvent(ENTRY_FOCUS, EVENT_OPTIONS);\n                    event.currentTarget.dispatchEvent(entryFocusEvent);\n                    if (!entryFocusEvent.defaultPrevented) {\n                        const items = getItems().filter((item)=>item.focusable);\n                        const activeItem = items.find((item)=>item.active);\n                        const currentItem = items.find((item)=>item.id === currentTabStopId);\n                        const candidateItems = [\n                            activeItem,\n                            currentItem,\n                            ...items\n                        ].filter(Boolean);\n                        const candidateNodes = candidateItems.map((item)=>item.ref.current);\n                        focusFirst(candidateNodes, preventScrollOnEntryFocus);\n                    }\n                }\n                isClickFocusRef.current = false;\n            }),\n            onBlur: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__.composeEventHandlers)(props.onBlur, ()=>setIsTabbingBackOut(false))\n        })\n    });\n});\nvar ITEM_NAME = \"RovingFocusGroupItem\";\nvar RovingFocusGroupItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeRovingFocusGroup, focusable = true, active = false, tabStopId, children, ...itemProps } = props;\n    const autoId = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_10__.useId)();\n    const id = tabStopId || autoId;\n    const context = useRovingFocusContext(ITEM_NAME, __scopeRovingFocusGroup);\n    const isCurrentTabStop = context.currentTabStopId === id;\n    const getItems = useCollection(__scopeRovingFocusGroup);\n    const { onFocusableItemAdd, onFocusableItemRemove, currentTabStopId } = context;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (focusable) {\n            onFocusableItemAdd();\n            return ()=>onFocusableItemRemove();\n        }\n    }, [\n        focusable,\n        onFocusableItemAdd,\n        onFocusableItemRemove\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.ItemSlot, {\n        scope: __scopeRovingFocusGroup,\n        id,\n        focusable,\n        active,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.span, {\n            tabIndex: isCurrentTabStop ? 0 : -1,\n            \"data-orientation\": context.orientation,\n            ...itemProps,\n            ref: forwardedRef,\n            onMouseDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__.composeEventHandlers)(props.onMouseDown, (event)=>{\n                if (!focusable) event.preventDefault();\n                else context.onItemFocus(id);\n            }),\n            onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__.composeEventHandlers)(props.onFocus, ()=>context.onItemFocus(id)),\n            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                if (event.key === \"Tab\" && event.shiftKey) {\n                    context.onItemShiftTab();\n                    return;\n                }\n                if (event.target !== event.currentTarget) return;\n                const focusIntent = getFocusIntent(event, context.orientation, context.dir);\n                if (focusIntent !== void 0) {\n                    if (event.metaKey || event.ctrlKey || event.altKey || event.shiftKey) return;\n                    event.preventDefault();\n                    const items = getItems().filter((item)=>item.focusable);\n                    let candidateNodes = items.map((item)=>item.ref.current);\n                    if (focusIntent === \"last\") candidateNodes.reverse();\n                    else if (focusIntent === \"prev\" || focusIntent === \"next\") {\n                        if (focusIntent === \"prev\") candidateNodes.reverse();\n                        const currentIndex = candidateNodes.indexOf(event.currentTarget);\n                        candidateNodes = context.loop ? wrapArray(candidateNodes, currentIndex + 1) : candidateNodes.slice(currentIndex + 1);\n                    }\n                    setTimeout(()=>focusFirst(candidateNodes));\n                }\n            }),\n            children: typeof children === \"function\" ? children({\n                isCurrentTabStop,\n                hasTabStop: currentTabStopId != null\n            }) : children\n        })\n    });\n});\nRovingFocusGroupItem.displayName = ITEM_NAME;\nvar MAP_KEY_TO_FOCUS_INTENT = {\n    ArrowLeft: \"prev\",\n    ArrowUp: \"prev\",\n    ArrowRight: \"next\",\n    ArrowDown: \"next\",\n    PageUp: \"first\",\n    Home: \"first\",\n    PageDown: \"last\",\n    End: \"last\"\n};\nfunction getDirectionAwareKey(key, dir) {\n    if (dir !== \"rtl\") return key;\n    return key === \"ArrowLeft\" ? \"ArrowRight\" : key === \"ArrowRight\" ? \"ArrowLeft\" : key;\n}\nfunction getFocusIntent(event, orientation, dir) {\n    const key = getDirectionAwareKey(event.key, dir);\n    if (orientation === \"vertical\" && [\n        \"ArrowLeft\",\n        \"ArrowRight\"\n    ].includes(key)) return void 0;\n    if (orientation === \"horizontal\" && [\n        \"ArrowUp\",\n        \"ArrowDown\"\n    ].includes(key)) return void 0;\n    return MAP_KEY_TO_FOCUS_INTENT[key];\n}\nfunction focusFirst(candidates, preventScroll = false) {\n    const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n    for (const candidate of candidates){\n        if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n        candidate.focus({\n            preventScroll\n        });\n        if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n    }\n}\nfunction wrapArray(array, startIndex) {\n    return array.map((_, index)=>array[(startIndex + index) % array.length]);\n}\nvar Root = RovingFocusGroup;\nvar Item = RovingFocusGroupItem;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@radix-ui+react-roving-focus@1.1.10_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@radix-ui/react-roving-focus/dist/index.mjs\n");

/***/ })

};
;