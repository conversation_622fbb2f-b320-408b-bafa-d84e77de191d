2025-06-28T15:27:33.607063Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-06-28T15:27:33.637295Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\ui\\src\\store"), AnchoredSystemPathBuf("packages\\ui\\src\\components\\ui"), AnchoredSystemPathBuf("packages\\ui\\src\\app\\flows"), AnchoredSystemPathBuf("packages\\ui\\src\\lib"), AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie"), AnchoredSystemPathBuf(".turbo\\cookies\\.turbo-cookie"), AnchoredSystemPathBuf(".turbo"), AnchoredSystemPathBuf(".turbo\\daemon"), AnchoredSystemPathBuf("packages\\ui\\src\\app"), AnchoredSystemPathBuf("packages\\shared\\src\\utils"), AnchoredSystemPathBuf("packages\\ui\\src\\components\\chat")}
2025-06-28T15:27:33.637375Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@flowwise-clone/ui"), path: AnchoredSystemPathBuf("packages\\ui") }, WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("@flowwise-clone/shared"), path: AnchoredSystemPathBuf("packages\\shared") }}))
2025-06-28T15:27:33.741179Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf("packages\\shared"), AnchoredSystemPathBuf(".turbo"), AnchoredSystemPathBuf(".turbo\\cookies\\2.cookie"), AnchoredSystemPathBuf("packages\\shared\\.turbo")}
2025-06-28T15:27:33.741250Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("@flowwise-clone/shared"), path: AnchoredSystemPathBuf("packages\\shared") }}))
2025-06-28T15:27:33.741356Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-28T15:27:34.140885Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:27:34.140919Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:27:34.241149Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:27:34.241224Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:27:36.836240Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\shared")}
2025-06-28T15:27:36.836264Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@flowwise-clone/shared"), path: AnchoredSystemPathBuf("packages\\shared") }}))
2025-06-28T15:27:36.861643Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-28T15:27:37.238775Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\ui\\.turbo"), AnchoredSystemPathBuf("packages\\server"), AnchoredSystemPathBuf(".turbo\\cookies\\4.cookie"), AnchoredSystemPathBuf(".turbo\\cookies\\5.cookie"), AnchoredSystemPathBuf("packages\\ui"), AnchoredSystemPathBuf("packages\\server\\.turbo"), AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo\\cookies\\3.cookie")}
2025-06-28T15:27:37.238806Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@flowwise-clone/ui"), path: AnchoredSystemPathBuf("packages\\ui") }, WorkspacePackage { name: Other("@flowwise-clone/server"), path: AnchoredSystemPathBuf("packages\\server") }, WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:27:37.259184Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-28T15:27:40.240439Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\ui")}
2025-06-28T15:27:40.240467Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@flowwise-clone/ui"), path: AnchoredSystemPathBuf("packages\\ui") }}))
2025-06-28T15:27:40.240535Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-28T15:27:40.542884Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\server")}
2025-06-28T15:27:40.542917Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@flowwise-clone/server"), path: AnchoredSystemPathBuf("packages\\server") }}))
2025-06-28T15:27:40.542982Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-28T15:28:11.140296Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\server\\src\\routes"), AnchoredSystemPathBuf("packages\\server\\src\\routes\\flows.ts")}
2025-06-28T15:28:11.140324Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@flowwise-clone/server"), path: AnchoredSystemPathBuf("packages\\server") }}))
2025-06-28T15:28:27.838159Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\server\\src\\controllers\\FlowController.ts"), AnchoredSystemPathBuf("packages\\server\\src\\controllers")}
2025-06-28T15:28:27.838186Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@flowwise-clone/server"), path: AnchoredSystemPathBuf("packages\\server") }}))
2025-06-28T15:28:41.738195Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\server\\src\\services\\FlowService.ts")}
2025-06-28T15:28:41.738218Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@flowwise-clone/server"), path: AnchoredSystemPathBuf("packages\\server") }}))
2025-06-28T15:28:41.838158Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\server\\src\\services")}
2025-06-28T15:28:41.838181Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@flowwise-clone/server"), path: AnchoredSystemPathBuf("packages\\server") }}))
2025-06-28T15:28:41.838226Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-28T15:28:50.338863Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\server\\src\\routes"), AnchoredSystemPathBuf("packages\\server\\src\\routes\\nodes.ts")}
2025-06-28T15:28:50.338885Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@flowwise-clone/server"), path: AnchoredSystemPathBuf("packages\\server") }}))
2025-06-28T15:28:56.838558Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\server\\src\\routes\\chat.ts"), AnchoredSystemPathBuf("packages\\server\\src\\routes")}
2025-06-28T15:28:56.838579Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@flowwise-clone/server"), path: AnchoredSystemPathBuf("packages\\server") }}))
2025-06-28T15:29:03.937850Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\server\\src\\routes\\agents.ts"), AnchoredSystemPathBuf("packages\\server\\src\\routes")}
2025-06-28T15:29:03.937872Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@flowwise-clone/server"), path: AnchoredSystemPathBuf("packages\\server") }}))
2025-06-28T15:29:11.336736Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\server\\src\\routes\\users.ts")}
2025-06-28T15:29:11.336775Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@flowwise-clone/server"), path: AnchoredSystemPathBuf("packages\\server") }}))
2025-06-28T15:29:11.439050Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\server\\src\\routes")}
2025-06-28T15:29:11.439070Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@flowwise-clone/server"), path: AnchoredSystemPathBuf("packages\\server") }}))
2025-06-28T15:29:11.439098Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-28T15:29:17.946278Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\server\\src\\routes\\workspaces.ts")}
2025-06-28T15:29:17.946300Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@flowwise-clone/server"), path: AnchoredSystemPathBuf("packages\\server") }}))
2025-06-28T15:29:18.039347Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\server\\src\\routes")}
2025-06-28T15:29:18.039373Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@flowwise-clone/server"), path: AnchoredSystemPathBuf("packages\\server") }}))
2025-06-28T15:29:18.039409Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-28T15:29:26.046196Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\server\\src\\routes"), AnchoredSystemPathBuf("packages\\server\\src\\routes\\executions.ts")}
2025-06-28T15:29:26.046215Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@flowwise-clone/server"), path: AnchoredSystemPathBuf("packages\\server") }}))
2025-06-28T15:29:33.040090Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\server\\src\\routes\\templates.ts"), AnchoredSystemPathBuf("packages\\server\\src\\routes")}
2025-06-28T15:29:33.040112Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@flowwise-clone/server"), path: AnchoredSystemPathBuf("packages\\server") }}))
2025-06-28T15:29:41.046444Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\server\\src\\middleware\\errorHandler.ts"), AnchoredSystemPathBuf("packages\\server\\src\\middleware")}
2025-06-28T15:29:41.046465Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@flowwise-clone/server"), path: AnchoredSystemPathBuf("packages\\server") }}))
2025-06-28T15:29:50.536540Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\server\\src\\middleware"), AnchoredSystemPathBuf("packages\\server\\src\\middleware\\rateLimiter.ts")}
2025-06-28T15:29:50.536561Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@flowwise-clone/server"), path: AnchoredSystemPathBuf("packages\\server") }}))
2025-06-28T15:30:00.375648Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\server\\src\\middleware\\auth.ts"), AnchoredSystemPathBuf("packages\\server\\src\\middleware")}
2025-06-28T15:30:00.375672Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@flowwise-clone/server"), path: AnchoredSystemPathBuf("packages\\server") }}))
2025-06-28T15:30:11.638559Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\server\\src\\services\\database.ts")}
2025-06-28T15:30:11.638591Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@flowwise-clone/server"), path: AnchoredSystemPathBuf("packages\\server") }}))
2025-06-28T15:30:11.750199Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\server\\src\\services")}
2025-06-28T15:30:11.750214Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@flowwise-clone/server"), path: AnchoredSystemPathBuf("packages\\server") }}))
2025-06-28T15:30:11.750242Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-28T15:30:19.251215Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\server\\src\\services"), AnchoredSystemPathBuf("packages\\server\\src\\services\\socket.ts")}
2025-06-28T15:30:19.251239Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@flowwise-clone/server"), path: AnchoredSystemPathBuf("packages\\server") }}))
2025-06-28T15:30:29.771740Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies\\.turbo-cookie"), AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie"), AnchoredSystemPathBuf(".turbo\\cookies")}
2025-06-28T15:30:29.771797Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:30:29.861351Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-06-28T15:30:29.869175Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo")}
2025-06-28T15:30:29.869215Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:30:29.998766Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies\\2.cookie"), AnchoredSystemPathBuf(".turbo\\cookies\\3.cookie"), AnchoredSystemPathBuf(".turbo\\cookies")}
2025-06-28T15:30:29.998788Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:30:30.168533Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies\\4.cookie"), AnchoredSystemPathBuf(".turbo\\cookies\\5.cookie"), AnchoredSystemPathBuf(".turbo\\cookies")}
2025-06-28T15:30:30.168633Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:30:30.868533Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:30:30.868609Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:30:48.882157Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\server\\src\\controllers\\FlowController.ts")}
2025-06-28T15:30:48.882181Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@flowwise-clone/server"), path: AnchoredSystemPathBuf("packages\\server") }}))
2025-06-28T15:31:02.071791Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\server\\src\\controllers\\FlowController.ts")}
2025-06-28T15:31:02.071804Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@flowwise-clone/server"), path: AnchoredSystemPathBuf("packages\\server") }}))
2025-06-28T15:31:13.666955Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\server\\src\\services\\FlowService.ts")}
2025-06-28T15:31:13.666976Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@flowwise-clone/server"), path: AnchoredSystemPathBuf("packages\\server") }}))
2025-06-28T15:31:13.781882Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\server\\src\\services\\FlowService.ts")}
2025-06-28T15:31:13.781898Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@flowwise-clone/server"), path: AnchoredSystemPathBuf("packages\\server") }}))
2025-06-28T15:31:13.781938Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-28T15:31:24.780688Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\server\\src\\services\\FlowService.ts")}
2025-06-28T15:31:24.780711Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@flowwise-clone/server"), path: AnchoredSystemPathBuf("packages\\server") }}))
2025-06-28T15:31:35.082332Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\server\\src\\services\\FlowService.ts")}
2025-06-28T15:31:35.082359Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@flowwise-clone/server"), path: AnchoredSystemPathBuf("packages\\server") }}))
2025-06-28T15:31:49.168672Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\server\\src\\controllers\\FlowController.ts")}
2025-06-28T15:31:49.168701Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@flowwise-clone/server"), path: AnchoredSystemPathBuf("packages\\server") }}))
2025-06-28T15:32:03.667158Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\server\\src\\controllers\\FlowController.ts")}
2025-06-28T15:32:03.667178Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@flowwise-clone/server"), path: AnchoredSystemPathBuf("packages\\server") }}))
2025-06-28T15:32:17.170941Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\server\\src\\controllers\\FlowController.ts")}
2025-06-28T15:32:17.170963Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@flowwise-clone/server"), path: AnchoredSystemPathBuf("packages\\server") }}))
2025-06-28T15:36:57.680718Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:36:57.680733Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:36:59.271913Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:36:59.271927Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:36:59.975692Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:36:59.975710Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:00.278130Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:00.278156Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:00.375426Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:00.375465Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:00.569258Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:00.569278Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:00.667950Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:00.667971Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:00.876623Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:00.876636Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:01.068193Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:01.068213Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:01.167568Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:01.167586Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:01.377872Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:01.377893Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:01.474107Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:01.474128Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:01.668592Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:01.668610Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:01.882317Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:01.882353Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:01.981004Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:01.981045Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:02.176351Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:02.176370Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:02.280415Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:02.280445Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:02.481873Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:02.481903Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:02.575290Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:02.575306Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:02.783238Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:02.783267Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:02.975891Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:02.975912Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:03.068834Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:03.068863Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:03.268613Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:03.268631Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:03.382636Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:03.382681Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:03.579564Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:03.579584Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:03.772694Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:03.772708Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:03.882321Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:03.882336Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:04.070509Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:04.070526Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:04.182542Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:04.182580Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:04.383834Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:04.383851Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:04.479749Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:04.479777Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:04.673185Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:04.673216Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:04.770446Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:04.770461Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:04.981779Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:04.981799Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:05.180265Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:05.180285Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:05.373798Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:05.373822Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:05.468167Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:05.468186Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:05.682056Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:05.682090Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:05.777994Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:05.778014Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:05.968563Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:05.968596Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:06.082397Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:06.082426Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:06.277863Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:06.277886Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:06.375438Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:06.375455Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:06.579295Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:06.579314Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:06.676435Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:06.676454Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:06.872447Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:06.872483Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:06.968087Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:06.968109Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:07.178712Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:07.178731Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:07.275674Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:07.275691Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:07.468333Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:07.468351Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:07.567393Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:07.567416Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:07.780394Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:07.780422Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:07.877449Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:07.877480Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:08.068258Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:08.068285Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:08.178081Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:08.178099Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:08.371391Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:08.371410Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:08.468903Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:08.468929Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:08.681606Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:08.681636Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:08.778879Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:08.778904Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:08.971977Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:08.972010Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:09.068282Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:09.068299Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:09.267858Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:09.267874Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:09.368008Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:09.368040Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:09.579584Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:09.579605Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:09.682220Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:09.682257Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:09.875019Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:09.875035Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:10.174394Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:10.174417Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:10.395176Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:10.395231Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:10.571217Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:10.571239Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:10.801453Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:10.801475Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:10.979455Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:10.979479Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:11.079833Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:11.079856Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:11.415462Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:11.415489Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:11.656982Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:11.657004Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:12.053769Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:12.053791Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:12.476002Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:12.476025Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:12.934353Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:12.934392Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:13.077004Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:13.077025Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:37:13.401794Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:37:13.401817Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:38:55.972226Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:38:55.972239Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-28T15:38:56.073612Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\1531d4ff095c7ec1-turbo.log.2025-06-28")}
2025-06-28T15:38:56.073631Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
