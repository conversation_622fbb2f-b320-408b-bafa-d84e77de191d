"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.StreamingChatResponseSchema = exports.ChatResponseSchema = exports.ChatRequestSchema = exports.ChatSessionSchema = exports.MessageSchema = exports.MessageRole = void 0;
const zod_1 = require("zod");
const common_1 = require("./common");
// Message role enum
var MessageRole;
(function (MessageRole) {
    MessageRole["USER"] = "user";
    MessageRole["ASSISTANT"] = "assistant";
    MessageRole["SYSTEM"] = "system";
    MessageRole["FUNCTION"] = "function";
    MessageRole["TOOL"] = "tool";
})(MessageRole || (exports.MessageRole = MessageRole = {}));
// Message schema
exports.MessageSchema = common_1.BaseEntitySchema.extend({
    role: zod_1.z.nativeEnum(MessageRole),
    content: zod_1.z.string(),
    name: zod_1.z.string().optional(),
    functionCall: zod_1.z.object({
        name: zod_1.z.string(),
        arguments: zod_1.z.string()
    }).optional(),
    toolCalls: zod_1.z.array(zod_1.z.object({
        id: zod_1.z.string(),
        type: zod_1.z.literal('function'),
        function: zod_1.z.object({
            name: zod_1.z.string(),
            arguments: zod_1.z.string()
        })
    })).optional(),
    toolCallId: zod_1.z.string().optional(),
    metadata: zod_1.z.record(zod_1.z.any()).optional()
});
// Chat session schema
exports.ChatSessionSchema = common_1.BaseEntitySchema.extend({
    flowId: zod_1.z.string().uuid(),
    userId: zod_1.z.string().uuid().optional(),
    sessionId: zod_1.z.string(),
    title: zod_1.z.string().optional(),
    messages: zod_1.z.array(exports.MessageSchema),
    isActive: zod_1.z.boolean().default(true),
    metadata: zod_1.z.record(zod_1.z.any()).optional()
});
// Chat request schema
exports.ChatRequestSchema = zod_1.z.object({
    message: zod_1.z.string(),
    sessionId: zod_1.z.string().optional(),
    flowId: zod_1.z.string().uuid(),
    userId: zod_1.z.string().uuid().optional(),
    streaming: zod_1.z.boolean().default(false),
    metadata: zod_1.z.record(zod_1.z.any()).optional()
});
// Chat response schema
exports.ChatResponseSchema = zod_1.z.object({
    message: exports.MessageSchema,
    sessionId: zod_1.z.string(),
    isComplete: zod_1.z.boolean().default(true),
    metadata: zod_1.z.record(zod_1.z.any()).optional()
});
// Streaming chat response schema
exports.StreamingChatResponseSchema = zod_1.z.object({
    delta: zod_1.z.string(),
    sessionId: zod_1.z.string(),
    isComplete: zod_1.z.boolean(),
    metadata: zod_1.z.record(zod_1.z.any()).optional()
});
//# sourceMappingURL=chat.js.map