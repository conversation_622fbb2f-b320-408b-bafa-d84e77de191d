'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Bo<PERSON>, 
  Workflow, 
  Users, 
  Zap, 
  ArrowRight,
  Github,
  Star
} from 'lucide-react';
import Link from 'next/link';

export default function HomePage() {
  const router = useRouter();

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Bot className="h-8 w-8 text-blue-600" />
            <span className="text-xl font-bold">Flowwise Clone</span>
          </div>
          <div className="flex items-center space-x-4">
            <Button variant="ghost" asChild>
              <Link href="/docs">Documentation</Link>
            </Button>
            <Button variant="ghost" asChild>
              <Link href="https://github.com/your-repo/flowwise-clone" target="_blank">
                <Github className="h-4 w-4 mr-2" />
                GitHub
              </Link>
            </Button>
            <Button asChild>
              <Link href="/flows">Get Started</Link>
            </Button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="container mx-auto px-4 py-20 text-center">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-5xl font-bold text-gray-900 mb-6">
            Build AI Agents,{' '}
            <span className="text-blue-600">Visually</span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Create powerful AI agents, chatbots, and multi-agent systems through an intuitive 
            drag-and-drop interface. No coding required.
          </p>
          <div className="flex items-center justify-center space-x-4">
            <Button size="lg" onClick={() => router.push('/flows')}>
              Start Building
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
            <Button variant="outline" size="lg" asChild>
              <Link href="/templates">View Templates</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="container mx-auto px-4 py-20">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Everything you need to build AI agents
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            From simple chatbots to complex multi-agent systems, our platform provides 
            all the tools you need.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <Card>
            <CardHeader>
              <Workflow className="h-8 w-8 text-blue-600 mb-2" />
              <CardTitle>Visual Flow Builder</CardTitle>
              <CardDescription>
                Drag-and-drop interface for building complex AI workflows without code
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Node-based editor</li>
                <li>• Real-time validation</li>
                <li>• Template library</li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <Users className="h-8 w-8 text-green-600 mb-2" />
              <CardTitle>Multi-Agent Systems</CardTitle>
              <CardDescription>
                Coordinate multiple AI agents for complex task orchestration
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Agent coordination</li>
                <li>• Task distribution</li>
                <li>• Workflow management</li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <Bot className="h-8 w-8 text-purple-600 mb-2" />
              <CardTitle>LLM Integrations</CardTitle>
              <CardDescription>
                Connect to OpenAI, Anthropic, Google, and other LLM providers
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Multiple providers</li>
                <li>• Streaming support</li>
                <li>• Custom models</li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <Zap className="h-8 w-8 text-yellow-600 mb-2" />
              <CardTitle>RAG & Vector Search</CardTitle>
              <CardDescription>
                Integrate with vector databases for knowledge retrieval
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Document processing</li>
                <li>• Vector embeddings</li>
                <li>• Semantic search</li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <Star className="h-8 w-8 text-orange-600 mb-2" />
              <CardTitle>Human-in-the-Loop</CardTitle>
              <CardDescription>
                Add human approval workflows and manual intervention
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Approval workflows</li>
                <li>• Task review</li>
                <li>• Manual override</li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <ArrowRight className="h-8 w-8 text-red-600 mb-2" />
              <CardTitle>API & Integrations</CardTitle>
              <CardDescription>
                REST APIs, SDKs, and embedded chat widgets
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• REST API</li>
                <li>• TypeScript SDK</li>
                <li>• Embedded widgets</li>
              </ul>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-blue-600 text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">
            Ready to build your first AI agent?
          </h2>
          <p className="text-xl mb-8 opacity-90">
            Join thousands of developers building the future of AI
          </p>
          <Button size="lg" variant="secondary" onClick={() => router.push('/flows')}>
            Get Started for Free
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <Bot className="h-6 w-6" />
                <span className="text-lg font-bold">Flowwise Clone</span>
              </div>
              <p className="text-gray-400">
                Visual AI agent builder for the modern web
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Product</h3>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="/features">Features</Link></li>
                <li><Link href="/templates">Templates</Link></li>
                <li><Link href="/pricing">Pricing</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Resources</h3>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="/docs">Documentation</Link></li>
                <li><Link href="/examples">Examples</Link></li>
                <li><Link href="/community">Community</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Company</h3>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="/about">About</Link></li>
                <li><Link href="/contact">Contact</Link></li>
                <li><Link href="/privacy">Privacy</Link></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 Flowwise Clone. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
