(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[437],{3454:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});let r=(0,n(5069).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},2315:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});let r=(0,n(5069).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},3389:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});let r=(0,n(5069).Z)("Bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]])},6844:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});let r=(0,n(5069).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},3814:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});let r=(0,n(5069).Z)("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]])},4838:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});let r=(0,n(5069).Z)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},2820:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});let r=(0,n(5069).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},860:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});let r=(0,n(5069).Z)("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]])},679:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});let r=(0,n(5069).Z)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},7343:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});let r=(0,n(5069).Z)("Link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]])},805:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});let r=(0,n(5069).Z)("Maximize",[["path",{d:"M8 3H5a2 2 0 0 0-2 2v3",key:"1dcmit"}],["path",{d:"M21 8V5a2 2 0 0 0-2-2h-3",key:"1e4gt3"}],["path",{d:"M3 16v3a2 2 0 0 0 2 2h3",key:"wsl5sc"}],["path",{d:"M16 21h3a2 2 0 0 0 2-2v-3",key:"18trek"}]])},3659:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});let r=(0,n(5069).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},6767:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});let r=(0,n(5069).Z)("PanelRight",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["line",{x1:"15",x2:"15",y1:"3",y2:"21",key:"1hpv9i"}]])},7107:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});let r=(0,n(5069).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},4385:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});let r=(0,n(5069).Z)("Scissors",[["circle",{cx:"6",cy:"6",r:"3",key:"1lh9wr"}],["path",{d:"M8.12 8.12 12 12",key:"1alkpv"}],["path",{d:"M20 4 8.12 15.88",key:"xgtan2"}],["circle",{cx:"6",cy:"18",r:"3",key:"fqmcym"}],["path",{d:"M14.8 14.8 20 20",key:"ptml3r"}]])},3250:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});let r=(0,n(5069).Z)("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]])},199:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});let r=(0,n(5069).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},9483:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});let r=(0,n(5069).Z)("Share",[["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["polyline",{points:"16 6 12 2 8 6",key:"m901s6"}],["line",{x1:"12",x2:"12",y1:"2",y2:"15",key:"1p0rca"}]])},4723:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});let r=(0,n(5069).Z)("Square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]])},2183:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});let r=(0,n(5069).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},4784:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});let r=(0,n(5069).Z)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},8323:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});let r=(0,n(5069).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},4279:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});let r=(0,n(5069).Z)("Wrench",[["path",{d:"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z",key:"cbrjhi"}]])},6984:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});let r=(0,n(5069).Z)("ZoomIn",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"11",x2:"11",y1:"8",y2:"14",key:"1vmskp"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]])},5317:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});let r=(0,n(5069).Z)("ZoomOut",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]])},7931:function(t,e,n){"use strict";var r=n(2208),i="function"==typeof Object.is?Object.is:function(t,e){return t===e&&(0!==t||1/t==1/e)||t!=t&&e!=e},o=r.useState,a=r.useEffect,u=r.useLayoutEffect,s=r.useDebugValue;function l(t){var e=t.getSnapshot;t=t.value;try{var n=e();return!i(t,n)}catch(t){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(t,e){return e()}:function(t,e){var n=e(),r=o({inst:{value:n,getSnapshot:e}}),i=r[0].inst,c=r[1];return u(function(){i.value=n,i.getSnapshot=e,l(i)&&c({inst:i})},[t,n,e]),a(function(){return l(i)&&c({inst:i}),t(function(){l(i)&&c({inst:i})})},[t]),s(n),n};e.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:c},5605:function(t,e,n){"use strict";var r=n(2208),i=n(9029),o="function"==typeof Object.is?Object.is:function(t,e){return t===e&&(0!==t||1/t==1/e)||t!=t&&e!=e},a=i.useSyncExternalStore,u=r.useRef,s=r.useEffect,l=r.useMemo,c=r.useDebugValue;e.useSyncExternalStoreWithSelector=function(t,e,n,r,i){var f=u(null);if(null===f.current){var h={hasValue:!1,value:null};f.current=h}else h=f.current;var d=a(t,(f=l(function(){function t(t){if(!s){if(s=!0,a=t,t=r(t),void 0!==i&&h.hasValue){var e=h.value;if(i(e,t))return u=e}return u=t}if(e=u,o(a,t))return e;var n=r(t);return void 0!==i&&i(e,n)?(a=t,e):(a=t,u=n)}var a,u,s=!1,l=void 0===n?null:n;return[function(){return t(e())},null===l?void 0:function(){return t(l())}]},[e,n,r,i]))[0],f[1]);return s(function(){h.hasValue=!0,h.value=d},[d]),c(d),d}},9029:function(t,e,n){"use strict";t.exports=n(7931)},2801:function(t,e,n){"use strict";t.exports=n(5605)},6609:function(){},7565:function(t,e,n){"use strict";n.d(e,{VY:function(){return W},aV:function(){return Y},fC:function(){return L},xz:function(){return U}});var r=n(2208),i=n(6734),o=n(662),a=n(3151),u=n(916),s=n(3102),l=n(7976),c=n(5696),f=n(9350),h=n(8044),d=n(8980),p="rovingFocusGroup.onEntryFocus",v={bubbles:!1,cancelable:!0},y="RovingFocusGroup",[m,g,_]=(0,a.B)(y),[w,b]=(0,o.b)(y,[_]),[x,k]=w(y),E=r.forwardRef((t,e)=>(0,d.jsx)(m.Provider,{scope:t.__scopeRovingFocusGroup,children:(0,d.jsx)(m.Slot,{scope:t.__scopeRovingFocusGroup,children:(0,d.jsx)(M,{...t,ref:e})})}));E.displayName=y;var M=r.forwardRef((t,e)=>{let{__scopeRovingFocusGroup:n,orientation:o,loop:a=!1,dir:s,currentTabStopId:m,defaultCurrentTabStopId:_,onCurrentTabStopIdChange:w,onEntryFocus:b,preventScrollOnEntryFocus:k=!1,...E}=t,M=r.useRef(null),S=(0,u.e)(e,M),Z=(0,h.gm)(s),[A,N]=(0,f.T)({prop:m,defaultProp:null!=_?_:null,onChange:w,caller:y}),[C,T]=r.useState(!1),$=(0,c.W)(b),R=g(n),P=r.useRef(!1),[j,D]=r.useState(0);return r.useEffect(()=>{let t=M.current;if(t)return t.addEventListener(p,$),()=>t.removeEventListener(p,$)},[$]),(0,d.jsx)(x,{scope:n,orientation:o,dir:Z,loop:a,currentTabStopId:A,onItemFocus:r.useCallback(t=>N(t),[N]),onItemShiftTab:r.useCallback(()=>T(!0),[]),onFocusableItemAdd:r.useCallback(()=>D(t=>t+1),[]),onFocusableItemRemove:r.useCallback(()=>D(t=>t-1),[]),children:(0,d.jsx)(l.WV.div,{tabIndex:C||0===j?-1:0,"data-orientation":o,...E,ref:S,style:{outline:"none",...t.style},onMouseDown:(0,i.M)(t.onMouseDown,()=>{P.current=!0}),onFocus:(0,i.M)(t.onFocus,t=>{let e=!P.current;if(t.target===t.currentTarget&&e&&!C){let e=new CustomEvent(p,v);if(t.currentTarget.dispatchEvent(e),!e.defaultPrevented){let t=R().filter(t=>t.focusable);z([t.find(t=>t.active),t.find(t=>t.id===A),...t].filter(Boolean).map(t=>t.ref.current),k)}}P.current=!1}),onBlur:(0,i.M)(t.onBlur,()=>T(!1))})})}),S="RovingFocusGroupItem",Z=r.forwardRef((t,e)=>{let{__scopeRovingFocusGroup:n,focusable:o=!0,active:a=!1,tabStopId:u,children:c,...f}=t,h=(0,s.M)(),p=u||h,v=k(S,n),y=v.currentTabStopId===p,_=g(n),{onFocusableItemAdd:w,onFocusableItemRemove:b,currentTabStopId:x}=v;return r.useEffect(()=>{if(o)return w(),()=>b()},[o,w,b]),(0,d.jsx)(m.ItemSlot,{scope:n,id:p,focusable:o,active:a,children:(0,d.jsx)(l.WV.span,{tabIndex:y?0:-1,"data-orientation":v.orientation,...f,ref:e,onMouseDown:(0,i.M)(t.onMouseDown,t=>{o?v.onItemFocus(p):t.preventDefault()}),onFocus:(0,i.M)(t.onFocus,()=>v.onItemFocus(p)),onKeyDown:(0,i.M)(t.onKeyDown,t=>{if("Tab"===t.key&&t.shiftKey){v.onItemShiftTab();return}if(t.target!==t.currentTarget)return;let e=function(t,e,n){var r;let i=(r=t.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===e&&["ArrowLeft","ArrowRight"].includes(i))&&!("horizontal"===e&&["ArrowUp","ArrowDown"].includes(i)))return A[i]}(t,v.orientation,v.dir);if(void 0!==e){if(t.metaKey||t.ctrlKey||t.altKey||t.shiftKey)return;t.preventDefault();let i=_().filter(t=>t.focusable).map(t=>t.ref.current);if("last"===e)i.reverse();else if("prev"===e||"next"===e){var n,r;"prev"===e&&i.reverse();let o=i.indexOf(t.currentTarget);i=v.loop?(n=i,r=o+1,n.map((t,e)=>n[(r+e)%n.length])):i.slice(o+1)}setTimeout(()=>z(i))}}),children:"function"==typeof c?c({isCurrentTabStop:y,hasTabStop:null!=x}):c})})});Z.displayName=S;var A={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function z(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let r of t)if(r===n||(r.focus({preventScroll:e}),document.activeElement!==n))return}var N=n(4357),C="Tabs",[T,$]=(0,o.b)(C,[b]),R=b(),[P,j]=T(C),D=r.forwardRef((t,e)=>{let{__scopeTabs:n,value:r,onValueChange:i,defaultValue:o,orientation:a="horizontal",dir:u,activationMode:c="automatic",...p}=t,v=(0,h.gm)(u),[y,m]=(0,f.T)({prop:r,onChange:i,defaultProp:null!=o?o:"",caller:C});return(0,d.jsx)(P,{scope:n,baseId:(0,s.M)(),value:y,onValueChange:m,orientation:a,dir:v,activationMode:c,children:(0,d.jsx)(l.WV.div,{dir:v,"data-orientation":a,...p,ref:e})})});D.displayName=C;var O="TabsList",I=r.forwardRef((t,e)=>{let{__scopeTabs:n,loop:r=!0,...i}=t,o=j(O,n),a=R(n);return(0,d.jsx)(E,{asChild:!0,...a,orientation:o.orientation,dir:o.dir,loop:r,children:(0,d.jsx)(l.WV.div,{role:"tablist","aria-orientation":o.orientation,...i,ref:e})})});I.displayName=O;var V="TabsTrigger",q=r.forwardRef((t,e)=>{let{__scopeTabs:n,value:r,disabled:o=!1,...a}=t,u=j(V,n),s=R(n),c=F(u.baseId,r),f=B(u.baseId,r),h=r===u.value;return(0,d.jsx)(Z,{asChild:!0,...s,focusable:!o,active:h,children:(0,d.jsx)(l.WV.button,{type:"button",role:"tab","aria-selected":h,"aria-controls":f,"data-state":h?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:c,...a,ref:e,onMouseDown:(0,i.M)(t.onMouseDown,t=>{o||0!==t.button||!1!==t.ctrlKey?t.preventDefault():u.onValueChange(r)}),onKeyDown:(0,i.M)(t.onKeyDown,t=>{[" ","Enter"].includes(t.key)&&u.onValueChange(r)}),onFocus:(0,i.M)(t.onFocus,()=>{let t="manual"!==u.activationMode;h||o||!t||u.onValueChange(r)})})})});q.displayName=V;var H="TabsContent",X=r.forwardRef((t,e)=>{let{__scopeTabs:n,value:i,forceMount:o,children:a,...u}=t,s=j(H,n),c=F(s.baseId,i),f=B(s.baseId,i),h=i===s.value,p=r.useRef(h);return r.useEffect(()=>{let t=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(t)},[]),(0,d.jsx)(N.z,{present:o||h,children:n=>{let{present:r}=n;return(0,d.jsx)(l.WV.div,{"data-state":h?"active":"inactive","data-orientation":s.orientation,role:"tabpanel","aria-labelledby":c,hidden:!r,id:f,tabIndex:0,...u,ref:e,style:{...t.style,animationDuration:p.current?"0s":void 0},children:r&&a})}})});function F(t,e){return"".concat(t,"-trigger-").concat(e)}function B(t,e){return"".concat(t,"-content-").concat(e)}X.displayName=H;var L=D,Y=I,U=q,W=X},246:function(t,e,n){"use strict";n.d(e,{A:function(){return v}});var r,i,o=n(2208),a=n(2658),u=n(4239),s=n(1619);function l({color:t,dimensions:e,lineWidth:n}){return o.createElement("path",{stroke:t,strokeWidth:n,d:`M${e[0]/2} 0 V${e[1]} M0 ${e[1]/2} H${e[0]}`})}function c({color:t,radius:e}){return o.createElement("circle",{cx:e,cy:e,r:e,fill:t})}(r=i||(i={})).Lines="lines",r.Dots="dots",r.Cross="cross";let f={[i.Dots]:"#91919a",[i.Lines]:"#eee",[i.Cross]:"#e2e2e2"},h={[i.Dots]:1,[i.Lines]:1,[i.Cross]:6},d=t=>({transform:t.transform,patternId:`pattern-${t.rfId}`});function p({id:t,variant:e=i.Dots,gap:n=20,size:r,lineWidth:p=1,offset:v=2,color:y,style:m,className:g}){let _=(0,o.useRef)(null),{transform:w,patternId:b}=(0,u.oR)(d,s.X),x=y||f[e],k=r||h[e],E=e===i.Dots,M=e===i.Cross,S=Array.isArray(n)?n:[n,n],Z=[S[0]*w[2]||1,S[1]*w[2]||1],A=k*w[2],z=M?[A,A]:Z,N=E?[A/v,A/v]:[z[0]/v,z[1]/v];return o.createElement("svg",{className:(0,a.Z)(["react-flow__background",g]),style:{...m,position:"absolute",width:"100%",height:"100%",top:0,left:0},ref:_,"data-testid":"rf__background"},o.createElement("pattern",{id:b+t,x:w[0]%Z[0],y:w[1]%Z[1],width:Z[0],height:Z[1],patternUnits:"userSpaceOnUse",patternTransform:`translate(-${N[0]},-${N[1]})`},E?o.createElement(c,{color:x,radius:A/v}):o.createElement(l,{dimensions:z,color:x,lineWidth:p})),o.createElement("rect",{x:"0",y:"0",width:"100%",height:"100%",fill:`url(#${b+t})`}))}p.displayName="Background";var v=(0,o.memo)(p)},4024:function(t,e,n){"use strict";n.d(e,{Z:function(){return v}});var r=n(2208),i=n(2658),o=n(1619),a=n(4239);function u(){return r.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32"},r.createElement("path",{d:"M32 18.133H18.133V32h-4.266V18.133H0v-4.266h13.867V0h4.266v13.867H32z"}))}function s(){return r.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 5"},r.createElement("path",{d:"M0 0h32v4.2H0z"}))}function l(){return r.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 30"},r.createElement("path",{d:"M3.692 4.63c0-.53.4-.938.939-.938h5.215V0H4.708C2.13 0 0 2.054 0 4.63v5.216h3.692V4.631zM27.354 0h-5.2v3.692h5.17c.53 0 .984.4.984.939v5.215H32V4.631A4.624 4.624 0 0027.354 0zm.954 24.83c0 .532-.4.94-.939.94h-5.215v3.768h5.215c2.577 0 4.631-2.13 4.631-4.707v-5.139h-3.692v5.139zm-23.677.94c-.531 0-.939-.4-.939-.94v-5.138H0v5.139c0 2.577 2.13 4.707 4.708 4.707h5.138V25.77H4.631z"}))}function c(){return r.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32"},r.createElement("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0 8 0 4.571 3.429 4.571 7.619v3.048H3.048A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047zm4.724-13.866H7.467V7.619c0-2.59 2.133-4.724 4.723-4.724 2.591 0 4.724 2.133 4.724 4.724v3.048z"}))}function f(){return r.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32"},r.createElement("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0c-4.114 1.828-1.37 2.133.305 2.438 1.676.305 4.42 2.59 4.42 5.181v3.048H3.047A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047z"}))}let h=({children:t,className:e,...n})=>r.createElement("button",{type:"button",className:(0,i.Z)(["react-flow__controls-button",e]),...n},t);h.displayName="ControlButton";let d=t=>({isInteractive:t.nodesDraggable||t.nodesConnectable||t.elementsSelectable,minZoomReached:t.transform[2]<=t.minZoom,maxZoomReached:t.transform[2]>=t.maxZoom}),p=({style:t,showZoom:e=!0,showFitView:n=!0,showInteractive:p=!0,fitViewOptions:v,onZoomIn:y,onZoomOut:m,onFitView:g,onInteractiveChange:_,className:w,children:b,position:x="bottom-left"})=>{let k=(0,a.AC)(),[E,M]=(0,r.useState)(!1),{isInteractive:S,minZoomReached:Z,maxZoomReached:A}=(0,a.oR)(d,o.X),{zoomIn:z,zoomOut:N,fitView:C}=(0,a._K)();return((0,r.useEffect)(()=>{M(!0)},[]),E)?r.createElement(a.s_,{className:(0,i.Z)(["react-flow__controls",w]),position:x,style:t,"data-testid":"rf__controls"},e&&r.createElement(r.Fragment,null,r.createElement(h,{onClick:()=>{z(),y?.()},className:"react-flow__controls-zoomin",title:"zoom in","aria-label":"zoom in",disabled:A},r.createElement(u,null)),r.createElement(h,{onClick:()=>{N(),m?.()},className:"react-flow__controls-zoomout",title:"zoom out","aria-label":"zoom out",disabled:Z},r.createElement(s,null))),n&&r.createElement(h,{className:"react-flow__controls-fitview",onClick:()=>{C(v),g?.()},title:"fit view","aria-label":"fit view"},r.createElement(l,null)),p&&r.createElement(h,{className:"react-flow__controls-interactive",onClick:()=>{k.setState({nodesDraggable:!S,nodesConnectable:!S,elementsSelectable:!S}),_?.(!S)},title:"toggle interactivity","aria-label":"toggle interactivity"},S?r.createElement(f,null):r.createElement(c,null)),b):null};p.displayName="Controls";var v=(0,r.memo)(p)},4769:function(t,e,n){"use strict";n.d(e,{a:function(){return g}});var r=n(2208),i=n(2658),o=n(1619),a=n(7853),u=n(1826),s=n(9199),l=n(4239);let c=({id:t,x:e,y:n,width:o,height:a,style:u,color:s,strokeColor:l,strokeWidth:c,className:f,borderRadius:h,shapeRendering:d,onClick:p,selected:v})=>{let{background:y,backgroundColor:m}=u||{};return r.createElement("rect",{className:(0,i.Z)(["react-flow__minimap-node",{selected:v},f]),x:e,y:n,rx:h,ry:h,width:o,height:a,fill:s||y||m,stroke:l,strokeWidth:c,shapeRendering:d,onClick:p?e=>p(e,t):void 0})};c.displayName="MiniMapNode";var f=(0,r.memo)(c);let h=t=>t.nodeOrigin,d=t=>t.getNodes().filter(t=>!t.hidden&&t.width&&t.height),p=t=>t instanceof Function?t:()=>t;var v=(0,r.memo)(function({nodeStrokeColor:t="transparent",nodeColor:e="#e2e2e2",nodeClassName:n="",nodeBorderRadius:i=5,nodeStrokeWidth:a=2,nodeComponent:u=f,onClick:s}){let c=(0,l.oR)(d,o.X),v=(0,l.oR)(h),y=p(e),m=p(t),g=p(n),_="undefined"==typeof window||window.chrome?"crispEdges":"geometricPrecision";return r.createElement(r.Fragment,null,c.map(t=>{let{x:e,y:n}=(0,l.VP)(t,v).positionAbsolute;return r.createElement(u,{key:t.id,x:e,y:n,width:t.width,height:t.height,style:t.style,selected:t.selected,className:g(t),color:y(t),borderRadius:i,strokeColor:m(t),strokeWidth:a,shapeRendering:_,onClick:s,id:t.id})}))});let y=t=>{let e=t.getNodes(),n={x:-t.transform[0]/t.transform[2],y:-t.transform[1]/t.transform[2],width:t.width/t.transform[2],height:t.height/t.transform[2]};return{viewBB:n,boundingRect:e.length>0?(0,l.oI)((0,l.RX)(e,t.nodeOrigin),n):n,rfId:t.rfId}};function m({style:t,className:e,nodeStrokeColor:n="transparent",nodeColor:c="#e2e2e2",nodeClassName:f="",nodeBorderRadius:h=5,nodeStrokeWidth:d=2,nodeComponent:p,maskColor:m="rgb(240, 240, 240, 0.6)",maskStrokeColor:g="none",maskStrokeWidth:_=1,position:w="bottom-right",onClick:b,onNodeClick:x,pannable:k=!1,zoomable:E=!1,ariaLabel:M="React Flow mini map",inversePan:S=!1,zoomStep:Z=10,offsetScale:A=5}){let z=(0,l.AC)(),N=(0,r.useRef)(null),{boundingRect:C,viewBB:T,rfId:$}=(0,l.oR)(y,o.X),R=t?.width??200,P=t?.height??150,j=Math.max(C.width/R,C.height/P),D=j*R,O=j*P,I=A*j,V=C.x-(D-C.width)/2-I,q=C.y-(O-C.height)/2-I,H=D+2*I,X=O+2*I,F=`react-flow__minimap-desc-${$}`,B=(0,r.useRef)(0);B.current=j,(0,r.useEffect)(()=>{if(N.current){let t=(0,u.Z)(N.current),e=(0,a.sP)().on("zoom",k?t=>{let{transform:e,d3Selection:n,d3Zoom:r,translateExtent:i,width:o,height:u}=z.getState();if("mousemove"!==t.sourceEvent.type||!n||!r)return;let s=B.current*Math.max(1,e[2])*(S?-1:1),l={x:e[0]-t.sourceEvent.movementX*s,y:e[1]-t.sourceEvent.movementY*s},c=a.CR.translate(l.x,l.y).scale(e[2]),f=r.constrain()(c,[[0,0],[o,u]],i);r.transform(n,f)}:null).on("zoom.wheel",E?t=>{let{transform:e,d3Selection:n,d3Zoom:r}=z.getState();if("wheel"!==t.sourceEvent.type||!n||!r)return;let i=-t.sourceEvent.deltaY*(1===t.sourceEvent.deltaMode?.05:t.sourceEvent.deltaMode?1:.002)*Z,o=e[2]*Math.pow(2,i);r.scaleTo(n,o)}:null);return t.call(e),()=>{t.on("zoom",null)}}},[k,E,S,Z]);let L=b?t=>{let e=(0,s.Z)(t);b(t,{x:e[0],y:e[1]})}:void 0;return r.createElement(l.s_,{position:w,style:t,className:(0,i.Z)(["react-flow__minimap",e]),"data-testid":"rf__minimap"},r.createElement("svg",{width:R,height:P,viewBox:`${V} ${q} ${H} ${X}`,role:"img","aria-labelledby":F,ref:N,onClick:L},M&&r.createElement("title",{id:F},M),r.createElement(v,{onClick:x?(t,e)=>{x(t,z.getState().nodeInternals.get(e))}:void 0,nodeColor:c,nodeStrokeColor:n,nodeBorderRadius:h,nodeClassName:f,nodeStrokeWidth:d,nodeComponent:p}),r.createElement("path",{className:"react-flow__minimap-mask",d:`M${V-I},${q-I}h${H+2*I}v${X+2*I}h${-H-2*I}z
        M${T.x},${T.y}h${T.width}v${T.height}h${-T.width}z`,fill:m,fillRule:"evenodd",stroke:g,strokeWidth:_,pointerEvents:"none"})))}m.displayName="MiniMap";var g=(0,r.memo)(m)},2658:function(t,e,n){"use strict";n.d(e,{Z:function(){return function t(e){if("string"==typeof e||"number"==typeof e)return""+e;let n="";if(Array.isArray(e))for(let r=0,i;r<e.length;r++)""!==(i=t(e[r]))&&(n+=(n&&" ")+i);else for(let t in e)e[t]&&(n+=(n&&" ")+t);return n}}})},4122:function(t,e){"use strict";var n={value:()=>{}};function r(){for(var t,e=0,n=arguments.length,r={};e<n;++e){if(!(t=arguments[e]+"")||t in r||/[\s.]/.test(t))throw Error("illegal type: "+t);r[t]=[]}return new i(r)}function i(t){this._=t}function o(t,e,r){for(var i=0,o=t.length;i<o;++i)if(t[i].name===e){t[i]=n,t=t.slice(0,i).concat(t.slice(i+1));break}return null!=r&&t.push({name:e,value:r}),t}i.prototype=r.prototype={constructor:i,on:function(t,e){var n,r=this._,i=(t+"").trim().split(/^|\s+/).map(function(t){var e="",n=t.indexOf(".");if(n>=0&&(e=t.slice(n+1),t=t.slice(0,n)),t&&!r.hasOwnProperty(t))throw Error("unknown type: "+t);return{type:t,name:e}}),a=-1,u=i.length;if(arguments.length<2){for(;++a<u;)if((n=(t=i[a]).type)&&(n=function(t,e){for(var n,r=0,i=t.length;r<i;++r)if((n=t[r]).name===e)return n.value}(r[n],t.name)))return n;return}if(null!=e&&"function"!=typeof e)throw Error("invalid callback: "+e);for(;++a<u;)if(n=(t=i[a]).type)r[n]=o(r[n],t.name,e);else if(null==e)for(n in r)r[n]=o(r[n],t.name,null);return this},copy:function(){var t={},e=this._;for(var n in e)t[n]=e[n].slice();return new i(t)},call:function(t,e){if((n=arguments.length-2)>0)for(var n,r,i=Array(n),o=0;o<n;++o)i[o]=arguments[o+2];if(!this._.hasOwnProperty(t))throw Error("unknown type: "+t);for(r=this._[t],o=0,n=r.length;o<n;++o)r[o].value.apply(e,i)},apply:function(t,e,n){if(!this._.hasOwnProperty(t))throw Error("unknown type: "+t);for(var r=this._[t],i=0,o=r.length;i<o;++i)r[i].value.apply(e,n)}},e.Z=r},1119:function(t,e,n){"use strict";n.d(e,{Z:function(){return p}});var r=n(4122),i=n(1826),o=n(9199),a=n(7009),u=n(3054),s=t=>()=>t;function l(t,{sourceEvent:e,subject:n,target:r,identifier:i,active:o,x:a,y:u,dx:s,dy:l,dispatch:c}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:e,enumerable:!0,configurable:!0},subject:{value:n,enumerable:!0,configurable:!0},target:{value:r,enumerable:!0,configurable:!0},identifier:{value:i,enumerable:!0,configurable:!0},active:{value:o,enumerable:!0,configurable:!0},x:{value:a,enumerable:!0,configurable:!0},y:{value:u,enumerable:!0,configurable:!0},dx:{value:s,enumerable:!0,configurable:!0},dy:{value:l,enumerable:!0,configurable:!0},_:{value:c}})}function c(t){return!t.ctrlKey&&!t.button}function f(){return this.parentNode}function h(t,e){return null==e?{x:t.x,y:t.y}:e}function d(){return navigator.maxTouchPoints||"ontouchstart"in this}function p(){var t,e,n,p,v=c,y=f,m=h,g=d,_={},w=(0,r.Z)("start","drag","end"),b=0,x=0;function k(t){t.on("mousedown.drag",E).filter(g).on("touchstart.drag",Z).on("touchmove.drag",A,u.Q7).on("touchend.drag touchcancel.drag",z).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function E(r,o){if(!p&&v.call(this,r,o)){var s=N(this,y.call(this,r,o),r,o,"mouse");s&&((0,i.Z)(r.view).on("mousemove.drag",M,u.Dd).on("mouseup.drag",S,u.Dd),(0,a.Z)(r.view),(0,u.rG)(r),n=!1,t=r.clientX,e=r.clientY,s("start",r))}}function M(r){if((0,u.ZP)(r),!n){var i=r.clientX-t,o=r.clientY-e;n=i*i+o*o>x}_.mouse("drag",r)}function S(t){(0,i.Z)(t.view).on("mousemove.drag mouseup.drag",null),(0,a.D)(t.view,n),(0,u.ZP)(t),_.mouse("end",t)}function Z(t,e){if(v.call(this,t,e)){var n,r,i=t.changedTouches,o=y.call(this,t,e),a=i.length;for(n=0;n<a;++n)(r=N(this,o,t,e,i[n].identifier,i[n]))&&((0,u.rG)(t),r("start",t,i[n]))}}function A(t){var e,n,r=t.changedTouches,i=r.length;for(e=0;e<i;++e)(n=_[r[e].identifier])&&((0,u.ZP)(t),n("drag",t,r[e]))}function z(t){var e,n,r=t.changedTouches,i=r.length;for(p&&clearTimeout(p),p=setTimeout(function(){p=null},500),e=0;e<i;++e)(n=_[r[e].identifier])&&((0,u.rG)(t),n("end",t,r[e]))}function N(t,e,n,r,i,a){var u,s,c,f=w.copy(),h=(0,o.Z)(a||n,e);if(null!=(c=m.call(t,new l("beforestart",{sourceEvent:n,target:k,identifier:i,active:b,x:h[0],y:h[1],dx:0,dy:0,dispatch:f}),r)))return u=c.x-h[0]||0,s=c.y-h[1]||0,function n(a,d,p){var v,y=h;switch(a){case"start":_[i]=n,v=b++;break;case"end":delete _[i],--b;case"drag":h=(0,o.Z)(p||d,e),v=b}f.call(a,t,new l(a,{sourceEvent:d,subject:c,target:k,identifier:i,active:v,x:h[0]+u,y:h[1]+s,dx:h[0]-y[0],dy:h[1]-y[1],dispatch:f}),r)}}return k.filter=function(t){return arguments.length?(v="function"==typeof t?t:s(!!t),k):v},k.container=function(t){return arguments.length?(y="function"==typeof t?t:s(t),k):y},k.subject=function(t){return arguments.length?(m="function"==typeof t?t:s(t),k):m},k.touchable=function(t){return arguments.length?(g="function"==typeof t?t:s(!!t),k):g},k.on=function(){var t=w.on.apply(w,arguments);return t===w?k:t},k.clickDistance=function(t){return arguments.length?(x=(t=+t)*t,k):Math.sqrt(x)},k}l.prototype.on=function(){var t=this._.on.apply(this._,arguments);return t===this._?this:t}},7009:function(t,e,n){"use strict";n.d(e,{D:function(){return a},Z:function(){return o}});var r=n(1826),i=n(3054);function o(t){var e=t.document.documentElement,n=(0,r.Z)(t).on("dragstart.drag",i.ZP,i.Dd);"onselectstart"in e?n.on("selectstart.drag",i.ZP,i.Dd):(e.__noselect=e.style.MozUserSelect,e.style.MozUserSelect="none")}function a(t,e){var n=t.document.documentElement,o=(0,r.Z)(t).on("dragstart.drag",null);e&&(o.on("click.drag",i.ZP,i.Dd),setTimeout(function(){o.on("click.drag",null)},0)),"onselectstart"in n?o.on("selectstart.drag",null):(n.style.MozUserSelect=n.__noselect,delete n.__noselect)}},3054:function(t,e,n){"use strict";n.d(e,{Dd:function(){return i},Q7:function(){return r},ZP:function(){return a},rG:function(){return o}});let r={passive:!1},i={capture:!0,passive:!1};function o(t){t.stopImmediatePropagation()}function a(t){t.preventDefault(),t.stopImmediatePropagation()}},7081:function(t,e,n){"use strict";function r(t){return function(){return this.matches(t)}}function i(t){return function(e){return e.matches(t)}}n.d(e,{P:function(){return i},Z:function(){return r}})},9345:function(t,e,n){"use strict";n.d(e,{Z:function(){return i}});var r=n(163);function i(t){var e=t+="",n=e.indexOf(":");return n>=0&&"xmlns"!==(e=t.slice(0,n))&&(t=t.slice(n+1)),r.Z.hasOwnProperty(e)?{space:r.Z[e],local:t}:t}},163:function(t,e,n){"use strict";n.d(e,{P:function(){return r}});var r="http://www.w3.org/1999/xhtml";e.Z={svg:"http://www.w3.org/2000/svg",xhtml:r,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"}},9199:function(t,e,n){"use strict";function r(t,e){if(t=function(t){let e;for(;e=t.sourceEvent;)t=e;return t}(t),void 0===e&&(e=t.currentTarget),e){var n=e.ownerSVGElement||e;if(n.createSVGPoint){var r=n.createSVGPoint();return r.x=t.clientX,r.y=t.clientY,[(r=r.matrixTransform(e.getScreenCTM().inverse())).x,r.y]}if(e.getBoundingClientRect){var i=e.getBoundingClientRect();return[t.clientX-i.left-e.clientLeft,t.clientY-i.top-e.clientTop]}}return[t.pageX,t.pageY]}n.d(e,{Z:function(){return r}})},1826:function(t,e,n){"use strict";n.d(e,{Z:function(){return i}});var r=n(6499);function i(t){return"string"==typeof t?new r.Y1([[document.querySelector(t)]],[document.documentElement]):new r.Y1([[t]],r.Jz)}},6499:function(t,e,n){"use strict";n.d(e,{Y1:function(){return O},ZP:function(){return V},Jz:function(){return D}});var r=n(7337),i=n(8088),o=n(7081),a=Array.prototype.find;function u(){return this.firstElementChild}var s=Array.prototype.filter;function l(){return Array.from(this.children)}function c(t){return Array(t.length)}function f(t,e){this.ownerDocument=t.ownerDocument,this.namespaceURI=t.namespaceURI,this._next=null,this._parent=t,this.__data__=e}function h(t,e,n,r,i,o){for(var a,u=0,s=e.length,l=o.length;u<l;++u)(a=e[u])?(a.__data__=o[u],r[u]=a):n[u]=new f(t,o[u]);for(;u<s;++u)(a=e[u])&&(i[u]=a)}function d(t,e,n,r,i,o,a){var u,s,l,c=new Map,h=e.length,d=o.length,p=Array(h);for(u=0;u<h;++u)(s=e[u])&&(p[u]=l=a.call(s,s.__data__,u,e)+"",c.has(l)?i[u]=s:c.set(l,s));for(u=0;u<d;++u)l=a.call(t,o[u],u,o)+"",(s=c.get(l))?(r[u]=s,s.__data__=o[u],c.delete(l)):n[u]=new f(t,o[u]);for(u=0;u<h;++u)(s=e[u])&&c.get(p[u])===s&&(i[u]=s)}function p(t){return t.__data__}function v(t,e){return t<e?-1:t>e?1:t>=e?0:NaN}f.prototype={constructor:f,appendChild:function(t){return this._parent.insertBefore(t,this._next)},insertBefore:function(t,e){return this._parent.insertBefore(t,e)},querySelector:function(t){return this._parent.querySelector(t)},querySelectorAll:function(t){return this._parent.querySelectorAll(t)}};var y=n(9345),m=n(9761);function g(t){return t.trim().split(/^|\s+/)}function _(t){return t.classList||new w(t)}function w(t){this._node=t,this._names=g(t.getAttribute("class")||"")}function b(t,e){for(var n=_(t),r=-1,i=e.length;++r<i;)n.add(e[r])}function x(t,e){for(var n=_(t),r=-1,i=e.length;++r<i;)n.remove(e[r])}function k(){this.textContent=""}function E(){this.innerHTML=""}function M(){this.nextSibling&&this.parentNode.appendChild(this)}function S(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}w.prototype={add:function(t){0>this._names.indexOf(t)&&(this._names.push(t),this._node.setAttribute("class",this._names.join(" ")))},remove:function(t){var e=this._names.indexOf(t);e>=0&&(this._names.splice(e,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(t){return this._names.indexOf(t)>=0}};var Z=n(163);function A(t){var e=(0,y.Z)(t);return(e.local?function(t){return function(){return this.ownerDocument.createElementNS(t.space,t.local)}}:function(t){return function(){var e=this.ownerDocument,n=this.namespaceURI;return n===Z.P&&e.documentElement.namespaceURI===Z.P?e.createElement(t):e.createElementNS(n,t)}})(e)}function z(){return null}function N(){var t=this.parentNode;t&&t.removeChild(this)}function C(){var t=this.cloneNode(!1),e=this.parentNode;return e?e.insertBefore(t,this.nextSibling):t}function T(){var t=this.cloneNode(!0),e=this.parentNode;return e?e.insertBefore(t,this.nextSibling):t}function $(t){return function(){var e=this.__on;if(e){for(var n,r=0,i=-1,o=e.length;r<o;++r)(n=e[r],t.type&&n.type!==t.type||n.name!==t.name)?e[++i]=n:this.removeEventListener(n.type,n.listener,n.options);++i?e.length=i:delete this.__on}}}function R(t,e,n){return function(){var r,i=this.__on,o=function(t){e.call(this,t,this.__data__)};if(i){for(var a=0,u=i.length;a<u;++a)if((r=i[a]).type===t.type&&r.name===t.name){this.removeEventListener(r.type,r.listener,r.options),this.addEventListener(r.type,r.listener=o,r.options=n),r.value=e;return}}this.addEventListener(t.type,o,n),r={type:t.type,name:t.name,value:e,listener:o,options:n},i?i.push(r):this.__on=[r]}}var P=n(4092);function j(t,e,n){var r=(0,P.Z)(t),i=r.CustomEvent;"function"==typeof i?i=new i(e,n):(i=r.document.createEvent("Event"),n?(i.initEvent(e,n.bubbles,n.cancelable),i.detail=n.detail):i.initEvent(e,!1,!1)),t.dispatchEvent(i)}var D=[null];function O(t,e){this._groups=t,this._parents=e}function I(){return new O([[document.documentElement]],D)}O.prototype=I.prototype={constructor:O,select:function(t){"function"!=typeof t&&(t=(0,r.Z)(t));for(var e=this._groups,n=e.length,i=Array(n),o=0;o<n;++o)for(var a,u,s=e[o],l=s.length,c=i[o]=Array(l),f=0;f<l;++f)(a=s[f])&&(u=t.call(a,a.__data__,f,s))&&("__data__"in a&&(u.__data__=a.__data__),c[f]=u);return new O(i,this._parents)},selectAll:function(t){if("function"==typeof t){var e;e=t,t=function(){var t;return t=e.apply(this,arguments),null==t?[]:Array.isArray(t)?t:Array.from(t)}}else t=(0,i.Z)(t);for(var n=this._groups,r=n.length,o=[],a=[],u=0;u<r;++u)for(var s,l=n[u],c=l.length,f=0;f<c;++f)(s=l[f])&&(o.push(t.call(s,s.__data__,f,l)),a.push(s));return new O(o,a)},selectChild:function(t){var e;return this.select(null==t?u:(e="function"==typeof t?t:(0,o.P)(t),function(){return a.call(this.children,e)}))},selectChildren:function(t){var e;return this.selectAll(null==t?l:(e="function"==typeof t?t:(0,o.P)(t),function(){return s.call(this.children,e)}))},filter:function(t){"function"!=typeof t&&(t=(0,o.Z)(t));for(var e=this._groups,n=e.length,r=Array(n),i=0;i<n;++i)for(var a,u=e[i],s=u.length,l=r[i]=[],c=0;c<s;++c)(a=u[c])&&t.call(a,a.__data__,c,u)&&l.push(a);return new O(r,this._parents)},data:function(t,e){if(!arguments.length)return Array.from(this,p);var n=e?d:h,r=this._parents,i=this._groups;"function"!=typeof t&&(b=t,t=function(){return b});for(var o=i.length,a=Array(o),u=Array(o),s=Array(o),l=0;l<o;++l){var c=r[l],f=i[l],v=f.length,y="object"==typeof(w=t.call(c,c&&c.__data__,l,r))&&"length"in w?w:Array.from(w),m=y.length,g=u[l]=Array(m),_=a[l]=Array(m);n(c,f,g,_,s[l]=Array(v),y,e);for(var w,b,x,k,E=0,M=0;E<m;++E)if(x=g[E]){for(E>=M&&(M=E+1);!(k=_[M])&&++M<m;);x._next=k||null}}return(a=new O(a,r))._enter=u,a._exit=s,a},enter:function(){return new O(this._enter||this._groups.map(c),this._parents)},exit:function(){return new O(this._exit||this._groups.map(c),this._parents)},join:function(t,e,n){var r=this.enter(),i=this,o=this.exit();return"function"==typeof t?(r=t(r))&&(r=r.selection()):r=r.append(t+""),null!=e&&(i=e(i))&&(i=i.selection()),null==n?o.remove():n(o),r&&i?r.merge(i).order():i},merge:function(t){for(var e=t.selection?t.selection():t,n=this._groups,r=e._groups,i=n.length,o=r.length,a=Math.min(i,o),u=Array(i),s=0;s<a;++s)for(var l,c=n[s],f=r[s],h=c.length,d=u[s]=Array(h),p=0;p<h;++p)(l=c[p]||f[p])&&(d[p]=l);for(;s<i;++s)u[s]=n[s];return new O(u,this._parents)},selection:function(){return this},order:function(){for(var t=this._groups,e=-1,n=t.length;++e<n;)for(var r,i=t[e],o=i.length-1,a=i[o];--o>=0;)(r=i[o])&&(a&&4^r.compareDocumentPosition(a)&&a.parentNode.insertBefore(r,a),a=r);return this},sort:function(t){function e(e,n){return e&&n?t(e.__data__,n.__data__):!e-!n}t||(t=v);for(var n=this._groups,r=n.length,i=Array(r),o=0;o<r;++o){for(var a,u=n[o],s=u.length,l=i[o]=Array(s),c=0;c<s;++c)(a=u[c])&&(l[c]=a);l.sort(e)}return new O(i,this._parents).order()},call:function(){var t=arguments[0];return arguments[0]=this,t.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var t=this._groups,e=0,n=t.length;e<n;++e)for(var r=t[e],i=0,o=r.length;i<o;++i){var a=r[i];if(a)return a}return null},size:function(){let t=0;for(let e of this)++t;return t},empty:function(){return!this.node()},each:function(t){for(var e=this._groups,n=0,r=e.length;n<r;++n)for(var i,o=e[n],a=0,u=o.length;a<u;++a)(i=o[a])&&t.call(i,i.__data__,a,o);return this},attr:function(t,e){var n=(0,y.Z)(t);if(arguments.length<2){var r=this.node();return n.local?r.getAttributeNS(n.space,n.local):r.getAttribute(n)}return this.each((null==e?n.local?function(t){return function(){this.removeAttributeNS(t.space,t.local)}}:function(t){return function(){this.removeAttribute(t)}}:"function"==typeof e?n.local?function(t,e){return function(){var n=e.apply(this,arguments);null==n?this.removeAttributeNS(t.space,t.local):this.setAttributeNS(t.space,t.local,n)}}:function(t,e){return function(){var n=e.apply(this,arguments);null==n?this.removeAttribute(t):this.setAttribute(t,n)}}:n.local?function(t,e){return function(){this.setAttributeNS(t.space,t.local,e)}}:function(t,e){return function(){this.setAttribute(t,e)}})(n,e))},style:m.Z,property:function(t,e){return arguments.length>1?this.each((null==e?function(t){return function(){delete this[t]}}:"function"==typeof e?function(t,e){return function(){var n=e.apply(this,arguments);null==n?delete this[t]:this[t]=n}}:function(t,e){return function(){this[t]=e}})(t,e)):this.node()[t]},classed:function(t,e){var n=g(t+"");if(arguments.length<2){for(var r=_(this.node()),i=-1,o=n.length;++i<o;)if(!r.contains(n[i]))return!1;return!0}return this.each(("function"==typeof e?function(t,e){return function(){(e.apply(this,arguments)?b:x)(this,t)}}:e?function(t){return function(){b(this,t)}}:function(t){return function(){x(this,t)}})(n,e))},text:function(t){return arguments.length?this.each(null==t?k:("function"==typeof t?function(t){return function(){var e=t.apply(this,arguments);this.textContent=null==e?"":e}}:function(t){return function(){this.textContent=t}})(t)):this.node().textContent},html:function(t){return arguments.length?this.each(null==t?E:("function"==typeof t?function(t){return function(){var e=t.apply(this,arguments);this.innerHTML=null==e?"":e}}:function(t){return function(){this.innerHTML=t}})(t)):this.node().innerHTML},raise:function(){return this.each(M)},lower:function(){return this.each(S)},append:function(t){var e="function"==typeof t?t:A(t);return this.select(function(){return this.appendChild(e.apply(this,arguments))})},insert:function(t,e){var n="function"==typeof t?t:A(t),i=null==e?z:"function"==typeof e?e:(0,r.Z)(e);return this.select(function(){return this.insertBefore(n.apply(this,arguments),i.apply(this,arguments)||null)})},remove:function(){return this.each(N)},clone:function(t){return this.select(t?T:C)},datum:function(t){return arguments.length?this.property("__data__",t):this.node().__data__},on:function(t,e,n){var r,i,o=(t+"").trim().split(/^|\s+/).map(function(t){var e="",n=t.indexOf(".");return n>=0&&(e=t.slice(n+1),t=t.slice(0,n)),{type:t,name:e}}),a=o.length;if(arguments.length<2){var u=this.node().__on;if(u){for(var s,l=0,c=u.length;l<c;++l)for(r=0,s=u[l];r<a;++r)if((i=o[r]).type===s.type&&i.name===s.name)return s.value}return}for(r=0,u=e?R:$;r<a;++r)this.each(u(o[r],e,n));return this},dispatch:function(t,e){return this.each(("function"==typeof e?function(t,e){return function(){return j(this,t,e.apply(this,arguments))}}:function(t,e){return function(){return j(this,t,e)}})(t,e))},[Symbol.iterator]:function*(){for(var t=this._groups,e=0,n=t.length;e<n;++e)for(var r,i=t[e],o=0,a=i.length;o<a;++o)(r=i[o])&&(yield r)}};var V=I},9761:function(t,e,n){"use strict";n.d(e,{S:function(){return o},Z:function(){return i}});var r=n(4092);function i(t,e,n){return arguments.length>1?this.each((null==e?function(t){return function(){this.style.removeProperty(t)}}:"function"==typeof e?function(t,e,n){return function(){var r=e.apply(this,arguments);null==r?this.style.removeProperty(t):this.style.setProperty(t,r,n)}}:function(t,e,n){return function(){this.style.setProperty(t,e,n)}})(t,e,null==n?"":n)):o(this.node(),t)}function o(t,e){return t.style.getPropertyValue(e)||(0,r.Z)(t).getComputedStyle(t,null).getPropertyValue(e)}},7337:function(t,e,n){"use strict";function r(){}function i(t){return null==t?r:function(){return this.querySelector(t)}}n.d(e,{Z:function(){return i}})},8088:function(t,e,n){"use strict";function r(){return[]}function i(t){return null==t?r:function(){return this.querySelectorAll(t)}}n.d(e,{Z:function(){return i}})},4092:function(t,e,n){"use strict";function r(t){return t.ownerDocument&&t.ownerDocument.defaultView||t.document&&t||t.defaultView}n.d(e,{Z:function(){return r}})},7853:function(t,e,n){"use strict";n.d(e,{sP:function(){return tQ},CR:function(){return tF}});var r,i=n(4122),o=n(7009);function a(t){return((t=Math.exp(t))+1/t)/2}var u,s,l=function t(e,n,r){function i(t,i){var o,u,s=t[0],l=t[1],c=t[2],f=i[0],h=i[1],d=i[2],p=f-s,v=h-l,y=p*p+v*v;if(y<1e-12)u=Math.log(d/c)/e,o=function(t){return[s+t*p,l+t*v,c*Math.exp(e*t*u)]};else{var m=Math.sqrt(y),g=(d*d-c*c+r*y)/(2*c*n*m),_=(d*d-c*c-r*y)/(2*d*n*m),w=Math.log(Math.sqrt(g*g+1)-g);u=(Math.log(Math.sqrt(_*_+1)-_)-w)/e,o=function(t){var r,i,o=t*u,f=a(w),h=c/(n*m)*(((r=Math.exp(2*(r=e*o+w)))-1)/(r+1)*f-((i=Math.exp(i=w))-1/i)/2);return[s+h*p,l+h*v,c*f/a(e*o+w)]}}return o.duration=1e3*u*e/Math.SQRT2,o}return i.rho=function(e){var n=Math.max(.001,+e),r=n*n;return t(n,r,r*r)},i}(Math.SQRT2,2,4),c=n(1826),f=n(9199),h=n(6499),d=0,p=0,v=0,y=0,m=0,g=0,_="object"==typeof performance&&performance.now?performance:Date,w="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(t){setTimeout(t,17)};function b(){return m||(w(x),m=_.now()+g)}function x(){m=0}function k(){this._call=this._time=this._next=null}function E(t,e,n){var r=new k;return r.restart(t,e,n),r}function M(){m=(y=_.now())+g,d=p=0;try{!function(){b(),++d;for(var t,e=u;e;)(t=m-e._time)>=0&&e._call.call(void 0,t),e=e._next;--d}()}finally{d=0,function(){for(var t,e,n=u,r=1/0;n;)n._call?(r>n._time&&(r=n._time),t=n,n=n._next):(e=n._next,n._next=null,n=t?t._next=e:u=e);s=t,Z(r)}(),m=0}}function S(){var t=_.now(),e=t-y;e>1e3&&(g-=e,y=t)}function Z(t){!d&&(p&&(p=clearTimeout(p)),t-m>24?(t<1/0&&(p=setTimeout(M,t-_.now()-g)),v&&(v=clearInterval(v))):(v||(y=_.now(),v=setInterval(S,1e3)),d=1,w(M)))}function A(t,e,n){var r=new k;return e=null==e?0:+e,r.restart(n=>{r.stop(),t(n+e)},e,n),r}k.prototype=E.prototype={constructor:k,restart:function(t,e,n){if("function"!=typeof t)throw TypeError("callback is not a function");n=(null==n?b():+n)+(null==e?0:+e),this._next||s===this||(s?s._next=this:u=this,s=this),this._call=t,this._time=n,Z()},stop:function(){this._call&&(this._call=null,this._time=1/0,Z())}};var z=(0,i.Z)("start","end","cancel","interrupt"),N=[];function C(t,e,n,r,i,o){var a=t.__transition;if(a){if(n in a)return}else t.__transition={};!function(t,e,n){var r,i=t.__transition;function o(s){var l,c,f,h;if(1!==n.state)return u();for(l in i)if((h=i[l]).name===n.name){if(3===h.state)return A(o);4===h.state?(h.state=6,h.timer.stop(),h.on.call("interrupt",t,t.__data__,h.index,h.group),delete i[l]):+l<e&&(h.state=6,h.timer.stop(),h.on.call("cancel",t,t.__data__,h.index,h.group),delete i[l])}if(A(function(){3===n.state&&(n.state=4,n.timer.restart(a,n.delay,n.time),a(s))}),n.state=2,n.on.call("start",t,t.__data__,n.index,n.group),2===n.state){for(l=0,n.state=3,r=Array(f=n.tween.length),c=-1;l<f;++l)(h=n.tween[l].value.call(t,t.__data__,n.index,n.group))&&(r[++c]=h);r.length=c+1}}function a(e){for(var i=e<n.duration?n.ease.call(null,e/n.duration):(n.timer.restart(u),n.state=5,1),o=-1,a=r.length;++o<a;)r[o].call(t,i);5===n.state&&(n.on.call("end",t,t.__data__,n.index,n.group),u())}function u(){for(var r in n.state=6,n.timer.stop(),delete i[e],i)return;delete t.__transition}i[e]=n,n.timer=E(function(t){n.state=1,n.timer.restart(o,n.delay,n.time),n.delay<=t&&o(t-n.delay)},0,n.time)}(t,n,{name:e,index:r,group:i,on:z,tween:N,time:o.time,delay:o.delay,duration:o.duration,ease:o.ease,timer:null,state:0})}function T(t,e){var n=R(t,e);if(n.state>0)throw Error("too late; already scheduled");return n}function $(t,e){var n=R(t,e);if(n.state>3)throw Error("too late; already running");return n}function R(t,e){var n=t.__transition;if(!n||!(n=n[e]))throw Error("transition not found");return n}function P(t,e){var n,r,i,o=t.__transition,a=!0;if(o){for(i in e=null==e?null:e+"",o){if((n=o[i]).name!==e){a=!1;continue}r=n.state>2&&n.state<5,n.state=6,n.timer.stop(),n.on.call(r?"interrupt":"cancel",t,t.__data__,n.index,n.group),delete o[i]}a&&delete t.__transition}}function j(t,e){return t=+t,e=+e,function(n){return t*(1-n)+e*n}}var D=180/Math.PI,O={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function I(t,e,n,r,i,o){var a,u,s;return(a=Math.sqrt(t*t+e*e))&&(t/=a,e/=a),(s=t*n+e*r)&&(n-=t*s,r-=e*s),(u=Math.sqrt(n*n+r*r))&&(n/=u,r/=u,s/=u),t*r<e*n&&(t=-t,e=-e,s=-s,a=-a),{translateX:i,translateY:o,rotate:Math.atan2(e,t)*D,skewX:Math.atan(s)*D,scaleX:a,scaleY:u}}function V(t,e,n,r){function i(t){return t.length?t.pop()+" ":""}return function(o,a){var u,s,l,c,f=[],h=[];return o=t(o),a=t(a),!function(t,r,i,o,a,u){if(t!==i||r!==o){var s=a.push("translate(",null,e,null,n);u.push({i:s-4,x:j(t,i)},{i:s-2,x:j(r,o)})}else(i||o)&&a.push("translate("+i+e+o+n)}(o.translateX,o.translateY,a.translateX,a.translateY,f,h),(u=o.rotate)!==(s=a.rotate)?(u-s>180?s+=360:s-u>180&&(u+=360),h.push({i:f.push(i(f)+"rotate(",null,r)-2,x:j(u,s)})):s&&f.push(i(f)+"rotate("+s+r),(l=o.skewX)!==(c=a.skewX)?h.push({i:f.push(i(f)+"skewX(",null,r)-2,x:j(l,c)}):c&&f.push(i(f)+"skewX("+c+r),!function(t,e,n,r,o,a){if(t!==n||e!==r){var u=o.push(i(o)+"scale(",null,",",null,")");a.push({i:u-4,x:j(t,n)},{i:u-2,x:j(e,r)})}else(1!==n||1!==r)&&o.push(i(o)+"scale("+n+","+r+")")}(o.scaleX,o.scaleY,a.scaleX,a.scaleY,f,h),o=a=null,function(t){for(var e,n=-1,r=h.length;++n<r;)f[(e=h[n]).i]=e.x(t);return f.join("")}}}var q=V(function(t){let e=new("function"==typeof DOMMatrix?DOMMatrix:WebKitCSSMatrix)(t+"");return e.isIdentity?O:I(e.a,e.b,e.c,e.d,e.e,e.f)},"px, ","px)","deg)"),H=V(function(t){return null==t?O:(r||(r=document.createElementNS("http://www.w3.org/2000/svg","g")),r.setAttribute("transform",t),t=r.transform.baseVal.consolidate())?I((t=t.matrix).a,t.b,t.c,t.d,t.e,t.f):O},", ",")",")"),X=n(9345);function F(t,e,n){var r=t._id;return t.each(function(){var t=$(this,r);(t.value||(t.value={}))[e]=n.apply(this,arguments)}),function(t){return R(t,r).value[e]}}function B(t,e,n){t.prototype=e.prototype=n,n.constructor=t}function L(t,e){var n=Object.create(t.prototype);for(var r in e)n[r]=e[r];return n}function Y(){}var U="\\s*([+-]?\\d+)\\s*",W="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",K="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",G=/^#([0-9a-f]{3,8})$/,J=RegExp(`^rgb\\(${U},${U},${U}\\)$`),Q=RegExp(`^rgb\\(${K},${K},${K}\\)$`),tt=RegExp(`^rgba\\(${U},${U},${U},${W}\\)$`),te=RegExp(`^rgba\\(${K},${K},${K},${W}\\)$`),tn=RegExp(`^hsl\\(${W},${K},${K}\\)$`),tr=RegExp(`^hsla\\(${W},${K},${K},${W}\\)$`),ti={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function to(){return this.rgb().formatHex()}function ta(){return this.rgb().formatRgb()}function tu(t){var e,n;return t=(t+"").trim().toLowerCase(),(e=G.exec(t))?(n=e[1].length,e=parseInt(e[1],16),6===n?ts(e):3===n?new tf(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===n?tl(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===n?tl(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=J.exec(t))?new tf(e[1],e[2],e[3],1):(e=Q.exec(t))?new tf(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=tt.exec(t))?tl(e[1],e[2],e[3],e[4]):(e=te.exec(t))?tl(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=tn.exec(t))?tm(e[1],e[2]/100,e[3]/100,1):(e=tr.exec(t))?tm(e[1],e[2]/100,e[3]/100,e[4]):ti.hasOwnProperty(t)?ts(ti[t]):"transparent"===t?new tf(NaN,NaN,NaN,0):null}function ts(t){return new tf(t>>16&255,t>>8&255,255&t,1)}function tl(t,e,n,r){return r<=0&&(t=e=n=NaN),new tf(t,e,n,r)}function tc(t,e,n,r){var i;return 1==arguments.length?((i=t)instanceof Y||(i=tu(i)),i)?new tf((i=i.rgb()).r,i.g,i.b,i.opacity):new tf:new tf(t,e,n,null==r?1:r)}function tf(t,e,n,r){this.r=+t,this.g=+e,this.b=+n,this.opacity=+r}function th(){return`#${ty(this.r)}${ty(this.g)}${ty(this.b)}`}function td(){let t=tp(this.opacity);return`${1===t?"rgb(":"rgba("}${tv(this.r)}, ${tv(this.g)}, ${tv(this.b)}${1===t?")":`, ${t})`}`}function tp(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function tv(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function ty(t){return((t=tv(t))<16?"0":"")+t.toString(16)}function tm(t,e,n,r){return r<=0?t=e=n=NaN:n<=0||n>=1?t=e=NaN:e<=0&&(t=NaN),new t_(t,e,n,r)}function tg(t){if(t instanceof t_)return new t_(t.h,t.s,t.l,t.opacity);if(t instanceof Y||(t=tu(t)),!t)return new t_;if(t instanceof t_)return t;var e=(t=t.rgb()).r/255,n=t.g/255,r=t.b/255,i=Math.min(e,n,r),o=Math.max(e,n,r),a=NaN,u=o-i,s=(o+i)/2;return u?(a=e===o?(n-r)/u+(n<r)*6:n===o?(r-e)/u+2:(e-n)/u+4,u/=s<.5?o+i:2-o-i,a*=60):u=s>0&&s<1?0:a,new t_(a,u,s,t.opacity)}function t_(t,e,n,r){this.h=+t,this.s=+e,this.l=+n,this.opacity=+r}function tw(t){return(t=(t||0)%360)<0?t+360:t}function tb(t){return Math.max(0,Math.min(1,t||0))}function tx(t,e,n){return(t<60?e+(n-e)*t/60:t<180?n:t<240?e+(n-e)*(240-t)/60:e)*255}function tk(t,e,n,r,i){var o=t*t,a=o*t;return((1-3*t+3*o-a)*e+(4-6*o+3*a)*n+(1+3*t+3*o-3*a)*r+a*i)/6}B(Y,tu,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:to,formatHex:to,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return tg(this).formatHsl()},formatRgb:ta,toString:ta}),B(tf,tc,L(Y,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new tf(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new tf(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new tf(tv(this.r),tv(this.g),tv(this.b),tp(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:th,formatHex:th,formatHex8:function(){return`#${ty(this.r)}${ty(this.g)}${ty(this.b)}${ty((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:td,toString:td})),B(t_,function(t,e,n,r){return 1==arguments.length?tg(t):new t_(t,e,n,null==r?1:r)},L(Y,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new t_(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new t_(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,e=isNaN(t)||isNaN(this.s)?0:this.s,n=this.l,r=n+(n<.5?n:1-n)*e,i=2*n-r;return new tf(tx(t>=240?t-240:t+120,i,r),tx(t,i,r),tx(t<120?t+240:t-120,i,r),this.opacity)},clamp(){return new t_(tw(this.h),tb(this.s),tb(this.l),tp(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let t=tp(this.opacity);return`${1===t?"hsl(":"hsla("}${tw(this.h)}, ${100*tb(this.s)}%, ${100*tb(this.l)}%${1===t?")":`, ${t})`}`}}));var tE=t=>()=>t;function tM(t,e){var n=e-t;return n?function(e){return t+e*n}:tE(isNaN(t)?e:t)}var tS=function t(e){var n,r=1==(n=+(n=e))?tM:function(t,e){var r,i,o;return e-t?(r=t,i=e,r=Math.pow(r,o=n),i=Math.pow(i,o)-r,o=1/o,function(t){return Math.pow(r+t*i,o)}):tE(isNaN(t)?e:t)};function i(t,e){var n=r((t=tc(t)).r,(e=tc(e)).r),i=r(t.g,e.g),o=r(t.b,e.b),a=tM(t.opacity,e.opacity);return function(e){return t.r=n(e),t.g=i(e),t.b=o(e),t.opacity=a(e),t+""}}return i.gamma=t,i}(1);function tZ(t){return function(e){var n,r,i=e.length,o=Array(i),a=Array(i),u=Array(i);for(n=0;n<i;++n)r=tc(e[n]),o[n]=r.r||0,a[n]=r.g||0,u[n]=r.b||0;return o=t(o),a=t(a),u=t(u),r.opacity=1,function(t){return r.r=o(t),r.g=a(t),r.b=u(t),r+""}}}tZ(function(t){var e=t.length-1;return function(n){var r=n<=0?n=0:n>=1?(n=1,e-1):Math.floor(n*e),i=t[r],o=t[r+1],a=r>0?t[r-1]:2*i-o,u=r<e-1?t[r+2]:2*o-i;return tk((n-r/e)*e,a,i,o,u)}}),tZ(function(t){var e=t.length;return function(n){var r=Math.floor(((n%=1)<0?++n:n)*e),i=t[(r+e-1)%e],o=t[r%e],a=t[(r+1)%e],u=t[(r+2)%e];return tk((n-r/e)*e,i,o,a,u)}});var tA=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,tz=RegExp(tA.source,"g");function tN(t,e){var n;return("number"==typeof e?j:e instanceof tu?tS:(n=tu(e))?(e=n,tS):function(t,e){var n,r,i,o,a,u=tA.lastIndex=tz.lastIndex=0,s=-1,l=[],c=[];for(t+="",e+="";(i=tA.exec(t))&&(o=tz.exec(e));)(a=o.index)>u&&(a=e.slice(u,a),l[s]?l[s]+=a:l[++s]=a),(i=i[0])===(o=o[0])?l[s]?l[s]+=o:l[++s]=o:(l[++s]=null,c.push({i:s,x:j(i,o)})),u=tz.lastIndex;return u<e.length&&(a=e.slice(u),l[s]?l[s]+=a:l[++s]=a),l.length<2?c[0]?(n=c[0].x,function(t){return n(t)+""}):(r=e,function(){return r}):(e=c.length,function(t){for(var n,r=0;r<e;++r)l[(n=c[r]).i]=n.x(t);return l.join("")})})(t,e)}var tC=n(7081),tT=n(7337),t$=n(8088),tR=h.ZP.prototype.constructor,tP=n(9761);function tj(t){return function(){this.style.removeProperty(t)}}var tD=0;function tO(t,e,n,r){this._groups=t,this._parents=e,this._name=n,this._id=r}var tI=h.ZP.prototype;tO.prototype=(function(t){return(0,h.ZP)().transition(t)}).prototype={constructor:tO,select:function(t){var e=this._name,n=this._id;"function"!=typeof t&&(t=(0,tT.Z)(t));for(var r=this._groups,i=r.length,o=Array(i),a=0;a<i;++a)for(var u,s,l=r[a],c=l.length,f=o[a]=Array(c),h=0;h<c;++h)(u=l[h])&&(s=t.call(u,u.__data__,h,l))&&("__data__"in u&&(s.__data__=u.__data__),f[h]=s,C(f[h],e,n,h,f,R(u,n)));return new tO(o,this._parents,e,n)},selectAll:function(t){var e=this._name,n=this._id;"function"!=typeof t&&(t=(0,t$.Z)(t));for(var r=this._groups,i=r.length,o=[],a=[],u=0;u<i;++u)for(var s,l=r[u],c=l.length,f=0;f<c;++f)if(s=l[f]){for(var h,d=t.call(s,s.__data__,f,l),p=R(s,n),v=0,y=d.length;v<y;++v)(h=d[v])&&C(h,e,n,v,d,p);o.push(d),a.push(s)}return new tO(o,a,e,n)},selectChild:tI.selectChild,selectChildren:tI.selectChildren,filter:function(t){"function"!=typeof t&&(t=(0,tC.Z)(t));for(var e=this._groups,n=e.length,r=Array(n),i=0;i<n;++i)for(var o,a=e[i],u=a.length,s=r[i]=[],l=0;l<u;++l)(o=a[l])&&t.call(o,o.__data__,l,a)&&s.push(o);return new tO(r,this._parents,this._name,this._id)},merge:function(t){if(t._id!==this._id)throw Error();for(var e=this._groups,n=t._groups,r=e.length,i=n.length,o=Math.min(r,i),a=Array(r),u=0;u<o;++u)for(var s,l=e[u],c=n[u],f=l.length,h=a[u]=Array(f),d=0;d<f;++d)(s=l[d]||c[d])&&(h[d]=s);for(;u<r;++u)a[u]=e[u];return new tO(a,this._parents,this._name,this._id)},selection:function(){return new tR(this._groups,this._parents)},transition:function(){for(var t=this._name,e=this._id,n=++tD,r=this._groups,i=r.length,o=0;o<i;++o)for(var a,u=r[o],s=u.length,l=0;l<s;++l)if(a=u[l]){var c=R(a,e);C(a,t,n,l,u,{time:c.time+c.delay+c.duration,delay:0,duration:c.duration,ease:c.ease})}return new tO(r,this._parents,t,n)},call:tI.call,nodes:tI.nodes,node:tI.node,size:tI.size,empty:tI.empty,each:tI.each,on:function(t,e){var n,r,i,o=this._id;return arguments.length<2?R(this.node(),o).on.on(t):this.each((i=(t+"").trim().split(/^|\s+/).every(function(t){var e=t.indexOf(".");return e>=0&&(t=t.slice(0,e)),!t||"start"===t})?T:$,function(){var a=i(this,o),u=a.on;u!==n&&(r=(n=u).copy()).on(t,e),a.on=r}))},attr:function(t,e){var n=(0,X.Z)(t),r="transform"===n?H:tN;return this.attrTween(t,"function"==typeof e?(n.local?function(t,e,n){var r,i,o;return function(){var a,u,s=n(this);return null==s?void this.removeAttributeNS(t.space,t.local):(a=this.getAttributeNS(t.space,t.local))===(u=s+"")?null:a===r&&u===i?o:(i=u,o=e(r=a,s))}}:function(t,e,n){var r,i,o;return function(){var a,u,s=n(this);return null==s?void this.removeAttribute(t):(a=this.getAttribute(t))===(u=s+"")?null:a===r&&u===i?o:(i=u,o=e(r=a,s))}})(n,r,F(this,"attr."+t,e)):null==e?(n.local?function(t){return function(){this.removeAttributeNS(t.space,t.local)}}:function(t){return function(){this.removeAttribute(t)}})(n):(n.local?function(t,e,n){var r,i,o=n+"";return function(){var a=this.getAttributeNS(t.space,t.local);return a===o?null:a===r?i:i=e(r=a,n)}}:function(t,e,n){var r,i,o=n+"";return function(){var a=this.getAttribute(t);return a===o?null:a===r?i:i=e(r=a,n)}})(n,r,e))},attrTween:function(t,e){var n="attr."+t;if(arguments.length<2)return(n=this.tween(n))&&n._value;if(null==e)return this.tween(n,null);if("function"!=typeof e)throw Error();var r=(0,X.Z)(t);return this.tween(n,(r.local?function(t,e){var n,r;function i(){var i=e.apply(this,arguments);return i!==r&&(n=(r=i)&&function(e){this.setAttributeNS(t.space,t.local,i.call(this,e))}),n}return i._value=e,i}:function(t,e){var n,r;function i(){var i=e.apply(this,arguments);return i!==r&&(n=(r=i)&&function(e){this.setAttribute(t,i.call(this,e))}),n}return i._value=e,i})(r,e))},style:function(t,e,n){var r,i,o,a,u,s,l,c,f,h,d,p,v,y,m,g,_,w,b,x,k,E="transform"==(t+="")?q:tN;return null==e?this.styleTween(t,(r=t,function(){var t=(0,tP.S)(this,r),e=(this.style.removeProperty(r),(0,tP.S)(this,r));return t===e?null:t===i&&e===o?a:a=E(i=t,o=e)})).on("end.style."+t,tj(t)):"function"==typeof e?this.styleTween(t,(u=t,s=F(this,"style."+t,e),function(){var t=(0,tP.S)(this,u),e=s(this),n=e+"";return null==e&&(this.style.removeProperty(u),n=e=(0,tP.S)(this,u)),t===n?null:t===l&&n===c?f:(c=n,f=E(l=t,e))})).each((h=this._id,_="end."+(g="style."+(d=t)),function(){var t=$(this,h),e=t.on,n=null==t.value[g]?m||(m=tj(d)):void 0;(e!==p||y!==n)&&(v=(p=e).copy()).on(_,y=n),t.on=v})):this.styleTween(t,(w=t,k=e+"",function(){var t=(0,tP.S)(this,w);return t===k?null:t===b?x:x=E(b=t,e)}),n).on("end.style."+t,null)},styleTween:function(t,e,n){var r="style."+(t+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(null==e)return this.tween(r,null);if("function"!=typeof e)throw Error();return this.tween(r,function(t,e,n){var r,i;function o(){var o=e.apply(this,arguments);return o!==i&&(r=(i=o)&&function(e){this.style.setProperty(t,o.call(this,e),n)}),r}return o._value=e,o}(t,e,null==n?"":n))},text:function(t){var e,n;return this.tween("text","function"==typeof t?(e=F(this,"text",t),function(){var t=e(this);this.textContent=null==t?"":t}):(n=null==t?"":t+"",function(){this.textContent=n}))},textTween:function(t){var e="text";if(arguments.length<1)return(e=this.tween(e))&&e._value;if(null==t)return this.tween(e,null);if("function"!=typeof t)throw Error();return this.tween(e,function(t){var e,n;function r(){var r=t.apply(this,arguments);return r!==n&&(e=(n=r)&&function(t){this.textContent=r.call(this,t)}),e}return r._value=t,r}(t))},remove:function(){var t;return this.on("end.remove",(t=this._id,function(){var e=this.parentNode;for(var n in this.__transition)if(+n!==t)return;e&&e.removeChild(this)}))},tween:function(t,e){var n=this._id;if(t+="",arguments.length<2){for(var r,i=R(this.node(),n).tween,o=0,a=i.length;o<a;++o)if((r=i[o]).name===t)return r.value;return null}return this.each((null==e?function(t,e){var n,r;return function(){var i=$(this,t),o=i.tween;if(o!==n){r=n=o;for(var a=0,u=r.length;a<u;++a)if(r[a].name===e){(r=r.slice()).splice(a,1);break}}i.tween=r}}:function(t,e,n){var r,i;if("function"!=typeof n)throw Error();return function(){var o=$(this,t),a=o.tween;if(a!==r){i=(r=a).slice();for(var u={name:e,value:n},s=0,l=i.length;s<l;++s)if(i[s].name===e){i[s]=u;break}s===l&&i.push(u)}o.tween=i}})(n,t,e))},delay:function(t){var e=this._id;return arguments.length?this.each(("function"==typeof t?function(t,e){return function(){T(this,t).delay=+e.apply(this,arguments)}}:function(t,e){return e=+e,function(){T(this,t).delay=e}})(e,t)):R(this.node(),e).delay},duration:function(t){var e=this._id;return arguments.length?this.each(("function"==typeof t?function(t,e){return function(){$(this,t).duration=+e.apply(this,arguments)}}:function(t,e){return e=+e,function(){$(this,t).duration=e}})(e,t)):R(this.node(),e).duration},ease:function(t){var e=this._id;return arguments.length?this.each(function(t,e){if("function"!=typeof e)throw Error();return function(){$(this,t).ease=e}}(e,t)):R(this.node(),e).ease},easeVarying:function(t){var e;if("function"!=typeof t)throw Error();return this.each((e=this._id,function(){var n=t.apply(this,arguments);if("function"!=typeof n)throw Error();$(this,e).ease=n}))},end:function(){var t,e,n=this,r=n._id,i=n.size();return new Promise(function(o,a){var u={value:a},s={value:function(){0==--i&&o()}};n.each(function(){var n=$(this,r),i=n.on;i!==t&&((e=(t=i).copy())._.cancel.push(u),e._.interrupt.push(u),e._.end.push(s)),n.on=e}),0===i&&o()})},[Symbol.iterator]:tI[Symbol.iterator]};var tV={time:null,delay:0,duration:250,ease:function(t){return((t*=2)<=1?t*t*t:(t-=2)*t*t+2)/2}};h.ZP.prototype.interrupt=function(t){return this.each(function(){P(this,t)})},h.ZP.prototype.transition=function(t){var e,n;t instanceof tO?(e=t._id,t=t._name):(e=++tD,(n=tV).time=b(),t=null==t?null:t+"");for(var r=this._groups,i=r.length,o=0;o<i;++o)for(var a,u=r[o],s=u.length,l=0;l<s;++l)(a=u[l])&&C(a,t,e,l,u,n||function(t,e){for(var n;!(n=t.__transition)||!(n=n[e]);)if(!(t=t.parentNode))throw Error(`transition ${e} not found`);return n}(a,e));return new tO(r,this._parents,t,e)};var tq=t=>()=>t;function tH(t,{sourceEvent:e,target:n,transform:r,dispatch:i}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:e,enumerable:!0,configurable:!0},target:{value:n,enumerable:!0,configurable:!0},transform:{value:r,enumerable:!0,configurable:!0},_:{value:i}})}function tX(t,e,n){this.k=t,this.x=e,this.y=n}tX.prototype={constructor:tX,scale:function(t){return 1===t?this:new tX(this.k*t,this.x,this.y)},translate:function(t,e){return 0===t&0===e?this:new tX(this.k,this.x+this.k*t,this.y+this.k*e)},apply:function(t){return[t[0]*this.k+this.x,t[1]*this.k+this.y]},applyX:function(t){return t*this.k+this.x},applyY:function(t){return t*this.k+this.y},invert:function(t){return[(t[0]-this.x)/this.k,(t[1]-this.y)/this.k]},invertX:function(t){return(t-this.x)/this.k},invertY:function(t){return(t-this.y)/this.k},rescaleX:function(t){return t.copy().domain(t.range().map(this.invertX,this).map(t.invert,t))},rescaleY:function(t){return t.copy().domain(t.range().map(this.invertY,this).map(t.invert,t))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var tF=new tX(1,0,0);function tB(t){t.stopImmediatePropagation()}function tL(t){t.preventDefault(),t.stopImmediatePropagation()}function tY(t){return(!t.ctrlKey||"wheel"===t.type)&&!t.button}function tU(){var t=this;return t instanceof SVGElement?(t=t.ownerSVGElement||t).hasAttribute("viewBox")?[[(t=t.viewBox.baseVal).x,t.y],[t.x+t.width,t.y+t.height]]:[[0,0],[t.width.baseVal.value,t.height.baseVal.value]]:[[0,0],[t.clientWidth,t.clientHeight]]}function tW(){return this.__zoom||tF}function tK(t){return-t.deltaY*(1===t.deltaMode?.05:t.deltaMode?1:.002)*(t.ctrlKey?10:1)}function tG(){return navigator.maxTouchPoints||"ontouchstart"in this}function tJ(t,e,n){var r=t.invertX(e[0][0])-n[0][0],i=t.invertX(e[1][0])-n[1][0],o=t.invertY(e[0][1])-n[0][1],a=t.invertY(e[1][1])-n[1][1];return t.translate(i>r?(r+i)/2:Math.min(0,r)||Math.max(0,i),a>o?(o+a)/2:Math.min(0,o)||Math.max(0,a))}function tQ(){var t,e,n,r=tY,a=tU,u=tJ,s=tK,h=tG,d=[0,1/0],p=[[-1/0,-1/0],[1/0,1/0]],v=250,y=l,m=(0,i.Z)("start","zoom","end"),g=0,_=10;function w(t){t.property("__zoom",tW).on("wheel.zoom",Z,{passive:!1}).on("mousedown.zoom",A).on("dblclick.zoom",z).filter(h).on("touchstart.zoom",N).on("touchmove.zoom",C).on("touchend.zoom touchcancel.zoom",T).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function b(t,e){return(e=Math.max(d[0],Math.min(d[1],e)))===t.k?t:new tX(e,t.x,t.y)}function x(t,e,n){var r=e[0]-n[0]*t.k,i=e[1]-n[1]*t.k;return r===t.x&&i===t.y?t:new tX(t.k,r,i)}function k(t){return[(+t[0][0]+ +t[1][0])/2,(+t[0][1]+ +t[1][1])/2]}function E(t,e,n,r){t.on("start.zoom",function(){M(this,arguments).event(r).start()}).on("interrupt.zoom end.zoom",function(){M(this,arguments).event(r).end()}).tween("zoom",function(){var t=arguments,i=M(this,t).event(r),o=a.apply(this,t),u=null==n?k(o):"function"==typeof n?n.apply(this,t):n,s=Math.max(o[1][0]-o[0][0],o[1][1]-o[0][1]),l=this.__zoom,c="function"==typeof e?e.apply(this,t):e,f=y(l.invert(u).concat(s/l.k),c.invert(u).concat(s/c.k));return function(t){if(1===t)t=c;else{var e=f(t),n=s/e[2];t=new tX(n,u[0]-e[0]*n,u[1]-e[1]*n)}i.zoom(null,t)}})}function M(t,e,n){return!n&&t.__zooming||new S(t,e)}function S(t,e){this.that=t,this.args=e,this.active=0,this.sourceEvent=null,this.extent=a.apply(t,e),this.taps=0}function Z(t,...e){if(r.apply(this,arguments)){var n=M(this,e).event(t),i=this.__zoom,o=Math.max(d[0],Math.min(d[1],i.k*Math.pow(2,s.apply(this,arguments)))),a=(0,f.Z)(t);if(n.wheel)(n.mouse[0][0]!==a[0]||n.mouse[0][1]!==a[1])&&(n.mouse[1]=i.invert(n.mouse[0]=a)),clearTimeout(n.wheel);else{if(i.k===o)return;n.mouse=[a,i.invert(a)],P(this),n.start()}tL(t),n.wheel=setTimeout(function(){n.wheel=null,n.end()},150),n.zoom("mouse",u(x(b(i,o),n.mouse[0],n.mouse[1]),n.extent,p))}}function A(t,...e){if(!n&&r.apply(this,arguments)){var i=t.currentTarget,a=M(this,e,!0).event(t),s=(0,c.Z)(t.view).on("mousemove.zoom",function(t){if(tL(t),!a.moved){var e=t.clientX-h,n=t.clientY-d;a.moved=e*e+n*n>g}a.event(t).zoom("mouse",u(x(a.that.__zoom,a.mouse[0]=(0,f.Z)(t,i),a.mouse[1]),a.extent,p))},!0).on("mouseup.zoom",function(t){s.on("mousemove.zoom mouseup.zoom",null),(0,o.D)(t.view,a.moved),tL(t),a.event(t).end()},!0),l=(0,f.Z)(t,i),h=t.clientX,d=t.clientY;(0,o.Z)(t.view),tB(t),a.mouse=[l,this.__zoom.invert(l)],P(this),a.start()}}function z(t,...e){if(r.apply(this,arguments)){var n=this.__zoom,i=(0,f.Z)(t.changedTouches?t.changedTouches[0]:t,this),o=n.invert(i),s=n.k*(t.shiftKey?.5:2),l=u(x(b(n,s),i,o),a.apply(this,e),p);tL(t),v>0?(0,c.Z)(this).transition().duration(v).call(E,l,i,t):(0,c.Z)(this).call(w.transform,l,i,t)}}function N(n,...i){if(r.apply(this,arguments)){var o,a,u,s,l=n.touches,c=l.length,h=M(this,i,n.changedTouches.length===c).event(n);for(tB(n),a=0;a<c;++a)u=l[a],s=[s=(0,f.Z)(u,this),this.__zoom.invert(s),u.identifier],h.touch0?h.touch1||h.touch0[2]===s[2]||(h.touch1=s,h.taps=0):(h.touch0=s,o=!0,h.taps=1+!!t);t&&(t=clearTimeout(t)),o&&(h.taps<2&&(e=s[0],t=setTimeout(function(){t=null},500)),P(this),h.start())}}function C(t,...e){if(this.__zooming){var n,r,i,o,a=M(this,e).event(t),s=t.changedTouches,l=s.length;for(tL(t),n=0;n<l;++n)r=s[n],i=(0,f.Z)(r,this),a.touch0&&a.touch0[2]===r.identifier?a.touch0[0]=i:a.touch1&&a.touch1[2]===r.identifier&&(a.touch1[0]=i);if(r=a.that.__zoom,a.touch1){var c=a.touch0[0],h=a.touch0[1],d=a.touch1[0],v=a.touch1[1],y=(y=d[0]-c[0])*y+(y=d[1]-c[1])*y,m=(m=v[0]-h[0])*m+(m=v[1]-h[1])*m;r=b(r,Math.sqrt(y/m)),i=[(c[0]+d[0])/2,(c[1]+d[1])/2],o=[(h[0]+v[0])/2,(h[1]+v[1])/2]}else{if(!a.touch0)return;i=a.touch0[0],o=a.touch0[1]}a.zoom("touch",u(x(r,i,o),a.extent,p))}}function T(t,...r){if(this.__zooming){var i,o,a=M(this,r).event(t),u=t.changedTouches,s=u.length;for(tB(t),n&&clearTimeout(n),n=setTimeout(function(){n=null},500),i=0;i<s;++i)o=u[i],a.touch0&&a.touch0[2]===o.identifier?delete a.touch0:a.touch1&&a.touch1[2]===o.identifier&&delete a.touch1;if(a.touch1&&!a.touch0&&(a.touch0=a.touch1,delete a.touch1),a.touch0)a.touch0[1]=this.__zoom.invert(a.touch0[0]);else if(a.end(),2===a.taps&&(o=(0,f.Z)(o,this),Math.hypot(e[0]-o[0],e[1]-o[1])<_)){var l=(0,c.Z)(this).on("dblclick.zoom");l&&l.apply(this,arguments)}}}return w.transform=function(t,e,n,r){var i=t.selection?t.selection():t;i.property("__zoom",tW),t!==i?E(t,e,n,r):i.interrupt().each(function(){M(this,arguments).event(r).start().zoom(null,"function"==typeof e?e.apply(this,arguments):e).end()})},w.scaleBy=function(t,e,n,r){w.scaleTo(t,function(){var t=this.__zoom.k,n="function"==typeof e?e.apply(this,arguments):e;return t*n},n,r)},w.scaleTo=function(t,e,n,r){w.transform(t,function(){var t=a.apply(this,arguments),r=this.__zoom,i=null==n?k(t):"function"==typeof n?n.apply(this,arguments):n,o=r.invert(i),s="function"==typeof e?e.apply(this,arguments):e;return u(x(b(r,s),i,o),t,p)},n,r)},w.translateBy=function(t,e,n,r){w.transform(t,function(){return u(this.__zoom.translate("function"==typeof e?e.apply(this,arguments):e,"function"==typeof n?n.apply(this,arguments):n),a.apply(this,arguments),p)},null,r)},w.translateTo=function(t,e,n,r,i){w.transform(t,function(){var t=a.apply(this,arguments),i=this.__zoom,o=null==r?k(t):"function"==typeof r?r.apply(this,arguments):r;return u(tF.translate(o[0],o[1]).scale(i.k).translate("function"==typeof e?-e.apply(this,arguments):-e,"function"==typeof n?-n.apply(this,arguments):-n),t,p)},r,i)},S.prototype={event:function(t){return t&&(this.sourceEvent=t),this},start:function(){return 1==++this.active&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(t,e){return this.mouse&&"mouse"!==t&&(this.mouse[1]=e.invert(this.mouse[0])),this.touch0&&"touch"!==t&&(this.touch0[1]=e.invert(this.touch0[0])),this.touch1&&"touch"!==t&&(this.touch1[1]=e.invert(this.touch1[0])),this.that.__zoom=e,this.emit("zoom"),this},end:function(){return 0==--this.active&&(delete this.that.__zooming,this.emit("end")),this},emit:function(t){var e=(0,c.Z)(this.that).datum();m.call(t,this.that,new tH(t,{sourceEvent:this.sourceEvent,target:w,type:t,transform:this.that.__zoom,dispatch:m}),e)}},w.wheelDelta=function(t){return arguments.length?(s="function"==typeof t?t:tq(+t),w):s},w.filter=function(t){return arguments.length?(r="function"==typeof t?t:tq(!!t),w):r},w.touchable=function(t){return arguments.length?(h="function"==typeof t?t:tq(!!t),w):h},w.extent=function(t){return arguments.length?(a="function"==typeof t?t:tq([[+t[0][0],+t[0][1]],[+t[1][0],+t[1][1]]]),w):a},w.scaleExtent=function(t){return arguments.length?(d[0]=+t[0],d[1]=+t[1],w):[d[0],d[1]]},w.translateExtent=function(t){return arguments.length?(p[0][0]=+t[0][0],p[1][0]=+t[1][0],p[0][1]=+t[0][1],p[1][1]=+t[1][1],w):[[p[0][0],p[0][1]],[p[1][0],p[1][1]]]},w.constrain=function(t){return arguments.length?(u=t,w):u},w.duration=function(t){return arguments.length?(v=+t,w):v},w.interpolate=function(t){return arguments.length?(y=t,w):y},w.on=function(){var t=m.on.apply(m,arguments);return t===m?w:t},w.clickDistance=function(t){return arguments.length?(g=(t=+t)*t,w):Math.sqrt(g)},w.tapDistance=function(t){return arguments.length?(_=+t,w):_},w}tX.prototype},3589:function(t,e,n){"use strict";n.d(e,{Ue:function(){return f}});var r=n(4776),i=n(2208),o=n(2801);let{useDebugValue:a}=i,{useSyncExternalStoreWithSelector:u}=o,s=!1,l=t=>t,c=t=>{"function"!=typeof t&&console.warn("[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.");let e="function"==typeof t?(0,r.M)(t):t,n=(t,n)=>(function(t,e=l,n){n&&!s&&(console.warn("[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937"),s=!0);let r=u(t.subscribe,t.getState,t.getServerState||t.getInitialState,e,n);return a(r),r})(e,t,n);return Object.assign(n,e),n},f=t=>t?c(t):c},3157:function(t,e,n){"use strict";n.d(e,{XR:function(){return s},mW:function(){return a}});let r=new Map,i=t=>{let e=r.get(t);return e?Object.fromEntries(Object.entries(e.stores).map(([t,e])=>[t,e.getState()])):{}},o=(t,e,n)=>{if(void 0===t)return{type:"untracked",connection:e.connect(n)};let i=r.get(n.name);if(i)return{type:"tracked",store:t,...i};let o={connection:e.connect(n),stores:{}};return r.set(n.name,o),{type:"tracked",store:t,...o}},a=(t,e={})=>(n,r,a)=>{let s;let{enabled:l,anonymousActionType:c,store:f,...h}=e;try{s=(null==l||l)&&window.__REDUX_DEVTOOLS_EXTENSION__}catch(t){}if(!s)return l&&console.warn("[zustand devtools middleware] Please install/enable Redux devtools extension"),t(n,r,a);let{connection:d,...p}=o(f,s,h),v=!0;a.setState=(t,e,o)=>{let u=n(t,e);if(!v)return u;let s=void 0===o?{type:c||"anonymous"}:"string"==typeof o?{type:o}:o;return void 0===f?null==d||d.send(s,r()):null==d||d.send({...s,type:`${f}/${s.type}`},{...i(h.name),[f]:a.getState()}),u};let y=(...t)=>{let e=v;v=!1,n(...t),v=e},m=t(a.setState,r,a);if("untracked"===p.type?null==d||d.init(m):(p.stores[p.store]=a,null==d||d.init(Object.fromEntries(Object.entries(p.stores).map(([t,e])=>[t,t===p.store?m:e.getState()])))),a.dispatchFromDevtools&&"function"==typeof a.dispatch){let t=!1,e=a.dispatch;a.dispatch=(...n)=>{"__setState"!==n[0].type||t||(console.warn('[zustand devtools middleware] "__setState" action type is reserved to set state from the devtools. Avoid using it.'),t=!0),e(...n)}}return d.subscribe(t=>{var e;switch(t.type){case"ACTION":if("string"!=typeof t.payload){console.error("[zustand devtools middleware] Unsupported action format");return}return u(t.payload,t=>{if("__setState"===t.type){if(void 0===f){y(t.state);return}1!==Object.keys(t.state).length&&console.error(`
                    [zustand devtools middleware] Unsupported __setState action format. 
                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),
                    and value of this only key should be a state object. Example: { "type": "__setState", "state": { "abc123Store": { "foo": "bar" } } }
                    `);let e=t.state[f];if(null==e)return;JSON.stringify(a.getState())!==JSON.stringify(e)&&y(e);return}a.dispatchFromDevtools&&"function"==typeof a.dispatch&&a.dispatch(t)});case"DISPATCH":switch(t.payload.type){case"RESET":if(y(m),void 0===f)return null==d?void 0:d.init(a.getState());return null==d?void 0:d.init(i(h.name));case"COMMIT":if(void 0===f){null==d||d.init(a.getState());break}return null==d?void 0:d.init(i(h.name));case"ROLLBACK":return u(t.state,t=>{if(void 0===f){y(t),null==d||d.init(a.getState());return}y(t[f]),null==d||d.init(i(h.name))});case"JUMP_TO_STATE":case"JUMP_TO_ACTION":return u(t.state,t=>{if(void 0===f){y(t);return}JSON.stringify(a.getState())!==JSON.stringify(t[f])&&y(t[f])});case"IMPORT_STATE":{let{nextLiftedState:n}=t.payload,r=null==(e=n.computedStates.slice(-1)[0])?void 0:e.state;if(!r)return;void 0===f?y(r):y(r[f]),null==d||d.send(null,n);break}case"PAUSE_RECORDING":return v=!v}return}}),m},u=(t,e)=>{let n;try{n=JSON.parse(t)}catch(t){console.error("[zustand devtools middleware] Could not parse the received json",t)}void 0!==n&&e(n)},s=t=>(e,n,r)=>{let i=r.subscribe;return r.subscribe=(t,e,n)=>{let o=t;if(e){let i=(null==n?void 0:n.equalityFn)||Object.is,a=t(r.getState());o=n=>{let r=t(n);if(!i(a,r)){let t=a;e(a=r,t)}},(null==n?void 0:n.fireImmediately)&&e(a,a)}return i(o)},t(e,n,r)}},1619:function(t,e,n){"use strict";function r(t,e){if(Object.is(t,e))return!0;if("object"!=typeof t||null===t||"object"!=typeof e||null===e)return!1;if(t instanceof Map&&e instanceof Map){if(t.size!==e.size)return!1;for(let[n,r]of t)if(!Object.is(r,e.get(n)))return!1;return!0}if(t instanceof Set&&e instanceof Set){if(t.size!==e.size)return!1;for(let n of t)if(!e.has(n))return!1;return!0}let n=Object.keys(t);if(n.length!==Object.keys(e).length)return!1;for(let r of n)if(!Object.prototype.hasOwnProperty.call(e,r)||!Object.is(t[r],e[r]))return!1;return!0}n.d(e,{X:function(){return r}})},1720:function(t,e,n){"use strict";n.d(e,{F:function(){return f},s:function(){return l}});var r=n(2208),i=n(2801),o=n(4776);let{useDebugValue:a}=r,{useSyncExternalStoreWithSelector:u}=i,s=t=>t;function l(t,e=s,n){let r=u(t.subscribe,t.getState,t.getServerState||t.getInitialState,e,n);return a(r),r}let c=(t,e)=>{let n=(0,o.M)(t),r=(t,r=e)=>l(n,t,r);return Object.assign(r,n),r},f=(t,e)=>t?c(t,e):c},4776:function(t,e,n){"use strict";n.d(e,{M:function(){return i}});let r=t=>{let e;let n=new Set,r=(t,r)=>{let i="function"==typeof t?t(e):t;if(!Object.is(i,e)){let t=e;e=(null!=r?r:"object"!=typeof i||null===i)?i:Object.assign({},e,i),n.forEach(n=>n(e,t))}},i=()=>e,o={setState:r,getState:i,getInitialState:()=>a,subscribe:t=>(n.add(t),()=>n.delete(t)),destroy:()=>{console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}},a=e=t(r,i,o);return o},i=t=>t?r(t):r}}]);