import { z } from 'zod';
import { BaseEntitySchema, PositionSchema, NodeType, ConnectionType, DataType } from './common';

// Node connection schema
export const NodeConnectionSchema = z.object({
  id: z.string(),
  type: z.nativeEnum(ConnectionType),
  dataType: z.nativeEnum(DataType),
  label: z.string(),
  required: z.boolean().default(false),
  multiple: z.boolean().default(false)
});

export type NodeConnection = z.infer<typeof NodeConnectionSchema>;

// Node configuration schema
export const NodeConfigSchema = z.record(z.any());

export type NodeConfig = z.infer<typeof NodeConfigSchema>;

// Node schema
export const NodeSchema = BaseEntitySchema.extend({
  type: z.nativeEnum(NodeType),
  name: z.string(),
  label: z.string(),
  description: z.string().optional(),
  category: z.string(),
  version: z.string().default('1.0.0'),
  position: PositionSchema,
  inputs: z.array(NodeConnectionSchema),
  outputs: z.array(NodeConnectionSchema),
  config: NodeConfigSchema,
  flowId: z.string().uuid(),
  isCustom: z.boolean().default(false),
  icon: z.string().optional(),
  color: z.string().optional(),
  documentation: z.string().optional()
});

export type Node = z.infer<typeof NodeSchema>;

// Flow connection schema (connections between nodes)
export const FlowConnectionSchema = z.object({
  id: z.string(),
  sourceNodeId: z.string().uuid(),
  sourceHandle: z.string(),
  targetNodeId: z.string().uuid(),
  targetHandle: z.string(),
  flowId: z.string().uuid()
});

export type FlowConnection = z.infer<typeof FlowConnectionSchema>;

// Node execution result schema
export const NodeExecutionResultSchema = z.object({
  nodeId: z.string().uuid(),
  status: z.enum(['success', 'error', 'pending']),
  output: z.any().optional(),
  error: z.string().optional(),
  executionTime: z.number().optional(),
  timestamp: z.string().datetime()
});

export type NodeExecutionResult = z.infer<typeof NodeExecutionResultSchema>;

// Node template schema for creating new nodes
export const NodeTemplateSchema = z.object({
  type: z.nativeEnum(NodeType),
  name: z.string(),
  label: z.string(),
  description: z.string(),
  category: z.string(),
  version: z.string(),
  inputs: z.array(NodeConnectionSchema),
  outputs: z.array(NodeConnectionSchema),
  defaultConfig: NodeConfigSchema,
  icon: z.string().optional(),
  color: z.string().optional(),
  documentation: z.string().optional(),
  isCustom: z.boolean().default(false)
});

export type NodeTemplate = z.infer<typeof NodeTemplateSchema>;
