# Flowwise Clone - Visual AI Agent Builder

A comprehensive clone of Flowwise that allows you to build AI agents, chatbots, and multi-agent systems visually through a drag-and-drop interface.

## Features

- 🎨 **Visual Flow Builder** - Drag-and-drop interface for building AI workflows
- 🤖 **Multi-Agent Systems** - Coordinate multiple AI agents for complex tasks
- 💬 **Chat Assistants** - Build conversational AI with memory and context
- 🔗 **LLM Integrations** - Support for OpenAI, Anthropic, Google, and local models
- 📚 **RAG (Retrieval Augmented Generation)** - Connect to vector databases and documents
- 🛠️ **Tool Integration** - Web search, APIs, file processing, and custom tools
- 👥 **Human-in-the-Loop** - Manual approval workflows and task review
- 📊 **Observability** - Execution tracing, logging, and monitoring
- 🔐 **Authentication** - User management and role-based access control
- 🚀 **API & SDK** - REST APIs and embedded chat widgets

## Architecture

This project follows a monorepo structure with the following packages:

- `packages/ui` - React frontend with Next.js
- `packages/server` - Node.js backend API
- `packages/components` - Reusable node components
- `packages/shared` - Shared types and utilities
- `packages/database` - Database schemas and migrations

## Quick Start

### Prerequisites

- Node.js >= 18.15.0
- pnpm >= 8.0.0

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd flowwise-clone
```

2. Install dependencies:
```bash
pnpm install
```

3. Set up environment variables:
```bash
cp packages/server/.env.example packages/server/.env
cp packages/ui/.env.example packages/ui/.env
```

4. Build the project:
```bash
pnpm build
```

5. Start the development server:
```bash
pnpm dev
```

6. Open [http://localhost:3000](http://localhost:3000) in your browser

## Development

### Project Structure

```
flowwise-clone/
├── packages/
│   ├── ui/                 # Frontend React app
│   ├── server/             # Backend Node.js API
│   ├── components/         # Node components library
│   ├── shared/             # Shared utilities
│   └── database/           # Database layer
├── docker/                 # Docker configurations
├── docs/                   # Documentation
└── scripts/                # Build and deployment scripts
```

### Available Scripts

- `pnpm dev` - Start development servers
- `pnpm build` - Build all packages
- `pnpm start` - Start production servers
- `pnpm lint` - Run linting
- `pnpm test` - Run tests
- `pnpm format` - Format code with Prettier

## Deployment

### Docker

1. Build the Docker image:
```bash
docker build -t flowwise-clone .
```

2. Run the container:
```bash
docker run -p 3000:3000 flowwise-clone
```

### Docker Compose

```bash
docker-compose up -d
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the Apache License 2.0 - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- Inspired by [Flowise](https://github.com/FlowiseAI/Flowise)
- Built with modern web technologies and AI frameworks
