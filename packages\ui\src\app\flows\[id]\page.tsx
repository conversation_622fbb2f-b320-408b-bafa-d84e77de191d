'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { FlowEditor } from '@/components/flow/FlowEditor';
import { FlowToolbar } from '@/components/flow/FlowToolbar';
import { NodeSidebar } from '@/components/flow/NodeSidebar';
import { PropertiesPanel } from '@/components/flow/PropertiesPanel';
import { ChatPanel } from '@/components/chat/ChatPanel';
import { useFlowStore } from '@/store/flowStore';
import { Flow, FlowStatus, NodeType, ConnectionType, DataType } from '@flowwise-clone/shared';

// Mock flow data - replace with actual API call
const mockFlow: Flow = {
  id: '1',
  name: 'Customer Support Bot',
  description: 'AI-powered customer support chatbot',
  status: FlowStatus.ACTIVE,
  nodes: [
    {
      id: 'start-node',
      type: NodeType.START,
      name: 'Start',
      label: 'Start',
      category: 'System',
      version: '1.0.0',
      position: { x: 100, y: 100 },
      inputs: [],
      outputs: [
        {
          id: 'output',
          type: ConnectionType.OUTPUT,
          dataType: DataType.STRING,
          label: 'Output',
          required: false,
          multiple: false,
        },
      ],
      config: {},
      flowId: '1',
      isCustom: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: 'llm-node',
      type: NodeType.LLM,
      name: 'OpenAI GPT-4',
      label: 'OpenAI GPT-4',
      category: 'LLM',
      version: '1.0.0',
      position: { x: 300, y: 100 },
      inputs: [
        {
          id: 'prompt',
          type: ConnectionType.INPUT,
          dataType: DataType.STRING,
          label: 'Prompt',
          required: true,
          multiple: false,
        },
      ],
      outputs: [
        {
          id: 'response',
          type: ConnectionType.OUTPUT,
          dataType: DataType.STRING,
          label: 'Response',
          required: false,
          multiple: false,
        },
      ],
      config: {
        model: 'gpt-4',
        temperature: 0.7,
        maxTokens: 1000,
      },
      flowId: '1',
      isCustom: false,
      color: '#3B82F6',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
  ],
  connections: [
    {
      id: 'connection-1',
      sourceNodeId: 'start-node',
      sourceHandle: 'output',
      targetNodeId: 'llm-node',
      targetHandle: 'prompt',
      flowId: '1',
    },
  ],
  userId: 'user-1',
  isPublic: false,
  tags: ['chatbot', 'support'],
  version: '1.0.0',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
};

export default function FlowEditorPage() {
  const params = useParams();
  const flowId = params.id as string;
  const { setCurrentFlow, currentFlow, isLoading, setLoading } = useFlowStore();
  const [showChat, setShowChat] = useState(false);
  const [showProperties, setShowProperties] = useState(true);

  useEffect(() => {
    const loadFlow = async () => {
      setLoading(true);
      try {
        // TODO: Replace with actual API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        setCurrentFlow(mockFlow);
      } catch (error) {
        console.error('Failed to load flow:', error);
      } finally {
        setLoading(false);
      }
    };

    loadFlow();
  }, [flowId, setCurrentFlow, setLoading]);

  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading flow...</p>
        </div>
      </div>
    );
  }

  if (!currentFlow) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Flow not found</h2>
          <p className="text-gray-600">The requested flow could not be loaded.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* Toolbar */}
      <FlowToolbar
        flow={currentFlow}
        onToggleChat={() => setShowChat(!showChat)}
        onToggleProperties={() => setShowProperties(!showProperties)}
        showChat={showChat}
        showProperties={showProperties}
      />

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Node Sidebar */}
        <NodeSidebar />

        {/* Flow Editor */}
        <div className="flex-1 relative">
          <FlowEditor />
        </div>

        {/* Properties Panel */}
        {showProperties && (
          <div className="w-80 border-l bg-white">
            <PropertiesPanel />
          </div>
        )}

        {/* Chat Panel */}
        {showChat && (
          <div className="w-96 border-l bg-white">
            <ChatPanel flowId={flowId} />
          </div>
        )}
      </div>
    </div>
  );
}
