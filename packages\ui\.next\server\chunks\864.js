"use strict";exports.id=864,exports.ids=[864],exports.modules={9128:(e,t,n)=>{n.d(t,{Ry:()=>c});var r=new WeakMap,o=new WeakMap,i={},l=0,a=function(e){return e&&(e.host||a(e.parentNode))},u=function(e,t,n,u){var c=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=a(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});i[n]||(i[n]=new WeakMap);var s=i[n],d=[],f=new Set,p=new Set(c),h=function(e){!e||f.has(e)||(f.add(e),h(e.parentNode))};c.forEach(h);var m=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))m(e);else try{var t=e.getAttribute(u),i=null!==t&&"false"!==t,l=(r.get(e)||0)+1,a=(s.get(e)||0)+1;r.set(e,l),s.set(e,a),d.push(e),1===l&&i&&o.set(e,!0),1===a&&e.setAttribute(n,"true"),i||e.setAttribute(u,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return m(t),f.clear(),l++,function(){d.forEach(function(e){var t=r.get(e)-1,i=s.get(e)-1;r.set(e,t),s.set(e,i),t||(o.has(e)||e.removeAttribute(u),o.delete(e)),i||e.removeAttribute(n)}),--l||(r=new WeakMap,r=new WeakMap,o=new WeakMap,i={})}},c=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r,o=Array.from(Array.isArray(e)?e:[e]),i=t||(r=e,"undefined"==typeof document?null:(Array.isArray(r)?r[0]:r).ownerDocument.body);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live], script"))),u(o,i,n,"aria-hidden")):function(){return null}}},6532:(e,t,n)=>{n.d(t,{Z:()=>r});let r=(0,n(3509).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},6e3:(e,t,n)=>{n.d(t,{Z:()=>r});let r=(0,n(3509).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},5990:(e,t,n)=>{n.d(t,{Z:()=>r});let r=(0,n(3509).Z)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},5297:(e,t,n)=>{n.d(t,{Z:()=>r});let r=(0,n(3509).Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},4669:(e,t,n)=>{n.d(t,{Z:()=>r});let r=(0,n(3509).Z)("MoreHorizontal",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},3407:(e,t,n)=>{n.d(t,{Z:()=>r});let r=(0,n(3509).Z)("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]])},2371:(e,t,n)=>{n.d(t,{Z:()=>r});let r=(0,n(3509).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},3764:(e,t,n)=>{n.d(t,{Z:()=>r});let r=(0,n(3509).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},7637:(e,t,n)=>{n.d(t,{Z:()=>r});let r=(0,n(3509).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},3205:(e,t,n)=>{n.d(t,{Z:()=>U});var r,o,i=function(){return(i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function l(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create,Object.create;var a=("function"==typeof SuppressedError&&SuppressedError,n(6364)),u="right-scroll-bar-position",c="width-before-scroll-bar";function s(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var d="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,f=new WeakMap;function p(e){return e}var h=function(e){void 0===e&&(e={});var t,n,r,o=(void 0===t&&(t=p),n=[],r=!1,{read:function(){if(r)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var o=t(e,r);return n.push(o),function(){n=n.filter(function(e){return e!==o})}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){r=!0;var t=[];if(n.length){var o=n;n=[],o.forEach(e),t=n}var i=function(){var n=t;t=[],n.forEach(e)},l=function(){return Promise.resolve().then(i)};l(),n={push:function(e){t.push(e),l()},filter:function(e){return t=t.filter(e),n}}}});return o.options=i({async:!0,ssr:!1},e),o}(),m=function(){},v=a.forwardRef(function(e,t){var n,r,o,u,c=a.useRef(null),p=a.useState({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:m}),v=p[0],g=p[1],y=e.forwardProps,w=e.children,b=e.className,x=e.removeScrollBar,E=e.enabled,S=e.shards,C=e.sideCar,R=e.noRelative,M=e.noIsolation,T=e.inert,A=e.allowPinchZoom,P=e.as,k=e.gapMode,N=l(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),L=(n=[c,t],r=function(e){return n.forEach(function(t){return s(t,e)})},(o=(0,a.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,u=o.facade,d(function(){var e=f.get(u);if(e){var t=new Set(e),r=new Set(n),o=u.current;t.forEach(function(e){r.has(e)||s(e,null)}),r.forEach(function(e){t.has(e)||s(e,o)})}f.set(u,n)},[n]),u),O=i(i({},N),v);return a.createElement(a.Fragment,null,E&&a.createElement(C,{sideCar:h,removeScrollBar:x,shards:S,noRelative:R,noIsolation:M,inert:T,setCallbacks:g,allowPinchZoom:!!A,lockRef:c,gapMode:k}),y?a.cloneElement(a.Children.only(w),i(i({},O),{ref:L})):a.createElement(void 0===P?"div":P,i({},O,{className:b,ref:L}),w))});v.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},v.classNames={fullWidth:c,zeroRight:u};var g=function(e){var t=e.sideCar,n=l(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return a.createElement(r,i({},n))};g.isSideCarExport=!0;var y=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,l;(i=t).styleSheet?i.styleSheet.cssText=r:i.appendChild(document.createTextNode(r)),l=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(l)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},w=function(){var e=y();return function(t,n){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},b=function(){var e=w();return function(t){return e(t.styles,t.dynamic),null}},x={left:0,top:0,right:0,gap:0},E=function(e){return parseInt(e||"",10)||0},S=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[E(n),E(r),E(o)]},C=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return x;var t=S(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},R=b(),M="data-scroll-locked",T=function(e,t,n,r){var o=e.left,i=e.top,l=e.right,a=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(a,"px ").concat(r,";\n  }\n  body[").concat(M,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(l,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(a,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(u," {\n    right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(c," {\n    margin-right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(c," .").concat(c," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(M,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(a,"px;\n  }\n")},A=function(){var e=parseInt(document.body.getAttribute(M)||"0",10);return isFinite(e)?e:0},P=function(){a.useEffect(function(){return document.body.setAttribute(M,(A()+1).toString()),function(){var e=A()-1;e<=0?document.body.removeAttribute(M):document.body.setAttribute(M,e.toString())}},[])},k=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;P();var i=a.useMemo(function(){return C(o)},[o]);return a.createElement(R,{styles:T(i,!t,o,n?"":"!important")})},N=!1;if("undefined"!=typeof window)try{var L=Object.defineProperty({},"passive",{get:function(){return N=!0,!0}});window.addEventListener("test",L,L),window.removeEventListener("test",L,L)}catch(e){N=!1}var O=!!N&&{passive:!1},j=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===n[t])},D=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),W(e,r)){var o=I(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},W=function(e,t){return"v"===e?j(t,"overflowY"):j(t,"overflowX")},I=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},F=function(e,t,n,r,o){var i,l=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),a=l*r,u=n.target,c=t.contains(u),s=!1,d=a>0,f=0,p=0;do{if(!u)break;var h=I(e,u),m=h[0],v=h[1]-h[2]-l*m;(m||v)&&W(e,u)&&(f+=v,p+=m);var g=u.parentNode;u=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&a>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-a>p)&&(s=!0),s},V=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},H=function(e){return[e.deltaX,e.deltaY]},B=function(e){return e&&"current"in e?e.current:e},_=0,z=[];let Z=(r=function(e){var t=a.useRef([]),n=a.useRef([0,0]),r=a.useRef(),o=a.useState(_++)[0],i=a.useState(b)[0],l=a.useRef(e);a.useEffect(function(){l.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(B),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!l.current.allowPinchZoom;var o,i=V(e),a=n.current,u="deltaX"in e?e.deltaX:a[0]-i[0],c="deltaY"in e?e.deltaY:a[1]-i[1],s=e.target,d=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=D(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=D(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||c)&&(r.current=o),!o)return!0;var p=r.current||o;return F(p,t,e,"h"===p?u:c,!0)},[]),c=a.useCallback(function(e){if(z.length&&z[z.length-1]===i){var n="deltaY"in e?H(e):V(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(l.current.shards||[]).map(B).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!l.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=a.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=a.useCallback(function(e){n.current=V(e),r.current=void 0},[]),f=a.useCallback(function(t){s(t.type,H(t),t.target,u(t,e.lockRef.current))},[]),p=a.useCallback(function(t){s(t.type,V(t),t.target,u(t,e.lockRef.current))},[]);a.useEffect(function(){return z.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",c,O),document.addEventListener("touchmove",c,O),document.addEventListener("touchstart",d,O),function(){z=z.filter(function(e){return e!==i}),document.removeEventListener("wheel",c,O),document.removeEventListener("touchmove",c,O),document.removeEventListener("touchstart",d,O)}},[]);var h=e.removeScrollBar,m=e.inert;return a.createElement(a.Fragment,null,m?a.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?a.createElement(k,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},h.useMedium(r),g);var $=a.forwardRef(function(e,t){return a.createElement(v,i({},e,{ref:t,sideCar:Z}))});$.classNames=v.classNames;let U=$},6295:(e,t,n)=>{n.d(t,{M:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},5674:(e,t,n)=>{n.d(t,{B:()=>u});var r=n(6364),o=n(8687),i=n(6958),l=n(7723),a=n(7491);function u(e){let t=e+"CollectionProvider",[n,u]=(0,o.b)(t),[c,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),d=e=>{let{scope:t,children:n}=e,o=r.useRef(null),i=r.useRef(new Map).current;return(0,a.jsx)(c,{scope:t,itemMap:i,collectionRef:o,children:n})};d.displayName=t;let f=e+"CollectionSlot",p=(0,l.Z8)(f),h=r.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=s(f,n),l=(0,i.e)(t,o.collectionRef);return(0,a.jsx)(p,{ref:l,children:r})});h.displayName=f;let m=e+"CollectionItemSlot",v="data-radix-collection-item",g=(0,l.Z8)(m),y=r.forwardRef((e,t)=>{let{scope:n,children:o,...l}=e,u=r.useRef(null),c=(0,i.e)(t,u),d=s(m,n);return r.useEffect(()=>(d.itemMap.set(u,{ref:u,...l}),()=>void d.itemMap.delete(u))),(0,a.jsx)(g,{[v]:"",ref:c,children:o})});return y.displayName=m,[{Provider:d,Slot:h,ItemSlot:y},function(t){let n=s(e+"CollectionConsumer",t);return r.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${v}]`));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},u]}},8687:(e,t,n)=>{n.d(t,{b:()=>l,k:()=>i});var r=n(6364),o=n(7491);function i(e,t){let n=r.createContext(t),i=e=>{let{children:t,...i}=e,l=r.useMemo(()=>i,Object.values(i));return(0,o.jsx)(n.Provider,{value:l,children:t})};return i.displayName=e+"Provider",[i,function(o){let i=r.useContext(n);if(i)return i;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function l(e,t=[]){let n=[],i=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return i.scopeName=e,[function(t,i){let l=r.createContext(i),a=n.length;n=[...n,i];let u=t=>{let{scope:n,children:i,...u}=t,c=n?.[e]?.[a]||l,s=r.useMemo(()=>u,Object.values(u));return(0,o.jsx)(c.Provider,{value:s,children:i})};return u.displayName=t+"Provider",[u,function(n,o){let u=o?.[e]?.[a]||l,c=r.useContext(u);if(c)return c;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(i,...t)]}},3175:(e,t,n)=>{n.d(t,{gm:()=>i});var r=n(6364);n(7491);var o=r.createContext(void 0);function i(e){let t=r.useContext(o);return e||t||"ltr"}},40:(e,t,n)=>{n.d(t,{XB:()=>f});var r,o=n(6364),i=n(6295),l=n(4582),a=n(6958),u=n(1285),c=n(7491),s="dismissableLayer.update",d=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:f,onPointerDownOutside:m,onFocusOutside:v,onInteractOutside:g,onDismiss:y,...w}=e,b=o.useContext(d),[x,E]=o.useState(null),S=x?.ownerDocument??globalThis?.document,[,C]=o.useState({}),R=(0,a.e)(t,e=>E(e)),M=Array.from(b.layers),[T]=[...b.layersWithOutsidePointerEventsDisabled].slice(-1),A=M.indexOf(T),P=x?M.indexOf(x):-1,k=b.layersWithOutsidePointerEventsDisabled.size>0,N=P>=A,L=function(e,t=globalThis?.document){let n=(0,u.W)(e),r=o.useRef(!1),i=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!r.current){let r=function(){h("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",i.current),i.current=r,t.addEventListener("click",i.current,{once:!0})):r()}else t.removeEventListener("click",i.current);r.current=!1},o=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(o),t.removeEventListener("pointerdown",e),t.removeEventListener("click",i.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,n=[...b.branches].some(e=>e.contains(t));!N||n||(m?.(e),g?.(e),e.defaultPrevented||y?.())},S),O=function(e,t=globalThis?.document){let n=(0,u.W)(e),r=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!r.current&&h("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;[...b.branches].some(e=>e.contains(t))||(v?.(e),g?.(e),e.defaultPrevented||y?.())},S);return function(e,t=globalThis?.document){let n=(0,u.W)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{P!==b.layers.size-1||(f?.(e),!e.defaultPrevented&&y&&(e.preventDefault(),y()))},S),o.useEffect(()=>{if(x)return n&&(0===b.layersWithOutsidePointerEventsDisabled.size&&(r=S.body.style.pointerEvents,S.body.style.pointerEvents="none"),b.layersWithOutsidePointerEventsDisabled.add(x)),b.layers.add(x),p(),()=>{n&&1===b.layersWithOutsidePointerEventsDisabled.size&&(S.body.style.pointerEvents=r)}},[x,S,n,b]),o.useEffect(()=>()=>{x&&(b.layers.delete(x),b.layersWithOutsidePointerEventsDisabled.delete(x),p())},[x,b]),o.useEffect(()=>{let e=()=>C({});return document.addEventListener(s,e),()=>document.removeEventListener(s,e)},[]),(0,c.jsx)(l.WV.div,{...w,ref:R,style:{pointerEvents:k?N?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.M)(e.onFocusCapture,O.onFocusCapture),onBlurCapture:(0,i.M)(e.onBlurCapture,O.onBlurCapture),onPointerDownCapture:(0,i.M)(e.onPointerDownCapture,L.onPointerDownCapture)})});function p(){let e=new CustomEvent(s);document.dispatchEvent(e)}function h(e,t,n,{discrete:r}){let o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?(0,l.jH)(o,i):o.dispatchEvent(i)}f.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(d),r=o.useRef(null),i=(0,a.e)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,c.jsx)(l.WV.div,{...e,ref:i})}).displayName="DismissableLayerBranch"},8425:(e,t,n)=>{n.d(t,{EW:()=>i});var r=n(6364),o=0;function i(){r.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??l()),document.body.insertAdjacentElement("beforeend",e[1]??l()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function l(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},7044:(e,t,n)=>{n.d(t,{M:()=>d});var r=n(6364),o=n(6958),i=n(4582),l=n(1285),a=n(7491),u="focusScope.autoFocusOnMount",c="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},d=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:d=!1,onMountAutoFocus:v,onUnmountAutoFocus:g,...y}=e,[w,b]=r.useState(null),x=(0,l.W)(v),E=(0,l.W)(g),S=r.useRef(null),C=(0,o.e)(t,e=>b(e)),R=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(d){let e=function(e){if(R.paused||!w)return;let t=e.target;w.contains(t)?S.current=t:h(S.current,{select:!0})},t=function(e){if(R.paused||!w)return;let t=e.relatedTarget;null===t||w.contains(t)||h(S.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&h(w)});return w&&n.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[d,w,R.paused]),r.useEffect(()=>{if(w){m.add(R);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(u,s);w.addEventListener(u,x),w.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(h(r,{select:t}),document.activeElement!==n)return}(f(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&h(w))}return()=>{w.removeEventListener(u,x),setTimeout(()=>{let t=new CustomEvent(c,s);w.addEventListener(c,E),w.dispatchEvent(t),t.defaultPrevented||h(e??document.body,{select:!0}),w.removeEventListener(c,E),m.remove(R)},0)}}},[w,x,E,R]);let M=r.useCallback(e=>{if(!n&&!d||R.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,i]=function(e){let t=f(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&i?e.shiftKey||r!==i?e.shiftKey&&r===o&&(e.preventDefault(),n&&h(i,{select:!0})):(e.preventDefault(),n&&h(o,{select:!0})):r===t&&e.preventDefault()}},[n,d,R.paused]);return(0,a.jsx)(i.WV.div,{tabIndex:-1,...y,ref:C,onKeyDown:M})});function f(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function h(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}d.displayName="FocusScope";var m=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=v(e,t)).unshift(t)},remove(t){e=v(e,t),e[0]?.resume()}}}();function v(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},5353:(e,t,n)=>{n.d(t,{M:()=>u});var r,o=n(6364),i=n(5805),l=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),a=0;function u(e){let[t,n]=o.useState(l());return(0,i.b)(()=>{e||n(e=>e??String(a++))},[e]),e||(t?`radix-${t}`:"")}},2245:(e,t,n)=>{n.d(t,{f:()=>a});var r=n(6364),o=n(4582),i=n(7491),l=r.forwardRef((e,t)=>(0,i.jsx)(o.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var a=l},1288:(e,t,n)=>{n.d(t,{h:()=>u});var r=n(6364),o=n(1965),i=n(4582),l=n(5805),a=n(7491),u=r.forwardRef((e,t)=>{let{container:n,...u}=e,[c,s]=r.useState(!1);(0,l.b)(()=>s(!0),[]);let d=n||c&&globalThis?.document?.body;return d?o.createPortal((0,a.jsx)(i.WV.div,{...u,ref:t}),d):null});u.displayName="Portal"},9830:(e,t,n)=>{n.d(t,{z:()=>l});var r=n(6364),o=n(6958),i=n(5805),l=e=>{let{present:t,children:n}=e,l=function(e){var t,n;let[o,l]=r.useState(),u=r.useRef(null),c=r.useRef(e),s=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>n[e][t]??e,t));return r.useEffect(()=>{let e=a(u.current);s.current="mounted"===d?e:"none"},[d]),(0,i.b)(()=>{let t=u.current,n=c.current;if(n!==e){let r=s.current,o=a(t);e?f("MOUNT"):"none"===o||t?.display==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),c.current=e}},[e,f]),(0,i.b)(()=>{if(o){let e;let t=o.ownerDocument.defaultView??window,n=n=>{let r=a(u.current).includes(n.animationName);if(n.target===o&&r&&(f("ANIMATION_END"),!c.current)){let n=o.style.animationFillMode;o.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=n)})}},r=e=>{e.target===o&&(s.current=a(u.current))};return o.addEventListener("animationstart",r),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{t.clearTimeout(e),o.removeEventListener("animationstart",r),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{u.current=e?getComputedStyle(e):null,l(e)},[])}}(t),u="function"==typeof n?n({present:l.isPresent}):r.Children.only(n),c=(0,o.e)(l.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(u));return"function"==typeof n||l.isPresent?r.cloneElement(u,{ref:c}):null};function a(e){return e?.animationName||"none"}l.displayName="Presence"},4582:(e,t,n)=>{n.d(t,{WV:()=>a,jH:()=>u});var r=n(6364),o=n(1965),i=n(7723),l=n(7491),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,i.Z8)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...i}=e,a=o?n:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(a,{...i,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function u(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},7670:(e,t,n)=>{n.d(t,{VY:()=>ns,ZA:()=>nf,JO:()=>nu,ck:()=>nh,wU:()=>nv,eT:()=>nm,__:()=>np,h_:()=>nc,fC:()=>ni,$G:()=>ny,u_:()=>ng,Z0:()=>nw,xz:()=>nl,B4:()=>na,l_:()=>nd});var r=n(6364),o=n(1965);function i(e,[t,n]){return Math.min(n,Math.max(t,e))}var l=n(6295),a=n(5674),u=n(6958),c=n(8687),s=n(3175),d=n(40),f=n(8425),p=n(7044),h=n(5353);let m=["top","right","bottom","left"],v=Math.min,g=Math.max,y=Math.round,w=Math.floor,b=e=>({x:e,y:e}),x={left:"right",right:"left",bottom:"top",top:"bottom"},E={start:"end",end:"start"};function S(e,t){return"function"==typeof e?e(t):e}function C(e){return e.split("-")[0]}function R(e){return e.split("-")[1]}function M(e){return"x"===e?"y":"x"}function T(e){return"y"===e?"height":"width"}let A=new Set(["top","bottom"]);function P(e){return A.has(C(e))?"y":"x"}function k(e){return e.replace(/start|end/g,e=>E[e])}let N=["left","right"],L=["right","left"],O=["top","bottom"],j=["bottom","top"];function D(e){return e.replace(/left|right|bottom|top/g,e=>x[e])}function W(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function I(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function F(e,t,n){let r,{reference:o,floating:i}=e,l=P(t),a=M(P(t)),u=T(a),c=C(t),s="y"===l,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,p=o[u]/2-i[u]/2;switch(c){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch(R(t)){case"start":r[a]-=p*(n&&s?-1:1);break;case"end":r[a]+=p*(n&&s?-1:1)}return r}let V=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),u=await (null==l.isRTL?void 0:l.isRTL(t)),c=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:d}=F(c,r,u),f=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:i,fn:m}=a[n],{x:v,y:g,data:y,reset:w}=await m({x:s,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:c,platform:l,elements:{reference:e,floating:t}});s=null!=v?v:s,d=null!=g?g:d,p={...p,[i]:{...p[i],...y}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(c=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:d}=F(c,f,u)),n=-1)}return{x:s,y:d,placement:f,strategy:o,middlewareData:p}};async function H(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:l,elements:a,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=S(t,e),h=W(p),m=a[f?"floating"===d?"reference":"floating":d],v=I(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(m)))||n?m:m.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:c,rootBoundary:s,strategy:u})),g="floating"===d?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,y=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),w=await (null==i.isElement?void 0:i.isElement(y))&&await (null==i.getScale?void 0:i.getScale(y))||{x:1,y:1},b=I(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:g,offsetParent:y,strategy:u}):g);return{top:(v.top-b.top+h.top)/w.y,bottom:(b.bottom-v.bottom+h.bottom)/w.y,left:(v.left-b.left+h.left)/w.x,right:(b.right-v.right+h.right)/w.x}}function B(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function _(e){return m.some(t=>e[t]>=0)}let z=new Set(["left","top"]);async function Z(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),l=C(n),a=R(n),u="y"===P(n),c=z.has(l)?-1:1,s=i&&u?-1:1,d=S(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:h}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&"number"==typeof h&&(p="end"===a?-1*h:h),u?{x:p*s,y:f*c}:{x:f*c,y:p*s}}function $(){return"undefined"!=typeof window}function U(e){return Y(e)?(e.nodeName||"").toLowerCase():"#document"}function K(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function X(e){var t;return null==(t=(Y(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function Y(e){return!!$()&&(e instanceof Node||e instanceof K(e).Node)}function q(e){return!!$()&&(e instanceof Element||e instanceof K(e).Element)}function G(e){return!!$()&&(e instanceof HTMLElement||e instanceof K(e).HTMLElement)}function J(e){return!!$()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof K(e).ShadowRoot)}let Q=new Set(["inline","contents"]);function ee(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=ed(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!Q.has(o)}let et=new Set(["table","td","th"]),en=[":popover-open",":modal"];function er(e){return en.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let eo=["transform","translate","scale","rotate","perspective"],ei=["transform","translate","scale","rotate","perspective","filter"],el=["paint","layout","strict","content"];function ea(e){let t=eu(),n=q(e)?ed(e):e;return eo.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||ei.some(e=>(n.willChange||"").includes(e))||el.some(e=>(n.contain||"").includes(e))}function eu(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let ec=new Set(["html","body","#document"]);function es(e){return ec.has(U(e))}function ed(e){return K(e).getComputedStyle(e)}function ef(e){return q(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function ep(e){if("html"===U(e))return e;let t=e.assignedSlot||e.parentNode||J(e)&&e.host||X(e);return J(t)?t.host:t}function eh(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=ep(t);return es(n)?t.ownerDocument?t.ownerDocument.body:t.body:G(n)&&ee(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=K(o);if(i){let e=em(l);return t.concat(l,l.visualViewport||[],ee(o)?o:[],e&&n?eh(e):[])}return t.concat(o,eh(o,[],n))}function em(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ev(e){let t=ed(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=G(e),i=o?e.offsetWidth:n,l=o?e.offsetHeight:r,a=y(n)!==i||y(r)!==l;return a&&(n=i,r=l),{width:n,height:r,$:a}}function eg(e){return q(e)?e:e.contextElement}function ey(e){let t=eg(e);if(!G(t))return b(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=ev(t),l=(i?y(n.width):n.width)/r,a=(i?y(n.height):n.height)/o;return l&&Number.isFinite(l)||(l=1),a&&Number.isFinite(a)||(a=1),{x:l,y:a}}let ew=b(0);function eb(e){let t=K(e);return eu()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:ew}function ex(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),l=eg(e),a=b(1);t&&(r?q(r)&&(a=ey(r)):a=ey(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===K(l))&&o)?eb(l):b(0),c=(i.left+u.x)/a.x,s=(i.top+u.y)/a.y,d=i.width/a.x,f=i.height/a.y;if(l){let e=K(l),t=r&&q(r)?K(r):r,n=e,o=em(n);for(;o&&r&&t!==n;){let e=ey(o),t=o.getBoundingClientRect(),r=ed(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,s*=e.y,d*=e.x,f*=e.y,c+=i,s+=l,o=em(n=K(o))}}return I({width:d,height:f,x:c,y:s})}function eE(e,t){let n=ef(e).scrollLeft;return t?t.left+n:ex(X(e)).left+n}function eS(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:eE(e,r)),y:r.top+t.scrollTop}}let eC=new Set(["absolute","fixed"]);function eR(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=K(e),r=X(e),o=n.visualViewport,i=r.clientWidth,l=r.clientHeight,a=0,u=0;if(o){i=o.width,l=o.height;let e=eu();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,u=o.offsetTop)}return{width:i,height:l,x:a,y:u}}(e,n);else if("document"===t)r=function(e){let t=X(e),n=ef(e),r=e.ownerDocument.body,o=g(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=g(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+eE(e),a=-n.scrollTop;return"rtl"===ed(r).direction&&(l+=g(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:a}}(X(e));else if(q(t))r=function(e,t){let n=ex(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=G(e)?ey(e):b(1),l=e.clientWidth*i.x;return{width:l,height:e.clientHeight*i.y,x:o*i.x,y:r*i.y}}(t,n);else{let n=eb(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return I(r)}function eM(e){return"static"===ed(e).position}function eT(e,t){if(!G(e)||"fixed"===ed(e).position)return null;if(t)return t(e);let n=e.offsetParent;return X(e)===n&&(n=n.ownerDocument.body),n}function eA(e,t){var n;let r=K(e);if(er(e))return r;if(!G(e)){let t=ep(e);for(;t&&!es(t);){if(q(t)&&!eM(t))return t;t=ep(t)}return r}let o=eT(e,t);for(;o&&(n=o,et.has(U(n)))&&eM(o);)o=eT(o,t);return o&&es(o)&&eM(o)&&!ea(o)?r:o||function(e){let t=ep(e);for(;G(t)&&!es(t);){if(ea(t))return t;if(er(t))break;t=ep(t)}return null}(e)||r}let eP=async function(e){let t=this.getOffsetParent||eA,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=G(t),o=X(t),i="fixed"===n,l=ex(e,!0,i,t),a={scrollLeft:0,scrollTop:0},u=b(0);if(r||!r&&!i){if(("body"!==U(t)||ee(o))&&(a=ef(t)),r){let e=ex(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=eE(o))}i&&!r&&o&&(u.x=eE(o));let c=!o||r||i?b(0):eS(o,a);return{x:l.left+a.scrollLeft-u.x-c.x,y:l.top+a.scrollTop-u.y-c.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ek={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,l=X(r),a=!!t&&er(t.floating);if(r===l||a&&i)return n;let u={scrollLeft:0,scrollTop:0},c=b(1),s=b(0),d=G(r);if((d||!d&&!i)&&(("body"!==U(r)||ee(l))&&(u=ef(r)),G(r))){let e=ex(r);c=ey(r),s.x=e.x+r.clientLeft,s.y=e.y+r.clientTop}let f=!l||d||i?b(0):eS(l,u,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-u.scrollLeft*c.x+s.x+f.x,y:n.y*c.y-u.scrollTop*c.y+s.y+f.y}},getDocumentElement:X,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,i=[..."clippingAncestors"===n?er(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=eh(e,[],!1).filter(e=>q(e)&&"body"!==U(e)),o=null,i="fixed"===ed(e).position,l=i?ep(e):e;for(;q(l)&&!es(l);){let t=ed(l),n=ea(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&eC.has(o.position)||ee(l)&&!n&&function e(t,n){let r=ep(t);return!(r===n||!q(r)||es(r))&&("fixed"===ed(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):o=t,l=ep(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],l=i[0],a=i.reduce((e,n)=>{let r=eR(t,n,o);return e.top=g(r.top,e.top),e.right=v(r.right,e.right),e.bottom=v(r.bottom,e.bottom),e.left=g(r.left,e.left),e},eR(t,l,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:eA,getElementRects:eP,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=ev(e);return{width:t,height:n}},getScale:ey,isElement:q,isRTL:function(e){return"rtl"===ed(e).direction}};function eN(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eL=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:i,platform:l,elements:a,middlewareData:u}=t,{element:c,padding:s=0}=S(e,t)||{};if(null==c)return{};let d=W(s),f={x:n,y:r},p=M(P(o)),h=T(p),m=await l.getDimensions(c),y="y"===p,w=y?"clientHeight":"clientWidth",b=i.reference[h]+i.reference[p]-f[p]-i.floating[h],x=f[p]-i.reference[p],E=await (null==l.getOffsetParent?void 0:l.getOffsetParent(c)),C=E?E[w]:0;C&&await (null==l.isElement?void 0:l.isElement(E))||(C=a.floating[w]||i.floating[h]);let A=C/2-m[h]/2-1,k=v(d[y?"top":"left"],A),N=v(d[y?"bottom":"right"],A),L=C-m[h]-N,O=C/2-m[h]/2+(b/2-x/2),j=g(k,v(O,L)),D=!u.arrow&&null!=R(o)&&O!==j&&i.reference[h]/2-(O<k?k:N)-m[h]/2<0,I=D?O<k?O-k:O-L:0;return{[p]:f[p]+I,data:{[p]:j,centerOffset:O-j-I,...D&&{alignmentOffset:I}},reset:D}}}),eO=(e,t,n)=>{let r=new Map,o={platform:ek,...n},i={...o.platform,_c:r};return V(e,t,{...o,platform:i})};var ej="undefined"!=typeof document?r.useLayoutEffect:function(){};function eD(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eD(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!eD(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eW(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eI(e,t){let n=eW(e);return Math.round(t*n)/n}function eF(e){let t=r.useRef(e);return ej(()=>{t.current=e}),t}let eV=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?eL({element:n.current,padding:r}).fn(t):{}:n?eL({element:n,padding:r}).fn(t):{}}}),eH=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:l,middlewareData:a}=t,u=await Z(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:l}}}}}(e),options:[e,t]}),eB=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:l=!1,limiter:a={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...u}=S(e,t),c={x:n,y:r},s=await H(t,u),d=P(C(o)),f=M(d),p=c[f],h=c[d];if(i){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=p+s[e],r=p-s[t];p=g(n,v(p,r))}if(l){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=h+s[e],r=h-s[t];h=g(n,v(h,r))}let m=a.fn({...t,[f]:p,[d]:h});return{...m,data:{x:m.x-n,y:m.y-r,enabled:{[f]:i,[d]:l}}}}}}(e),options:[e,t]}),e_=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:u=!0,crossAxis:c=!0}=S(e,t),s={x:n,y:r},d=P(o),f=M(d),p=s[f],h=s[d],m=S(a,t),v="number"==typeof m?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(u){let e="y"===f?"height":"width",t=i.reference[f]-i.floating[e]+v.mainAxis,n=i.reference[f]+i.reference[e]-v.mainAxis;p<t?p=t:p>n&&(p=n)}if(c){var g,y;let e="y"===f?"width":"height",t=z.has(C(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(g=l.offset)?void 0:g[d])||0)+(t?0:v.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(y=l.offset)?void 0:y[d])||0)-(t?v.crossAxis:0);h<n?h=n:h>r&&(h=r)}return{[f]:p,[d]:h}}}}(e),options:[e,t]}),ez=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,l;let{placement:a,middlewareData:u,rects:c,initialPlacement:s,platform:d,elements:f}=t,{mainAxis:p=!0,crossAxis:h=!0,fallbackPlacements:m,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:y=!0,...w}=S(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let b=C(a),x=P(s),E=C(s)===s,A=await (null==d.isRTL?void 0:d.isRTL(f.floating)),W=m||(E||!y?[D(s)]:function(e){let t=D(e);return[k(e),t,k(t)]}(s)),I="none"!==g;!m&&I&&W.push(...function(e,t,n,r){let o=R(e),i=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?L:N;return t?N:L;case"left":case"right":return t?O:j;default:return[]}}(C(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(k)))),i}(s,y,g,A));let F=[s,...W],V=await H(t,w),B=[],_=(null==(r=u.flip)?void 0:r.overflows)||[];if(p&&B.push(V[b]),h){let e=function(e,t,n){void 0===n&&(n=!1);let r=R(e),o=M(P(e)),i=T(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=D(l)),[l,D(l)]}(a,c,A);B.push(V[e[0]],V[e[1]])}if(_=[..._,{placement:a,overflows:B}],!B.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=F[e];if(t&&(!("alignment"===h&&x!==P(t))||_.every(e=>e.overflows[0]>0&&P(e.placement)===x)))return{data:{index:e,overflows:_},reset:{placement:t}};let n=null==(i=_.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(v){case"bestFit":{let e=null==(l=_.filter(e=>{if(I){let t=P(e.placement);return t===x||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=s}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),eZ=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,i;let{placement:l,rects:a,platform:u,elements:c}=t,{apply:s=()=>{},...d}=S(e,t),f=await H(t,d),p=C(l),h=R(l),m="y"===P(l),{width:y,height:w}=a.floating;"top"===p||"bottom"===p?(o=p,i=h===(await (null==u.isRTL?void 0:u.isRTL(c.floating))?"start":"end")?"left":"right"):(i=p,o="end"===h?"top":"bottom");let b=w-f.top-f.bottom,x=y-f.left-f.right,E=v(w-f[o],b),M=v(y-f[i],x),T=!t.middlewareData.shift,A=E,k=M;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(k=x),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(A=b),T&&!h){let e=g(f.left,0),t=g(f.right,0),n=g(f.top,0),r=g(f.bottom,0);m?k=y-2*(0!==e||0!==t?e+t:g(f.left,f.right)):A=w-2*(0!==n||0!==r?n+r:g(f.top,f.bottom))}await s({...t,availableWidth:k,availableHeight:A});let N=await u.getDimensions(c.floating);return y!==N.width||w!==N.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),e$=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=S(e,t);switch(r){case"referenceHidden":{let e=B(await H(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:_(e)}}}case"escaped":{let e=B(await H(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:_(e)}}}default:return{}}}}}(e),options:[e,t]}),eU=(e,t)=>({...eV(e),options:[e,t]});var eK=n(4582),eX=n(7491),eY=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,eX.jsx)(eK.WV.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eX.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eY.displayName="Arrow";var eq=n(1285),eG=n(5805),eJ="Popper",[eQ,e0]=(0,c.b)(eJ),[e1,e2]=eQ(eJ),e5=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,eX.jsx)(e1,{scope:t,anchor:o,onAnchorChange:i,children:n})};e5.displayName=eJ;var e6="PopperAnchor",e4=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...i}=e,l=e2(e6,n),a=r.useRef(null),c=(0,u.e)(t,a);return r.useEffect(()=>{l.onAnchorChange(o?.current||a.current)}),o?null:(0,eX.jsx)(eK.WV.div,{...i,ref:c})});e4.displayName=e6;var e3="PopperContent",[e8,e9]=eQ(e3),e7=r.forwardRef((e,t)=>{let{__scopePopper:n,side:i="bottom",sideOffset:l=0,align:a="center",alignOffset:c=0,arrowPadding:s=0,avoidCollisions:d=!0,collisionBoundary:f=[],collisionPadding:p=0,sticky:h="partial",hideWhenDetached:m=!1,updatePositionStrategy:y="optimized",onPlaced:b,...x}=e,E=e2(e3,n),[S,C]=r.useState(null),R=(0,u.e)(t,e=>C(e)),[M,T]=r.useState(null),A=function(e){let[t,n]=r.useState(void 0);return(0,eG.b)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(M),P=A?.width??0,k=A?.height??0,N="number"==typeof p?p:{top:0,right:0,bottom:0,left:0,...p},L=Array.isArray(f)?f:[f],O=L.length>0,j={padding:N,boundary:L.filter(tr),altBoundary:O},{refs:D,floatingStyles:W,placement:I,isPositioned:F,middlewareData:V}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:i=[],platform:l,elements:{reference:a,floating:u}={},transform:c=!0,whileElementsMounted:s,open:d}=e,[f,p]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[h,m]=r.useState(i);eD(h,i)||m(i);let[v,g]=r.useState(null),[y,w]=r.useState(null),b=r.useCallback(e=>{e!==C.current&&(C.current=e,g(e))},[]),x=r.useCallback(e=>{e!==R.current&&(R.current=e,w(e))},[]),E=a||v,S=u||y,C=r.useRef(null),R=r.useRef(null),M=r.useRef(f),T=null!=s,A=eF(s),P=eF(l),k=eF(d),N=r.useCallback(()=>{if(!C.current||!R.current)return;let e={placement:t,strategy:n,middleware:h};P.current&&(e.platform=P.current),eO(C.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==k.current};L.current&&!eD(M.current,t)&&(M.current=t,o.flushSync(()=>{p(t)}))})},[h,t,n,P,k]);ej(()=>{!1===d&&M.current.isPositioned&&(M.current.isPositioned=!1,p(e=>({...e,isPositioned:!1})))},[d]);let L=r.useRef(!1);ej(()=>(L.current=!0,()=>{L.current=!1}),[]),ej(()=>{if(E&&(C.current=E),S&&(R.current=S),E&&S){if(A.current)return A.current(E,S,N);N()}},[E,S,N,A,T]);let O=r.useMemo(()=>({reference:C,floating:R,setReference:b,setFloating:x}),[b,x]),j=r.useMemo(()=>({reference:E,floating:S}),[E,S]),D=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!j.floating)return e;let t=eI(j.floating,f.x),r=eI(j.floating,f.y);return c?{...e,transform:"translate("+t+"px, "+r+"px)",...eW(j.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,c,j.floating,f.x,f.y]);return r.useMemo(()=>({...f,update:N,refs:O,elements:j,floatingStyles:D}),[f,N,O,j,D])}({strategy:"fixed",placement:i+("center"!==a?"-"+a:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:i=!0,ancestorResize:l=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:u="function"==typeof IntersectionObserver,animationFrame:c=!1}=r,s=eg(e),d=i||l?[...s?eh(s):[],...eh(t)]:[];d.forEach(e=>{i&&e.addEventListener("scroll",n,{passive:!0}),l&&e.addEventListener("resize",n)});let f=s&&u?function(e,t){let n,r=null,o=X(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return function l(a,u){void 0===a&&(a=!1),void 0===u&&(u=1),i();let c=e.getBoundingClientRect(),{left:s,top:d,width:f,height:p}=c;if(a||t(),!f||!p)return;let h=w(d),m=w(o.clientWidth-(s+f)),y={rootMargin:-h+"px "+-m+"px "+-w(o.clientHeight-(d+p))+"px "+-w(s)+"px",threshold:g(0,v(1,u))||1},b=!0;function x(t){let r=t[0].intersectionRatio;if(r!==u){if(!b)return l();r?l(!1,r):n=setTimeout(()=>{l(!1,1e-7)},1e3)}1!==r||eN(c,e.getBoundingClientRect())||l(),b=!1}try{r=new IntersectionObserver(x,{...y,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(x,y)}r.observe(e)}(!0),i}(s,n):null,p=-1,h=null;a&&(h=new ResizeObserver(e=>{let[r]=e;r&&r.target===s&&h&&(h.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=h)||e.observe(t)})),n()}),s&&!c&&h.observe(s),h.observe(t));let m=c?ex(e):null;return c&&function t(){let r=ex(e);m&&!eN(m,r)&&n(),m=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;d.forEach(e=>{i&&e.removeEventListener("scroll",n),l&&e.removeEventListener("resize",n)}),null==f||f(),null==(e=h)||e.disconnect(),h=null,c&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===y}),elements:{reference:E.anchor},middleware:[eH({mainAxis:l+k,alignmentAxis:c}),d&&eB({mainAxis:!0,crossAxis:!1,limiter:"partial"===h?e_():void 0,...j}),d&&ez({...j}),eZ({...j,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:o,height:i}=t.reference,l=e.floating.style;l.setProperty("--radix-popper-available-width",`${n}px`),l.setProperty("--radix-popper-available-height",`${r}px`),l.setProperty("--radix-popper-anchor-width",`${o}px`),l.setProperty("--radix-popper-anchor-height",`${i}px`)}}),M&&eU({element:M,padding:s}),to({arrowWidth:P,arrowHeight:k}),m&&e$({strategy:"referenceHidden",...j})]}),[H,B]=ti(I),_=(0,eq.W)(b);(0,eG.b)(()=>{F&&_?.()},[F,_]);let z=V.arrow?.x,Z=V.arrow?.y,$=V.arrow?.centerOffset!==0,[U,K]=r.useState();return(0,eG.b)(()=>{S&&K(window.getComputedStyle(S).zIndex)},[S]),(0,eX.jsx)("div",{ref:D.setFloating,"data-radix-popper-content-wrapper":"",style:{...W,transform:F?W.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:U,"--radix-popper-transform-origin":[V.transformOrigin?.x,V.transformOrigin?.y].join(" "),...V.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eX.jsx)(e8,{scope:n,placedSide:H,onArrowChange:T,arrowX:z,arrowY:Z,shouldHideArrow:$,children:(0,eX.jsx)(eK.WV.div,{"data-side":H,"data-align":B,...x,ref:R,style:{...x.style,animation:F?void 0:"none"}})})})});e7.displayName=e3;var te="PopperArrow",tt={top:"bottom",right:"left",bottom:"top",left:"right"},tn=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=e9(te,n),i=tt[o.placedSide];return(0,eX.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eX.jsx)(eY,{...r,ref:t,style:{...r.style,display:"block"}})})});function tr(e){return null!==e}tn.displayName=te;var to=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,l=i?0:e.arrowWidth,a=i?0:e.arrowHeight,[u,c]=ti(n),s={start:"0%",center:"50%",end:"100%"}[c],d=(o.arrow?.x??0)+l/2,f=(o.arrow?.y??0)+a/2,p="",h="";return"bottom"===u?(p=i?s:`${d}px`,h=`${-a}px`):"top"===u?(p=i?s:`${d}px`,h=`${r.floating.height+a}px`):"right"===u?(p=`${-a}px`,h=i?s:`${f}px`):"left"===u&&(p=`${r.floating.width+a}px`,h=i?s:`${f}px`),{data:{x:p,y:h}}}});function ti(e){let[t,n="center"]=e.split("-");return[t,n]}var tl=n(1288),ta=n(7723),tu=n(3485),tc=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});r.forwardRef((e,t)=>(0,eX.jsx)(eK.WV.span,{...e,ref:t,style:{...tc,...e.style}})).displayName="VisuallyHidden";var ts=n(9128),td=n(3205),tf=[" ","Enter","ArrowUp","ArrowDown"],tp=[" ","Enter"],th="Select",[tm,tv,tg]=(0,a.B)(th),[ty,tw]=(0,c.b)(th,[tg,e0]),tb=e0(),[tx,tE]=ty(th),[tS,tC]=ty(th),tR=e=>{let{__scopeSelect:t,children:n,open:o,defaultOpen:i,onOpenChange:l,value:a,defaultValue:u,onValueChange:c,dir:d,name:f,autoComplete:p,disabled:m,required:v,form:g}=e,y=tb(t),[w,b]=r.useState(null),[x,E]=r.useState(null),[S,C]=r.useState(!1),R=(0,s.gm)(d),[M,T]=(0,tu.T)({prop:o,defaultProp:i??!1,onChange:l,caller:th}),[A,P]=(0,tu.T)({prop:a,defaultProp:u,onChange:c,caller:th}),k=r.useRef(null),N=!w||g||!!w.closest("form"),[L,O]=r.useState(new Set),j=Array.from(L).map(e=>e.props.value).join(";");return(0,eX.jsx)(e5,{...y,children:(0,eX.jsxs)(tx,{required:v,scope:t,trigger:w,onTriggerChange:b,valueNode:x,onValueNodeChange:E,valueNodeHasChildren:S,onValueNodeHasChildrenChange:C,contentId:(0,h.M)(),value:A,onValueChange:P,open:M,onOpenChange:T,dir:R,triggerPointerDownPosRef:k,disabled:m,children:[(0,eX.jsx)(tm.Provider,{scope:t,children:(0,eX.jsx)(tS,{scope:e.__scopeSelect,onNativeOptionAdd:r.useCallback(e=>{O(t=>new Set(t).add(e))},[]),onNativeOptionRemove:r.useCallback(e=>{O(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),N?(0,eX.jsxs)(nt,{"aria-hidden":!0,required:v,tabIndex:-1,name:f,autoComplete:p,value:A,onChange:e=>P(e.target.value),disabled:m,form:g,children:[void 0===A?(0,eX.jsx)("option",{value:""}):null,Array.from(L)]},j):null]})})};tR.displayName=th;var tM="SelectTrigger",tT=r.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:o=!1,...i}=e,a=tb(n),c=tE(tM,n),s=c.disabled||o,d=(0,u.e)(t,c.onTriggerChange),f=tv(n),p=r.useRef("touch"),[h,m,v]=nr(e=>{let t=f().filter(e=>!e.disabled),n=t.find(e=>e.value===c.value),r=no(t,e,n);void 0!==r&&c.onValueChange(r.value)}),g=e=>{s||(c.onOpenChange(!0),v()),e&&(c.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,eX.jsx)(e4,{asChild:!0,...a,children:(0,eX.jsx)(eK.WV.button,{type:"button",role:"combobox","aria-controls":c.contentId,"aria-expanded":c.open,"aria-required":c.required,"aria-autocomplete":"none",dir:c.dir,"data-state":c.open?"open":"closed",disabled:s,"data-disabled":s?"":void 0,"data-placeholder":nn(c.value)?"":void 0,...i,ref:d,onClick:(0,l.M)(i.onClick,e=>{e.currentTarget.focus(),"mouse"!==p.current&&g(e)}),onPointerDown:(0,l.M)(i.onPointerDown,e=>{p.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(g(e),e.preventDefault())}),onKeyDown:(0,l.M)(i.onKeyDown,e=>{let t=""!==h.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&tf.includes(e.key)&&(g(),e.preventDefault())})})})});tT.displayName=tM;var tA="SelectValue",tP=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:i,placeholder:l="",...a}=e,c=tE(tA,n),{onValueNodeHasChildrenChange:s}=c,d=void 0!==i,f=(0,u.e)(t,c.onValueNodeChange);return(0,eG.b)(()=>{s(d)},[s,d]),(0,eX.jsx)(eK.WV.span,{...a,ref:f,style:{pointerEvents:"none"},children:nn(c.value)?(0,eX.jsx)(eX.Fragment,{children:l}):i})});tP.displayName=tA;var tk=r.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,eX.jsx)(eK.WV.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});tk.displayName="SelectIcon";var tN=e=>(0,eX.jsx)(tl.h,{asChild:!0,...e});tN.displayName="SelectPortal";var tL="SelectContent",tO=r.forwardRef((e,t)=>{let n=tE(tL,e.__scopeSelect),[i,l]=r.useState();return((0,eG.b)(()=>{l(new DocumentFragment)},[]),n.open)?(0,eX.jsx)(tI,{...e,ref:t}):i?o.createPortal((0,eX.jsx)(tj,{scope:e.__scopeSelect,children:(0,eX.jsx)(tm.Slot,{scope:e.__scopeSelect,children:(0,eX.jsx)("div",{children:e.children})})}),i):null});tO.displayName=tL;var[tj,tD]=ty(tL),tW=(0,ta.Z8)("SelectContent.RemoveScroll"),tI=r.forwardRef((e,t)=>{let{__scopeSelect:n,position:o="item-aligned",onCloseAutoFocus:i,onEscapeKeyDown:a,onPointerDownOutside:c,side:s,sideOffset:h,align:m,alignOffset:v,arrowPadding:g,collisionBoundary:y,collisionPadding:w,sticky:b,hideWhenDetached:x,avoidCollisions:E,...S}=e,C=tE(tL,n),[R,M]=r.useState(null),[T,A]=r.useState(null),P=(0,u.e)(t,e=>M(e)),[k,N]=r.useState(null),[L,O]=r.useState(null),j=tv(n),[D,W]=r.useState(!1),I=r.useRef(!1);r.useEffect(()=>{if(R)return(0,ts.Ry)(R)},[R]),(0,f.EW)();let F=r.useCallback(e=>{let[t,...n]=j().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(n?.scrollIntoView({block:"nearest"}),n===t&&T&&(T.scrollTop=0),n===r&&T&&(T.scrollTop=T.scrollHeight),n?.focus(),document.activeElement!==o))return},[j,T]),V=r.useCallback(()=>F([k,R]),[F,k,R]);r.useEffect(()=>{D&&V()},[D,V]);let{onOpenChange:H,triggerPointerDownPosRef:B}=C;r.useEffect(()=>{if(R){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(B.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(B.current?.y??0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():R.contains(n.target)||H(!1),document.removeEventListener("pointermove",t),B.current=null};return null!==B.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[R,H,B]),r.useEffect(()=>{let e=()=>H(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[H]);let[_,z]=nr(e=>{let t=j().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=no(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),Z=r.useCallback((e,t,n)=>{let r=!I.current&&!n;(void 0!==C.value&&C.value===t||r)&&(N(e),r&&(I.current=!0))},[C.value]),$=r.useCallback(()=>R?.focus(),[R]),U=r.useCallback((e,t,n)=>{let r=!I.current&&!n;(void 0!==C.value&&C.value===t||r)&&O(e)},[C.value]),K="popper"===o?tV:tF,X=K===tV?{side:s,sideOffset:h,align:m,alignOffset:v,arrowPadding:g,collisionBoundary:y,collisionPadding:w,sticky:b,hideWhenDetached:x,avoidCollisions:E}:{};return(0,eX.jsx)(tj,{scope:n,content:R,viewport:T,onViewportChange:A,itemRefCallback:Z,selectedItem:k,onItemLeave:$,itemTextRefCallback:U,focusSelectedItem:V,selectedItemText:L,position:o,isPositioned:D,searchRef:_,children:(0,eX.jsx)(td.Z,{as:tW,allowPinchZoom:!0,children:(0,eX.jsx)(p.M,{asChild:!0,trapped:C.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,l.M)(i,e=>{C.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,eX.jsx)(d.XB,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:c,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>C.onOpenChange(!1),children:(0,eX.jsx)(K,{role:"listbox",id:C.contentId,"data-state":C.open?"open":"closed",dir:C.dir,onContextMenu:e=>e.preventDefault(),...S,...X,onPlaced:()=>W(!0),ref:P,style:{display:"flex",flexDirection:"column",outline:"none",...S.style},onKeyDown:(0,l.M)(S.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||z(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=j().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>F(t)),e.preventDefault()}})})})})})})});tI.displayName="SelectContentImpl";var tF=r.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:o,...l}=e,a=tE(tL,n),c=tD(tL,n),[s,d]=r.useState(null),[f,p]=r.useState(null),h=(0,u.e)(t,e=>p(e)),m=tv(n),v=r.useRef(!1),g=r.useRef(!0),{viewport:y,selectedItem:w,selectedItemText:b,focusSelectedItem:x}=c,E=r.useCallback(()=>{if(a.trigger&&a.valueNode&&s&&f&&y&&w&&b){let e=a.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),n=a.valueNode.getBoundingClientRect(),r=b.getBoundingClientRect();if("rtl"!==a.dir){let o=r.left-t.left,l=n.left-o,a=e.left-l,u=e.width+a,c=Math.max(u,t.width),d=i(l,[10,Math.max(10,window.innerWidth-10-c)]);s.style.minWidth=u+"px",s.style.left=d+"px"}else{let o=t.right-r.right,l=window.innerWidth-n.right-o,a=window.innerWidth-e.right-l,u=e.width+a,c=Math.max(u,t.width),d=i(l,[10,Math.max(10,window.innerWidth-10-c)]);s.style.minWidth=u+"px",s.style.right=d+"px"}let l=m(),u=window.innerHeight-20,c=y.scrollHeight,d=window.getComputedStyle(f),p=parseInt(d.borderTopWidth,10),h=parseInt(d.paddingTop,10),g=parseInt(d.borderBottomWidth,10),x=p+h+c+parseInt(d.paddingBottom,10)+g,E=Math.min(5*w.offsetHeight,x),S=window.getComputedStyle(y),C=parseInt(S.paddingTop,10),R=parseInt(S.paddingBottom,10),M=e.top+e.height/2-10,T=w.offsetHeight/2,A=p+h+(w.offsetTop+T);if(A<=M){let e=l.length>0&&w===l[l.length-1].ref.current;s.style.bottom="0px";let t=f.clientHeight-y.offsetTop-y.offsetHeight;s.style.height=A+Math.max(u-M,T+(e?R:0)+t+g)+"px"}else{let e=l.length>0&&w===l[0].ref.current;s.style.top="0px";let t=Math.max(M,p+y.offsetTop+(e?C:0)+T);s.style.height=t+(x-A)+"px",y.scrollTop=A-M+y.offsetTop}s.style.margin="10px 0",s.style.minHeight=E+"px",s.style.maxHeight=u+"px",o?.(),requestAnimationFrame(()=>v.current=!0)}},[m,a.trigger,a.valueNode,s,f,y,w,b,a.dir,o]);(0,eG.b)(()=>E(),[E]);let[S,C]=r.useState();(0,eG.b)(()=>{f&&C(window.getComputedStyle(f).zIndex)},[f]);let R=r.useCallback(e=>{e&&!0===g.current&&(E(),x?.(),g.current=!1)},[E,x]);return(0,eX.jsx)(tH,{scope:n,contentWrapper:s,shouldExpandOnScrollRef:v,onScrollButtonChange:R,children:(0,eX.jsx)("div",{ref:d,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:S},children:(0,eX.jsx)(eK.WV.div,{...l,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...l.style}})})})});tF.displayName="SelectItemAlignedPosition";var tV=r.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...i}=e,l=tb(n);return(0,eX.jsx)(e7,{...l,...i,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});tV.displayName="SelectPopperPosition";var[tH,tB]=ty(tL,{}),t_="SelectViewport",tz=r.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:o,...i}=e,a=tD(t_,n),c=tB(t_,n),s=(0,u.e)(t,a.onViewportChange),d=r.useRef(0);return(0,eX.jsxs)(eX.Fragment,{children:[(0,eX.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,eX.jsx)(tm.Slot,{scope:n,children:(0,eX.jsx)(eK.WV.div,{"data-radix-select-viewport":"",role:"presentation",...i,ref:s,style:{position:"relative",flex:1,overflow:"hidden auto",...i.style},onScroll:(0,l.M)(i.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=c;if(r?.current&&n){let e=Math.abs(d.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let i=o+e,l=Math.min(r,i),a=i-l;n.style.height=l+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}d.current=t.scrollTop})})})]})});tz.displayName=t_;var tZ="SelectGroup",[t$,tU]=ty(tZ),tK=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=(0,h.M)();return(0,eX.jsx)(t$,{scope:n,id:o,children:(0,eX.jsx)(eK.WV.div,{role:"group","aria-labelledby":o,...r,ref:t})})});tK.displayName=tZ;var tX="SelectLabel",tY=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=tU(tX,n);return(0,eX.jsx)(eK.WV.div,{id:o.id,...r,ref:t})});tY.displayName=tX;var tq="SelectItem",[tG,tJ]=ty(tq),tQ=r.forwardRef((e,t)=>{let{__scopeSelect:n,value:o,disabled:i=!1,textValue:a,...c}=e,s=tE(tq,n),d=tD(tq,n),f=s.value===o,[p,m]=r.useState(a??""),[v,g]=r.useState(!1),y=(0,u.e)(t,e=>d.itemRefCallback?.(e,o,i)),w=(0,h.M)(),b=r.useRef("touch"),x=()=>{i||(s.onValueChange(o),s.onOpenChange(!1))};if(""===o)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,eX.jsx)(tG,{scope:n,value:o,disabled:i,textId:w,isSelected:f,onItemTextChange:r.useCallback(e=>{m(t=>t||(e?.textContent??"").trim())},[]),children:(0,eX.jsx)(tm.ItemSlot,{scope:n,value:o,disabled:i,textValue:p,children:(0,eX.jsx)(eK.WV.div,{role:"option","aria-labelledby":w,"data-highlighted":v?"":void 0,"aria-selected":f&&v,"data-state":f?"checked":"unchecked","aria-disabled":i||void 0,"data-disabled":i?"":void 0,tabIndex:i?void 0:-1,...c,ref:y,onFocus:(0,l.M)(c.onFocus,()=>g(!0)),onBlur:(0,l.M)(c.onBlur,()=>g(!1)),onClick:(0,l.M)(c.onClick,()=>{"mouse"!==b.current&&x()}),onPointerUp:(0,l.M)(c.onPointerUp,()=>{"mouse"===b.current&&x()}),onPointerDown:(0,l.M)(c.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,l.M)(c.onPointerMove,e=>{b.current=e.pointerType,i?d.onItemLeave?.():"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,l.M)(c.onPointerLeave,e=>{e.currentTarget===document.activeElement&&d.onItemLeave?.()}),onKeyDown:(0,l.M)(c.onKeyDown,e=>{d.searchRef?.current!==""&&" "===e.key||(tp.includes(e.key)&&x()," "===e.key&&e.preventDefault())})})})})});tQ.displayName=tq;var t0="SelectItemText",t1=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:i,style:l,...a}=e,c=tE(t0,n),s=tD(t0,n),d=tJ(t0,n),f=tC(t0,n),[p,h]=r.useState(null),m=(0,u.e)(t,e=>h(e),d.onItemTextChange,e=>s.itemTextRefCallback?.(e,d.value,d.disabled)),v=p?.textContent,g=r.useMemo(()=>(0,eX.jsx)("option",{value:d.value,disabled:d.disabled,children:v},d.value),[d.disabled,d.value,v]),{onNativeOptionAdd:y,onNativeOptionRemove:w}=f;return(0,eG.b)(()=>(y(g),()=>w(g)),[y,w,g]),(0,eX.jsxs)(eX.Fragment,{children:[(0,eX.jsx)(eK.WV.span,{id:d.textId,...a,ref:m}),d.isSelected&&c.valueNode&&!c.valueNodeHasChildren?o.createPortal(a.children,c.valueNode):null]})});t1.displayName=t0;var t2="SelectItemIndicator",t5=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return tJ(t2,n).isSelected?(0,eX.jsx)(eK.WV.span,{"aria-hidden":!0,...r,ref:t}):null});t5.displayName=t2;var t6="SelectScrollUpButton",t4=r.forwardRef((e,t)=>{let n=tD(t6,e.__scopeSelect),o=tB(t6,e.__scopeSelect),[i,l]=r.useState(!1),a=(0,u.e)(t,o.onScrollButtonChange);return(0,eG.b)(()=>{if(n.viewport&&n.isPositioned){let e=function(){l(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,eX.jsx)(t9,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});t4.displayName=t6;var t3="SelectScrollDownButton",t8=r.forwardRef((e,t)=>{let n=tD(t3,e.__scopeSelect),o=tB(t3,e.__scopeSelect),[i,l]=r.useState(!1),a=(0,u.e)(t,o.onScrollButtonChange);return(0,eG.b)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;l(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,eX.jsx)(t9,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});t8.displayName=t3;var t9=r.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:o,...i}=e,a=tD("SelectScrollButton",n),u=r.useRef(null),c=tv(n),s=r.useCallback(()=>{null!==u.current&&(window.clearInterval(u.current),u.current=null)},[]);return r.useEffect(()=>()=>s(),[s]),(0,eG.b)(()=>{let e=c().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[c]),(0,eX.jsx)(eK.WV.div,{"aria-hidden":!0,...i,ref:t,style:{flexShrink:0,...i.style},onPointerDown:(0,l.M)(i.onPointerDown,()=>{null===u.current&&(u.current=window.setInterval(o,50))}),onPointerMove:(0,l.M)(i.onPointerMove,()=>{a.onItemLeave?.(),null===u.current&&(u.current=window.setInterval(o,50))}),onPointerLeave:(0,l.M)(i.onPointerLeave,()=>{s()})})}),t7=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,eX.jsx)(eK.WV.div,{"aria-hidden":!0,...r,ref:t})});t7.displayName="SelectSeparator";var ne="SelectArrow";r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=tb(n),i=tE(ne,n),l=tD(ne,n);return i.open&&"popper"===l.position?(0,eX.jsx)(tn,{...o,...r,ref:t}):null}).displayName=ne;var nt=r.forwardRef(({__scopeSelect:e,value:t,...n},o)=>{let i=r.useRef(null),l=(0,u.e)(o,i),a=function(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(t);return r.useEffect(()=>{let e=i.current;if(!e)return;let n=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(a!==t&&n){let r=new Event("change",{bubbles:!0});n.call(e,t),e.dispatchEvent(r)}},[a,t]),(0,eX.jsx)(eK.WV.select,{...n,style:{...tc,...n.style},ref:l,defaultValue:t})});function nn(e){return""===e||void 0===e}function nr(e){let t=(0,eq.W)(e),n=r.useRef(""),o=r.useRef(0),i=r.useCallback(e=>{let r=n.current+e;t(r),function e(t){n.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout(()=>e(""),1e3))}(r)},[t]),l=r.useCallback(()=>{n.current="",window.clearTimeout(o.current)},[]);return r.useEffect(()=>()=>window.clearTimeout(o.current),[]),[n,i,l]}function no(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=(r=Math.max(n?e.indexOf(n):-1,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(i=i.filter(e=>e!==n));let l=i.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}nt.displayName="SelectBubbleInput";var ni=tR,nl=tT,na=tP,nu=tk,nc=tN,ns=tO,nd=tz,nf=tK,np=tY,nh=tQ,nm=t1,nv=t5,ng=t4,ny=t8,nw=t7},1285:(e,t,n)=>{n.d(t,{W:()=>o});var r=n(6364);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},3485:(e,t,n)=>{n.d(t,{T:()=>a});var r,o=n(6364),i=n(5805),l=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||i.b;function a({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[i,a,u]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),i=o.useRef(n),a=o.useRef(t);return l(()=>{a.current=t},[t]),o.useEffect(()=>{i.current!==n&&(a.current?.(n),i.current=n)},[n,i]),[n,r,a]}({defaultProp:t,onChange:n}),c=void 0!==e,s=c?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,r])}return[s,o.useCallback(t=>{if(c){let n="function"==typeof t?t(e):t;n!==e&&u.current?.(n)}else a(t)},[c,e,a,u])]}Symbol("RADIX:SYNC_STATE")},5805:(e,t,n)=>{n.d(t,{b:()=>o});var r=n(6364),o=globalThis?.document?r.useLayoutEffect:()=>{}}};