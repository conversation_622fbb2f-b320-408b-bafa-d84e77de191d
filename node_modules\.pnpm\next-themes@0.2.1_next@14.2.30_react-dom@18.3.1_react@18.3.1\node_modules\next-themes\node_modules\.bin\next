#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/augment-projects/agent_framework/node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/bin/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/agent_framework/node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/agent_framework/node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/agent_framework/node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/agent_framework/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/augment-projects/agent_framework/node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/bin/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/agent_framework/node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/agent_framework/node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/agent_framework/node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/agent_framework/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/bin/next" "$@"
else
  exec node  "$basedir/../../../../../next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/bin/next" "$@"
fi
