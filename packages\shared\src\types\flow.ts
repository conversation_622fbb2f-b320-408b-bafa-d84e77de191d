import { z } from 'zod';
import { BaseEntitySchema, FlowStatus, ExecutionStatus } from './common';
import { NodeSchema, FlowConnectionSchema, NodeExecutionResultSchema } from './node';

// Flow schema
export const FlowSchema = BaseEntitySchema.extend({
  name: z.string(),
  description: z.string().optional(),
  status: z.nativeEnum(FlowStatus),
  nodes: z.array(NodeSchema),
  connections: z.array(FlowConnectionSchema),
  userId: z.string().uuid(),
  workspaceId: z.string().uuid().optional(),
  isPublic: z.boolean().default(false),
  tags: z.array(z.string()).default([]),
  version: z.string().default('1.0.0'),
  metadata: z.record(z.any()).optional()
});

export type Flow = z.infer<typeof FlowSchema>;

// Flow execution schema
export const FlowExecutionSchema = BaseEntitySchema.extend({
  flowId: z.string().uuid(),
  status: z.nativeEnum(ExecutionStatus),
  input: z.any().optional(),
  output: z.any().optional(),
  error: z.string().optional(),
  startTime: z.string().datetime(),
  endTime: z.string().datetime().optional(),
  executionTime: z.number().optional(),
  nodeResults: z.array(NodeExecutionResultSchema),
  userId: z.string().uuid().optional(),
  sessionId: z.string().optional(),
  metadata: z.record(z.any()).optional()
});

export type FlowExecution = z.infer<typeof FlowExecutionSchema>;

// Flow template schema
export const FlowTemplateSchema = z.object({
  name: z.string(),
  description: z.string(),
  category: z.string(),
  tags: z.array(z.string()),
  nodes: z.array(NodeSchema),
  connections: z.array(FlowConnectionSchema),
  thumbnail: z.string().optional(),
  isOfficial: z.boolean().default(false),
  difficulty: z.enum(['beginner', 'intermediate', 'advanced']).default('beginner')
});

export type FlowTemplate = z.infer<typeof FlowTemplateSchema>;

// Flow validation result
export const FlowValidationResultSchema = z.object({
  isValid: z.boolean(),
  errors: z.array(z.object({
    nodeId: z.string().optional(),
    connectionId: z.string().optional(),
    message: z.string(),
    type: z.enum(['error', 'warning'])
  }))
});

export type FlowValidationResult = z.infer<typeof FlowValidationResultSchema>;
