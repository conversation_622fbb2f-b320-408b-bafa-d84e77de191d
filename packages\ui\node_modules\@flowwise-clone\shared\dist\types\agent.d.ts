import { z } from 'zod';
import { ExecutionStatus } from './common';
export declare enum AgentType {
    CONVERSATIONAL = "conversational",
    REACT = "react",
    PLAN_AND_EXECUTE = "planAndExecute",
    OPENAI_FUNCTIONS = "openAiFunctions",
    STRUCTURED_CHAT = "structuredChat",
    CUSTOM = "custom"
}
export declare const AgentSchema: z.ZodObject<{
    id: z.ZodString;
    createdAt: z.ZodString;
    updatedAt: z.ZodString;
} & {
    name: z.ZodString;
    description: z.ZodOptional<z.ZodString>;
    type: z.ZodNativeEnum<typeof AgentType>;
    llmId: z.ZodString;
    tools: z.Zod<PERSON>rray<z.ZodString, "many">;
    memory: z.ZodOptional<z.ZodString>;
    systemMessage: z.ZodOptional<z.ZodString>;
    maxIterations: z.ZodDefault<z.ZodNumber>;
    temperature: z.ZodDefault<z.ZodNumber>;
    topP: z.ZodDefault<z.ZodNumber>;
    frequencyPenalty: z.ZodDefault<z.ZodNumber>;
    presencePenalty: z.ZodDefault<z.ZodNumber>;
    stopSequences: z.ZodDefault<z.ZodArray<z.ZodString, "many">>;
    userId: z.ZodString;
    workspaceId: z.ZodOptional<z.ZodString>;
    isActive: z.ZodDefault<z.ZodBoolean>;
    metadata: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
}, "strip", z.ZodTypeAny, {
    type: AgentType;
    id: string;
    createdAt: string;
    updatedAt: string;
    name: string;
    userId: string;
    isActive: boolean;
    llmId: string;
    tools: string[];
    maxIterations: number;
    temperature: number;
    topP: number;
    frequencyPenalty: number;
    presencePenalty: number;
    stopSequences: string[];
    memory?: string | undefined;
    description?: string | undefined;
    workspaceId?: string | undefined;
    metadata?: Record<string, any> | undefined;
    systemMessage?: string | undefined;
}, {
    type: AgentType;
    id: string;
    createdAt: string;
    updatedAt: string;
    name: string;
    userId: string;
    llmId: string;
    tools: string[];
    memory?: string | undefined;
    description?: string | undefined;
    workspaceId?: string | undefined;
    metadata?: Record<string, any> | undefined;
    isActive?: boolean | undefined;
    systemMessage?: string | undefined;
    maxIterations?: number | undefined;
    temperature?: number | undefined;
    topP?: number | undefined;
    frequencyPenalty?: number | undefined;
    presencePenalty?: number | undefined;
    stopSequences?: string[] | undefined;
}>;
export type Agent = z.infer<typeof AgentSchema>;
export declare const MultiAgentSystemSchema: z.ZodObject<{
    id: z.ZodString;
    createdAt: z.ZodString;
    updatedAt: z.ZodString;
} & {
    name: z.ZodString;
    description: z.ZodOptional<z.ZodString>;
    agents: z.ZodArray<z.ZodString, "many">;
    orchestrator: z.ZodOptional<z.ZodString>;
    workflow: z.ZodRecord<z.ZodString, z.ZodAny>;
    userId: z.ZodString;
    workspaceId: z.ZodOptional<z.ZodString>;
    isActive: z.ZodDefault<z.ZodBoolean>;
    metadata: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: string;
    updatedAt: string;
    name: string;
    userId: string;
    isActive: boolean;
    agents: string[];
    workflow: Record<string, any>;
    description?: string | undefined;
    workspaceId?: string | undefined;
    metadata?: Record<string, any> | undefined;
    orchestrator?: string | undefined;
}, {
    id: string;
    createdAt: string;
    updatedAt: string;
    name: string;
    userId: string;
    agents: string[];
    workflow: Record<string, any>;
    description?: string | undefined;
    workspaceId?: string | undefined;
    metadata?: Record<string, any> | undefined;
    isActive?: boolean | undefined;
    orchestrator?: string | undefined;
}>;
export type MultiAgentSystem = z.infer<typeof MultiAgentSystemSchema>;
export declare const AgentExecutionSchema: z.ZodObject<{
    id: z.ZodString;
    createdAt: z.ZodString;
    updatedAt: z.ZodString;
} & {
    agentId: z.ZodString;
    systemId: z.ZodOptional<z.ZodString>;
    status: z.ZodNativeEnum<typeof ExecutionStatus>;
    input: z.ZodAny;
    output: z.ZodOptional<z.ZodAny>;
    error: z.ZodOptional<z.ZodString>;
    steps: z.ZodArray<z.ZodObject<{
        step: z.ZodNumber;
        action: z.ZodString;
        observation: z.ZodOptional<z.ZodString>;
        thought: z.ZodOptional<z.ZodString>;
        timestamp: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        timestamp: string;
        step: number;
        action: string;
        observation?: string | undefined;
        thought?: string | undefined;
    }, {
        timestamp: string;
        step: number;
        action: string;
        observation?: string | undefined;
        thought?: string | undefined;
    }>, "many">;
    startTime: z.ZodString;
    endTime: z.ZodOptional<z.ZodString>;
    executionTime: z.ZodOptional<z.ZodNumber>;
    userId: z.ZodOptional<z.ZodString>;
    sessionId: z.ZodOptional<z.ZodString>;
    metadata: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
}, "strip", z.ZodTypeAny, {
    status: ExecutionStatus;
    id: string;
    createdAt: string;
    updatedAt: string;
    startTime: string;
    agentId: string;
    steps: {
        timestamp: string;
        step: number;
        action: string;
        observation?: string | undefined;
        thought?: string | undefined;
    }[];
    input?: any;
    output?: any;
    error?: string | undefined;
    executionTime?: number | undefined;
    userId?: string | undefined;
    metadata?: Record<string, any> | undefined;
    endTime?: string | undefined;
    sessionId?: string | undefined;
    systemId?: string | undefined;
}, {
    status: ExecutionStatus;
    id: string;
    createdAt: string;
    updatedAt: string;
    startTime: string;
    agentId: string;
    steps: {
        timestamp: string;
        step: number;
        action: string;
        observation?: string | undefined;
        thought?: string | undefined;
    }[];
    input?: any;
    output?: any;
    error?: string | undefined;
    executionTime?: number | undefined;
    userId?: string | undefined;
    metadata?: Record<string, any> | undefined;
    endTime?: string | undefined;
    sessionId?: string | undefined;
    systemId?: string | undefined;
}>;
export type AgentExecution = z.infer<typeof AgentExecutionSchema>;
export declare const ToolSchema: z.ZodObject<{
    id: z.ZodString;
    createdAt: z.ZodString;
    updatedAt: z.ZodString;
} & {
    name: z.ZodString;
    description: z.ZodString;
    type: z.ZodEnum<["api", "function", "webhook", "database", "custom"]>;
    config: z.ZodRecord<z.ZodString, z.ZodAny>;
    schema: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
    userId: z.ZodString;
    workspaceId: z.ZodOptional<z.ZodString>;
    isActive: z.ZodDefault<z.ZodBoolean>;
    isPublic: z.ZodDefault<z.ZodBoolean>;
    metadata: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
}, "strip", z.ZodTypeAny, {
    type: "function" | "webhook" | "custom" | "api" | "database";
    id: string;
    createdAt: string;
    updatedAt: string;
    name: string;
    description: string;
    config: Record<string, any>;
    userId: string;
    isPublic: boolean;
    isActive: boolean;
    workspaceId?: string | undefined;
    metadata?: Record<string, any> | undefined;
    schema?: Record<string, any> | undefined;
}, {
    type: "function" | "webhook" | "custom" | "api" | "database";
    id: string;
    createdAt: string;
    updatedAt: string;
    name: string;
    description: string;
    config: Record<string, any>;
    userId: string;
    workspaceId?: string | undefined;
    isPublic?: boolean | undefined;
    metadata?: Record<string, any> | undefined;
    isActive?: boolean | undefined;
    schema?: Record<string, any> | undefined;
}>;
export type Tool = z.infer<typeof ToolSchema>;
//# sourceMappingURL=agent.d.ts.map