#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/augment-projects/agent_framework/node_modules/.pnpm/langsmith@0.0.70/node_modules/langsmith/dist/cli/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/agent_framework/node_modules/.pnpm/langsmith@0.0.70/node_modules/langsmith/dist/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/agent_framework/node_modules/.pnpm/langsmith@0.0.70/node_modules/langsmith/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/agent_framework/node_modules/.pnpm/langsmith@0.0.70/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/agent_framework/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/augment-projects/agent_framework/node_modules/.pnpm/langsmith@0.0.70/node_modules/langsmith/dist/cli/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/agent_framework/node_modules/.pnpm/langsmith@0.0.70/node_modules/langsmith/dist/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/agent_framework/node_modules/.pnpm/langsmith@0.0.70/node_modules/langsmith/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/agent_framework/node_modules/.pnpm/langsmith@0.0.70/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/agent_framework/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../langsmith/dist/cli/main.cjs" "$@"
else
  exec node  "$basedir/../langsmith/dist/cli/main.cjs" "$@"
fi
