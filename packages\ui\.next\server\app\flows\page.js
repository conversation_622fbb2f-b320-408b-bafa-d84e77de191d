/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/flows/page";
exports.ids = ["app/flows/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fflows%2Fpage&page=%2Fflows%2Fpage&appPaths=%2Fflows%2Fpage&pagePath=private-next-app-dir%2Fflows%2Fpage.tsx&appDir=C%3A%5CUsers%5CAgaran%5CDocuments%5Caugment-projects%5Cagent_framework%5Cpackages%5Cui%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAgaran%5CDocuments%5Caugment-projects%5Cagent_framework%5Cpackages%5Cui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fflows%2Fpage&page=%2Fflows%2Fpage&appPaths=%2Fflows%2Fpage&pagePath=private-next-app-dir%2Fflows%2Fpage.tsx&appDir=C%3A%5CUsers%5CAgaran%5CDocuments%5Caugment-projects%5Cagent_framework%5Cpackages%5Cui%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAgaran%5CDocuments%5Caugment-projects%5Cagent_framework%5Cpackages%5Cui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?2387\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'flows',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/flows/page.tsx */ \"(rsc)/./src/app/flows/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\app\\\\flows\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\app\\\\flows\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/flows/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/flows/page\",\n        pathname: \"/flows\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fflows%2Fpage&page=%2Fflows%2Fpage&appPaths=%2Fflows%2Fpage&pagePath=private-next-app-dir%2Fflows%2Fpage.tsx&appDir=C%3A%5CUsers%5CAgaran%5CDocuments%5Caugment-projects%5Cagent_framework%5Cpackages%5Cui%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAgaran%5CDocuments%5Caugment-projects%5Cagent_framework%5Cpackages%5Cui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/app-router.js */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/client-page.js */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cnode_modules%5C%5C.pnpm%5C%5Creact-hot-toast%402.5.2_react-dom%4018.3.1_react%4018.3.1%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cpackages%5C%5Cui%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cpackages%5C%5Cui%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cnode_modules%5C%5C.pnpm%5C%5Creact-hot-toast%402.5.2_react-dom%4018.3.1_react%4018.3.1%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cpackages%5C%5Cui%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cpackages%5C%5Cui%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/.pnpm/react-hot-toast@2.5.2_react-dom@18.3.1_react@18.3.1/node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/../../node_modules/.pnpm/react-hot-toast@2.5.2_react-dom@18.3.1_react@18.3.1/node_modules/react-hot-toast/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers.tsx */ \"(ssr)/./src/components/providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL25leHRAMTQuMi4zMF9yZWFjdC1kb21AMTguMy4xX3JlYWN0QDE4LjMuMS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FnYXJhbiU1QyU1Q0RvY3VtZW50cyU1QyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMlNUNhZ2VudF9mcmFtZXdvcmslNUMlNUNub2RlX21vZHVsZXMlNUMlNUMucG5wbSU1QyU1Q25leHQlNDAxNC4yLjMwX3JlYWN0LWRvbSU0MDE4LjMuMV9yZWFjdCU0MDE4LjMuMSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlNUMlNUMlNUMlNUNhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNBZ2FyYW4lNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDYWdlbnRfZnJhbWV3b3JrJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDLnBucG0lNUMlNUNyZWFjdC1ob3QtdG9hc3QlNDAyLjUuMl9yZWFjdC1kb20lNDAxOC4zLjFfcmVhY3QlNDAxOC4zLjElNUMlNUNub2RlX21vZHVsZXMlNUMlNUNyZWFjdC1ob3QtdG9hc3QlNUMlNUNkaXN0JTVDJTVDaW5kZXgubWpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyVG9hc3RlciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNBZ2FyYW4lNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDYWdlbnRfZnJhbWV3b3JrJTVDJTVDcGFja2FnZXMlNUMlNUN1aSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FnYXJhbiU1QyU1Q0RvY3VtZW50cyU1QyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMlNUNhZ2VudF9mcmFtZXdvcmslNUMlNUNwYWNrYWdlcyU1QyU1Q3VpJTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q3Byb3ZpZGVycy50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJQcm92aWRlcnMlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDRWQUEyUDtBQUMzUDtBQUNBLHdLQUFpTCIsInNvdXJjZXMiOlsid2VicGFjazovL0BmbG93d2lzZS1jbG9uZS91aS8/ZjdiNiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlRvYXN0ZXJcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxBZ2FyYW5cXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcYWdlbnRfZnJhbWV3b3JrXFxcXG5vZGVfbW9kdWxlc1xcXFwucG5wbVxcXFxyZWFjdC1ob3QtdG9hc3RAMi41LjJfcmVhY3QtZG9tQDE4LjMuMV9yZWFjdEAxOC4zLjFcXFxcbm9kZV9tb2R1bGVzXFxcXHJlYWN0LWhvdC10b2FzdFxcXFxkaXN0XFxcXGluZGV4Lm1qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiUHJvdmlkZXJzXCJdICovIFwiQzpcXFxcVXNlcnNcXFxcQWdhcmFuXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXGFnZW50X2ZyYW1ld29ya1xcXFxwYWNrYWdlc1xcXFx1aVxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxwcm92aWRlcnMudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cnode_modules%5C%5C.pnpm%5C%5Creact-hot-toast%402.5.2_react-dom%4018.3.1_react%4018.3.1%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cpackages%5C%5Cui%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cpackages%5C%5Cui%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cpackages%5C%5Cui%5C%5Csrc%5C%5Capp%5C%5Cflows%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cpackages%5C%5Cui%5C%5Csrc%5C%5Capp%5C%5Cflows%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/flows/page.tsx */ \"(ssr)/./src/app/flows/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL25leHRAMTQuMi4zMF9yZWFjdC1kb21AMTguMy4xX3JlYWN0QDE4LjMuMS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FnYXJhbiU1QyU1Q0RvY3VtZW50cyU1QyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMlNUNhZ2VudF9mcmFtZXdvcmslNUMlNUNwYWNrYWdlcyU1QyU1Q3VpJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZmxvd3MlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsNEpBQTZJIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGZsb3d3aXNlLWNsb25lL3VpLz9lMDU1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQWdhcmFuXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXGFnZW50X2ZyYW1ld29ya1xcXFxwYWNrYWdlc1xcXFx1aVxcXFxzcmNcXFxcYXBwXFxcXGZsb3dzXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAgaran%5C%5CDocuments%5C%5Caugment-projects%5C%5Cagent_framework%5C%5Cpackages%5C%5Cui%5C%5Csrc%5C%5Capp%5C%5Cflows%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../shared/dist/constants/index.js":
/*!*****************************************!*\
  !*** ../shared/dist/constants/index.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.LIMITS = exports.SUPPORTED_FILE_TYPES = exports.VECTOR_DATABASES = exports.LLM_PROVIDERS = exports.NODE_COLORS = exports.NODE_CATEGORIES = exports.API_ENDPOINTS = void 0;\n// API endpoints\nexports.API_ENDPOINTS = {\n    FLOWS: \"/api/flows\",\n    NODES: \"/api/nodes\",\n    CHAT: \"/api/chat\",\n    AGENTS: \"/api/agents\",\n    USERS: \"/api/users\",\n    WORKSPACES: \"/api/workspaces\",\n    EXECUTIONS: \"/api/executions\",\n    TEMPLATES: \"/api/templates\"\n};\n// Node categories\nexports.NODE_CATEGORIES = {\n    LLM: \"Large Language Models\",\n    MEMORY: \"Memory\",\n    TOOLS: \"Tools\",\n    VECTOR_STORES: \"Vector Stores\",\n    DOCUMENT_LOADERS: \"Document Loaders\",\n    TEXT_SPLITTERS: \"Text Splitters\",\n    EMBEDDINGS: \"Embeddings\",\n    RETRIEVERS: \"Retrievers\",\n    CHAINS: \"Chains\",\n    AGENTS: \"Agents\",\n    OUTPUT_PARSERS: \"Output Parsers\",\n    PROMPT_TEMPLATES: \"Prompt Templates\",\n    UTILITIES: \"Utilities\",\n    CUSTOM: \"Custom\"\n};\n// Default node colors\nexports.NODE_COLORS = {\n    LLM: \"#3B82F6\",\n    MEMORY: \"#10B981\",\n    TOOLS: \"#F59E0B\",\n    VECTOR_STORES: \"#8B5CF6\",\n    DOCUMENT_LOADERS: \"#EC4899\",\n    TEXT_SPLITTERS: \"#06B6D4\",\n    EMBEDDINGS: \"#84CC16\",\n    RETRIEVERS: \"#EF4444\",\n    CHAINS: \"#6B7280\",\n    AGENTS: \"#F97316\",\n    OUTPUT_PARSERS: \"#14B8A6\",\n    PROMPT_TEMPLATES: \"#A855F7\",\n    UTILITIES: \"#64748B\",\n    CUSTOM: \"#1F2937\"\n};\n// Supported LLM providers\nexports.LLM_PROVIDERS = {\n    OPENAI: \"OpenAI\",\n    ANTHROPIC: \"Anthropic\",\n    GOOGLE: \"Google\",\n    AZURE_OPENAI: \"Azure OpenAI\",\n    HUGGING_FACE: \"Hugging Face\",\n    COHERE: \"Cohere\",\n    REPLICATE: \"Replicate\",\n    LOCAL: \"Local Models\"\n};\n// Supported vector databases\nexports.VECTOR_DATABASES = {\n    PINECONE: \"Pinecone\",\n    CHROMA: \"Chroma\",\n    WEAVIATE: \"Weaviate\",\n    QDRANT: \"Qdrant\",\n    MILVUS: \"Milvus\",\n    FAISS: \"FAISS\",\n    REDIS: \"Redis\",\n    ELASTICSEARCH: \"Elasticsearch\"\n};\n// File types supported for document loading\nexports.SUPPORTED_FILE_TYPES = {\n    PDF: \".pdf\",\n    DOCX: \".docx\",\n    DOC: \".doc\",\n    TXT: \".txt\",\n    MD: \".md\",\n    CSV: \".csv\",\n    JSON: \".json\",\n    XML: \".xml\",\n    HTML: \".html\",\n    RTF: \".rtf\"\n};\n// Default limits\nexports.LIMITS = {\n    MAX_NODES_PER_FLOW: 100,\n    MAX_CONNECTIONS_PER_NODE: 20,\n    MAX_MESSAGE_LENGTH: 10000,\n    MAX_FILE_SIZE: 50 * 1024 * 1024,\n    MAX_EXECUTION_TIME: 300000,\n    MAX_CHAT_HISTORY: 100\n}; //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../shared/dist/constants/index.js\n");

/***/ }),

/***/ "(ssr)/../shared/dist/index.js":
/*!*******************************!*\
  !*** ../shared/dist/index.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nvar __createBinding = (void 0) && (void 0).__createBinding || (Object.create ? function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n        desc = {\n            enumerable: true,\n            get: function() {\n                return m[k];\n            }\n        };\n    }\n    Object.defineProperty(o, k2, desc);\n} : function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n});\nvar __exportStar = (void 0) && (void 0).__exportStar || function(m, exports1) {\n    for(var p in m)if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports1, p)) __createBinding(exports1, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n// Export all types\n__exportStar(__webpack_require__(/*! ./types */ \"(ssr)/../shared/dist/types/index.js\"), exports);\n// Export utilities\n__exportStar(__webpack_require__(/*! ./utils */ \"(ssr)/../shared/dist/utils/index.js\"), exports);\n// Export constants\n__exportStar(__webpack_require__(/*! ./constants */ \"(ssr)/../shared/dist/constants/index.js\"), exports); //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vc2hhcmVkL2Rpc3QvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxtQkFBbUI7QUFDbkJBLGFBQUFDLG1CQUFBQSxDQUFBLHVEQUFBQztBQUVBLG1CQUFtQjtBQUNuQkYsYUFBQUMsbUJBQUFBLENBQUEsdURBQUFDO0FBRUEsbUJBQW1CO0FBQ25CRixhQUFBQyxtQkFBQUEsQ0FBQSwrREFBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AZmxvd3dpc2UtY2xvbmUvdWkvLi4vc3JjL2luZGV4LnRzPzUyZDUiXSwibmFtZXMiOlsiX19leHBvcnRTdGFyIiwicmVxdWlyZSIsImV4cG9ydHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../shared/dist/index.js\n");

/***/ }),

/***/ "(ssr)/../shared/dist/types/agent.js":
/*!*************************************!*\
  !*** ../shared/dist/types/agent.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.ToolSchema = exports.AgentExecutionSchema = exports.MultiAgentSystemSchema = exports.AgentSchema = exports.AgentType = void 0;\nconst zod_1 = __webpack_require__(/*! zod */ \"(ssr)/../../node_modules/.pnpm/zod@3.25.67/node_modules/zod/dist/cjs/index.js\");\nconst common_1 = __webpack_require__(/*! ./common */ \"(ssr)/../shared/dist/types/common.js\");\n// Agent type enum\nvar AgentType;\n(function(AgentType) {\n    AgentType[\"CONVERSATIONAL\"] = \"conversational\";\n    AgentType[\"REACT\"] = \"react\";\n    AgentType[\"PLAN_AND_EXECUTE\"] = \"planAndExecute\";\n    AgentType[\"OPENAI_FUNCTIONS\"] = \"openAiFunctions\";\n    AgentType[\"STRUCTURED_CHAT\"] = \"structuredChat\";\n    AgentType[\"CUSTOM\"] = \"custom\";\n})(AgentType || (exports.AgentType = AgentType = {}));\n// Agent schema\nexports.AgentSchema = common_1.BaseEntitySchema.extend({\n    name: zod_1.z.string(),\n    description: zod_1.z.string().optional(),\n    type: zod_1.z.nativeEnum(AgentType),\n    llmId: zod_1.z.string().uuid(),\n    tools: zod_1.z.array(zod_1.z.string().uuid()),\n    memory: zod_1.z.string().uuid().optional(),\n    systemMessage: zod_1.z.string().optional(),\n    maxIterations: zod_1.z.number().default(10),\n    temperature: zod_1.z.number().min(0).max(2).default(0.7),\n    topP: zod_1.z.number().min(0).max(1).default(1),\n    frequencyPenalty: zod_1.z.number().min(-2).max(2).default(0),\n    presencePenalty: zod_1.z.number().min(-2).max(2).default(0),\n    stopSequences: zod_1.z.array(zod_1.z.string()).default([]),\n    userId: zod_1.z.string().uuid(),\n    workspaceId: zod_1.z.string().uuid().optional(),\n    isActive: zod_1.z.boolean().default(true),\n    metadata: zod_1.z.record(zod_1.z.any()).optional()\n});\n// Multi-agent system schema\nexports.MultiAgentSystemSchema = common_1.BaseEntitySchema.extend({\n    name: zod_1.z.string(),\n    description: zod_1.z.string().optional(),\n    agents: zod_1.z.array(zod_1.z.string().uuid()),\n    orchestrator: zod_1.z.string().uuid().optional(),\n    workflow: zod_1.z.record(zod_1.z.any()),\n    userId: zod_1.z.string().uuid(),\n    workspaceId: zod_1.z.string().uuid().optional(),\n    isActive: zod_1.z.boolean().default(true),\n    metadata: zod_1.z.record(zod_1.z.any()).optional()\n});\n// Agent execution schema\nexports.AgentExecutionSchema = common_1.BaseEntitySchema.extend({\n    agentId: zod_1.z.string().uuid(),\n    systemId: zod_1.z.string().uuid().optional(),\n    status: zod_1.z.nativeEnum(common_1.ExecutionStatus),\n    input: zod_1.z.any(),\n    output: zod_1.z.any().optional(),\n    error: zod_1.z.string().optional(),\n    steps: zod_1.z.array(zod_1.z.object({\n        step: zod_1.z.number(),\n        action: zod_1.z.string(),\n        observation: zod_1.z.string().optional(),\n        thought: zod_1.z.string().optional(),\n        timestamp: zod_1.z.string().datetime()\n    })),\n    startTime: zod_1.z.string().datetime(),\n    endTime: zod_1.z.string().datetime().optional(),\n    executionTime: zod_1.z.number().optional(),\n    userId: zod_1.z.string().uuid().optional(),\n    sessionId: zod_1.z.string().optional(),\n    metadata: zod_1.z.record(zod_1.z.any()).optional()\n});\n// Tool schema\nexports.ToolSchema = common_1.BaseEntitySchema.extend({\n    name: zod_1.z.string(),\n    description: zod_1.z.string(),\n    type: zod_1.z.enum([\n        \"api\",\n        \"function\",\n        \"webhook\",\n        \"database\",\n        \"custom\"\n    ]),\n    config: zod_1.z.record(zod_1.z.any()),\n    schema: zod_1.z.record(zod_1.z.any()).optional(),\n    userId: zod_1.z.string().uuid(),\n    workspaceId: zod_1.z.string().uuid().optional(),\n    isActive: zod_1.z.boolean().default(true),\n    isPublic: zod_1.z.boolean().default(false),\n    metadata: zod_1.z.record(zod_1.z.any()).optional()\n}); //# sourceMappingURL=agent.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../shared/dist/types/agent.js\n");

/***/ }),

/***/ "(ssr)/../shared/dist/types/chat.js":
/*!************************************!*\
  !*** ../shared/dist/types/chat.js ***!
  \************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.StreamingChatResponseSchema = exports.ChatResponseSchema = exports.ChatRequestSchema = exports.ChatSessionSchema = exports.MessageSchema = exports.MessageRole = void 0;\nconst zod_1 = __webpack_require__(/*! zod */ \"(ssr)/../../node_modules/.pnpm/zod@3.25.67/node_modules/zod/dist/cjs/index.js\");\nconst common_1 = __webpack_require__(/*! ./common */ \"(ssr)/../shared/dist/types/common.js\");\n// Message role enum\nvar MessageRole;\n(function(MessageRole) {\n    MessageRole[\"USER\"] = \"user\";\n    MessageRole[\"ASSISTANT\"] = \"assistant\";\n    MessageRole[\"SYSTEM\"] = \"system\";\n    MessageRole[\"FUNCTION\"] = \"function\";\n    MessageRole[\"TOOL\"] = \"tool\";\n})(MessageRole || (exports.MessageRole = MessageRole = {}));\n// Message schema\nexports.MessageSchema = common_1.BaseEntitySchema.extend({\n    role: zod_1.z.nativeEnum(MessageRole),\n    content: zod_1.z.string(),\n    name: zod_1.z.string().optional(),\n    functionCall: zod_1.z.object({\n        name: zod_1.z.string(),\n        arguments: zod_1.z.string()\n    }).optional(),\n    toolCalls: zod_1.z.array(zod_1.z.object({\n        id: zod_1.z.string(),\n        type: zod_1.z.literal(\"function\"),\n        function: zod_1.z.object({\n            name: zod_1.z.string(),\n            arguments: zod_1.z.string()\n        })\n    })).optional(),\n    toolCallId: zod_1.z.string().optional(),\n    metadata: zod_1.z.record(zod_1.z.any()).optional()\n});\n// Chat session schema\nexports.ChatSessionSchema = common_1.BaseEntitySchema.extend({\n    flowId: zod_1.z.string().uuid(),\n    userId: zod_1.z.string().uuid().optional(),\n    sessionId: zod_1.z.string(),\n    title: zod_1.z.string().optional(),\n    messages: zod_1.z.array(exports.MessageSchema),\n    isActive: zod_1.z.boolean().default(true),\n    metadata: zod_1.z.record(zod_1.z.any()).optional()\n});\n// Chat request schema\nexports.ChatRequestSchema = zod_1.z.object({\n    message: zod_1.z.string(),\n    sessionId: zod_1.z.string().optional(),\n    flowId: zod_1.z.string().uuid(),\n    userId: zod_1.z.string().uuid().optional(),\n    streaming: zod_1.z.boolean().default(false),\n    metadata: zod_1.z.record(zod_1.z.any()).optional()\n});\n// Chat response schema\nexports.ChatResponseSchema = zod_1.z.object({\n    message: exports.MessageSchema,\n    sessionId: zod_1.z.string(),\n    isComplete: zod_1.z.boolean().default(true),\n    metadata: zod_1.z.record(zod_1.z.any()).optional()\n});\n// Streaming chat response schema\nexports.StreamingChatResponseSchema = zod_1.z.object({\n    delta: zod_1.z.string(),\n    sessionId: zod_1.z.string(),\n    isComplete: zod_1.z.boolean(),\n    metadata: zod_1.z.record(zod_1.z.any()).optional()\n}); //# sourceMappingURL=chat.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../shared/dist/types/chat.js\n");

/***/ }),

/***/ "(ssr)/../shared/dist/types/common.js":
/*!**************************************!*\
  !*** ../shared/dist/types/common.js ***!
  \**************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.BaseEntitySchema = exports.PositionSchema = exports.ExecutionStatus = exports.FlowStatus = exports.DataType = exports.ConnectionType = exports.NodeType = exports.TimestampSchema = exports.IdSchema = void 0;\nconst zod_1 = __webpack_require__(/*! zod */ \"(ssr)/../../node_modules/.pnpm/zod@3.25.67/node_modules/zod/dist/cjs/index.js\");\n// Base schemas\nexports.IdSchema = zod_1.z.string().uuid();\nexports.TimestampSchema = zod_1.z.string().datetime();\n// Common enums\nvar NodeType;\n(function(NodeType) {\n    NodeType[\"START\"] = \"start\";\n    NodeType[\"LLM\"] = \"llm\";\n    NodeType[\"TOOL\"] = \"tool\";\n    NodeType[\"MEMORY\"] = \"memory\";\n    NodeType[\"VECTOR_STORE\"] = \"vectorStore\";\n    NodeType[\"DOCUMENT_LOADER\"] = \"documentLoader\";\n    NodeType[\"TEXT_SPLITTER\"] = \"textSplitter\";\n    NodeType[\"EMBEDDINGS\"] = \"embeddings\";\n    NodeType[\"RETRIEVER\"] = \"retriever\";\n    NodeType[\"CHAIN\"] = \"chain\";\n    NodeType[\"AGENT\"] = \"agent\";\n    NodeType[\"OUTPUT_PARSER\"] = \"outputParser\";\n    NodeType[\"PROMPT_TEMPLATE\"] = \"promptTemplate\";\n    NodeType[\"CONDITIONAL\"] = \"conditional\";\n    NodeType[\"LOOP\"] = \"loop\";\n    NodeType[\"WEBHOOK\"] = \"webhook\";\n    NodeType[\"API_CALL\"] = \"apiCall\";\n    NodeType[\"CUSTOM\"] = \"custom\";\n})(NodeType || (exports.NodeType = NodeType = {}));\nvar ConnectionType;\n(function(ConnectionType) {\n    ConnectionType[\"INPUT\"] = \"input\";\n    ConnectionType[\"OUTPUT\"] = \"output\";\n})(ConnectionType || (exports.ConnectionType = ConnectionType = {}));\nvar DataType;\n(function(DataType) {\n    DataType[\"STRING\"] = \"string\";\n    DataType[\"NUMBER\"] = \"number\";\n    DataType[\"BOOLEAN\"] = \"boolean\";\n    DataType[\"OBJECT\"] = \"object\";\n    DataType[\"ARRAY\"] = \"array\";\n    DataType[\"LLM\"] = \"llm\";\n    DataType[\"MEMORY\"] = \"memory\";\n    DataType[\"VECTOR_STORE\"] = \"vectorStore\";\n    DataType[\"DOCUMENT\"] = \"document\";\n    DataType[\"TOOL\"] = \"tool\";\n    DataType[\"AGENT\"] = \"agent\";\n})(DataType || (exports.DataType = DataType = {}));\nvar FlowStatus;\n(function(FlowStatus) {\n    FlowStatus[\"DRAFT\"] = \"draft\";\n    FlowStatus[\"ACTIVE\"] = \"active\";\n    FlowStatus[\"INACTIVE\"] = \"inactive\";\n    FlowStatus[\"ARCHIVED\"] = \"archived\";\n})(FlowStatus || (exports.FlowStatus = FlowStatus = {}));\nvar ExecutionStatus;\n(function(ExecutionStatus) {\n    ExecutionStatus[\"PENDING\"] = \"pending\";\n    ExecutionStatus[\"RUNNING\"] = \"running\";\n    ExecutionStatus[\"COMPLETED\"] = \"completed\";\n    ExecutionStatus[\"FAILED\"] = \"failed\";\n    ExecutionStatus[\"CANCELLED\"] = \"cancelled\";\n})(ExecutionStatus || (exports.ExecutionStatus = ExecutionStatus = {}));\n// Position schema for node positioning\nexports.PositionSchema = zod_1.z.object({\n    x: zod_1.z.number(),\n    y: zod_1.z.number()\n});\n// Base entity schema\nexports.BaseEntitySchema = zod_1.z.object({\n    id: exports.IdSchema,\n    createdAt: exports.TimestampSchema,\n    updatedAt: exports.TimestampSchema\n}); //# sourceMappingURL=common.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../shared/dist/types/common.js\n");

/***/ }),

/***/ "(ssr)/../shared/dist/types/flow.js":
/*!************************************!*\
  !*** ../shared/dist/types/flow.js ***!
  \************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.FlowValidationResultSchema = exports.FlowTemplateSchema = exports.FlowExecutionSchema = exports.FlowSchema = void 0;\nconst zod_1 = __webpack_require__(/*! zod */ \"(ssr)/../../node_modules/.pnpm/zod@3.25.67/node_modules/zod/dist/cjs/index.js\");\nconst common_1 = __webpack_require__(/*! ./common */ \"(ssr)/../shared/dist/types/common.js\");\nconst node_1 = __webpack_require__(/*! ./node */ \"(ssr)/../shared/dist/types/node.js\");\n// Flow schema\nexports.FlowSchema = common_1.BaseEntitySchema.extend({\n    name: zod_1.z.string(),\n    description: zod_1.z.string().optional(),\n    status: zod_1.z.nativeEnum(common_1.FlowStatus),\n    nodes: zod_1.z.array(node_1.NodeSchema),\n    connections: zod_1.z.array(node_1.FlowConnectionSchema),\n    userId: zod_1.z.string().uuid(),\n    workspaceId: zod_1.z.string().uuid().optional(),\n    isPublic: zod_1.z.boolean().default(false),\n    tags: zod_1.z.array(zod_1.z.string()).default([]),\n    version: zod_1.z.string().default(\"1.0.0\"),\n    metadata: zod_1.z.record(zod_1.z.any()).optional()\n});\n// Flow execution schema\nexports.FlowExecutionSchema = common_1.BaseEntitySchema.extend({\n    flowId: zod_1.z.string().uuid(),\n    status: zod_1.z.nativeEnum(common_1.ExecutionStatus),\n    input: zod_1.z.any().optional(),\n    output: zod_1.z.any().optional(),\n    error: zod_1.z.string().optional(),\n    startTime: zod_1.z.string().datetime(),\n    endTime: zod_1.z.string().datetime().optional(),\n    executionTime: zod_1.z.number().optional(),\n    nodeResults: zod_1.z.array(node_1.NodeExecutionResultSchema),\n    userId: zod_1.z.string().uuid().optional(),\n    sessionId: zod_1.z.string().optional(),\n    metadata: zod_1.z.record(zod_1.z.any()).optional()\n});\n// Flow template schema\nexports.FlowTemplateSchema = zod_1.z.object({\n    name: zod_1.z.string(),\n    description: zod_1.z.string(),\n    category: zod_1.z.string(),\n    tags: zod_1.z.array(zod_1.z.string()),\n    nodes: zod_1.z.array(node_1.NodeSchema),\n    connections: zod_1.z.array(node_1.FlowConnectionSchema),\n    thumbnail: zod_1.z.string().optional(),\n    isOfficial: zod_1.z.boolean().default(false),\n    difficulty: zod_1.z.enum([\n        \"beginner\",\n        \"intermediate\",\n        \"advanced\"\n    ]).default(\"beginner\")\n});\n// Flow validation result\nexports.FlowValidationResultSchema = zod_1.z.object({\n    isValid: zod_1.z.boolean(),\n    errors: zod_1.z.array(zod_1.z.object({\n        nodeId: zod_1.z.string().optional(),\n        connectionId: zod_1.z.string().optional(),\n        message: zod_1.z.string(),\n        type: zod_1.z.enum([\n            \"error\",\n            \"warning\"\n        ])\n    }))\n}); //# sourceMappingURL=flow.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vc2hhcmVkL2Rpc3QvdHlwZXMvZmxvdy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBLE1BQUFBLFFBQUFDLG1CQUFBQSxDQUFBO0FBQ0EsTUFBQUMsV0FBQUQsbUJBQUFBLENBQUE7QUFDQSxNQUFBRSxTQUFBRixtQkFBQUEsQ0FBQTtBQUVBLGNBQWM7QUFDREcsa0JBQVUsR0FBR0YsU0FBQUksZ0JBQWdCLENBQUNDLE1BQU0sQ0FBQztJQUNoREMsTUFBTVIsTUFBQVMsQ0FBQyxDQUFDQyxNQUFNO0lBQ2RDLGFBQWFYLE1BQUFTLENBQUMsQ0FBQ0MsTUFBTSxHQUFHRSxRQUFRO0lBQ2hDQyxRQUFRYixNQUFBUyxDQUFDLENBQUNLLFVBQVUsQ0FBQ1osU0FBQWEsVUFBVTtJQUMvQkMsT0FBT2hCLE1BQUFTLENBQUMsQ0FBQ1EsS0FBSyxDQUFDZCxPQUFBZSxVQUFVO0lBQ3pCQyxhQUFhbkIsTUFBQVMsQ0FBQyxDQUFDUSxLQUFLLENBQUNkLE9BQUFpQixvQkFBb0I7SUFDekNDLFFBQVFyQixNQUFBUyxDQUFDLENBQUNDLE1BQU0sR0FBR1ksSUFBSTtJQUN2QkMsYUFBYXZCLE1BQUFTLENBQUMsQ0FBQ0MsTUFBTSxHQUFHWSxJQUFJLEdBQUdWLFFBQVE7SUFDdkNZLFVBQVV4QixNQUFBUyxDQUFDLENBQUNnQixPQUFPLEdBQUdDLE9BQU8sQ0FBQztJQUM5QkMsTUFBTTNCLE1BQUFTLENBQUMsQ0FBQ1EsS0FBSyxDQUFDakIsTUFBQVMsQ0FBQyxDQUFDQyxNQUFNLElBQUlnQixPQUFPLENBQUMsRUFBRTtJQUNwQ0UsU0FBUzVCLE1BQUFTLENBQUMsQ0FBQ0MsTUFBTSxHQUFHZ0IsT0FBTyxDQUFDO0lBQzVCRyxVQUFVN0IsTUFBQVMsQ0FBQyxDQUFDcUIsTUFBTSxDQUFDOUIsTUFBQVMsQ0FBQyxDQUFDc0IsR0FBRyxJQUFJbkIsUUFBUTs7QUFLdEMsd0JBQXdCO0FBQ1hSLDJCQUFtQixHQUFHRixTQUFBSSxnQkFBZ0IsQ0FBQ0MsTUFBTSxDQUFDO0lBQ3pEMEIsUUFBUWpDLE1BQUFTLENBQUMsQ0FBQ0MsTUFBTSxHQUFHWSxJQUFJO0lBQ3ZCVCxRQUFRYixNQUFBUyxDQUFDLENBQUNLLFVBQVUsQ0FBQ1osU0FBQWdDLGVBQWU7SUFDcENDLE9BQU9uQyxNQUFBUyxDQUFDLENBQUNzQixHQUFHLEdBQUduQixRQUFRO0lBQ3ZCd0IsUUFBUXBDLE1BQUFTLENBQUMsQ0FBQ3NCLEdBQUcsR0FBR25CLFFBQVE7SUFDeEJ5QixPQUFPckMsTUFBQVMsQ0FBQyxDQUFDQyxNQUFNLEdBQUdFLFFBQVE7SUFDMUIwQixXQUFXdEMsTUFBQVMsQ0FBQyxDQUFDQyxNQUFNLEdBQUc2QixRQUFRO0lBQzlCQyxTQUFTeEMsTUFBQVMsQ0FBQyxDQUFDQyxNQUFNLEdBQUc2QixRQUFRLEdBQUczQixRQUFRO0lBQ3ZDNkIsZUFBZXpDLE1BQUFTLENBQUMsQ0FBQ2lDLE1BQU0sR0FBRzlCLFFBQVE7SUFDbEMrQixhQUFhM0MsTUFBQVMsQ0FBQyxDQUFDUSxLQUFLLENBQUNkLE9BQUF5Qyx5QkFBeUI7SUFDOUN2QixRQUFRckIsTUFBQVMsQ0FBQyxDQUFDQyxNQUFNLEdBQUdZLElBQUksR0FBR1YsUUFBUTtJQUNsQ2lDLFdBQVc3QyxNQUFBUyxDQUFDLENBQUNDLE1BQU0sR0FBR0UsUUFBUTtJQUM5QmlCLFVBQVU3QixNQUFBUyxDQUFDLENBQUNxQixNQUFNLENBQUM5QixNQUFBUyxDQUFDLENBQUNzQixHQUFHLElBQUluQixRQUFROztBQUt0Qyx1QkFBdUI7QUFDVlIsMEJBQWtCLEdBQUdKLE1BQUFTLENBQUMsQ0FBQ3NDLE1BQU0sQ0FBQztJQUN6Q3ZDLE1BQU1SLE1BQUFTLENBQUMsQ0FBQ0MsTUFBTTtJQUNkQyxhQUFhWCxNQUFBUyxDQUFDLENBQUNDLE1BQU07SUFDckJzQyxVQUFVaEQsTUFBQVMsQ0FBQyxDQUFDQyxNQUFNO0lBQ2xCaUIsTUFBTTNCLE1BQUFTLENBQUMsQ0FBQ1EsS0FBSyxDQUFDakIsTUFBQVMsQ0FBQyxDQUFDQyxNQUFNO0lBQ3RCTSxPQUFPaEIsTUFBQVMsQ0FBQyxDQUFDUSxLQUFLLENBQUNkLE9BQUFlLFVBQVU7SUFDekJDLGFBQWFuQixNQUFBUyxDQUFDLENBQUNRLEtBQUssQ0FBQ2QsT0FBQWlCLG9CQUFvQjtJQUN6QzZCLFdBQVdqRCxNQUFBUyxDQUFDLENBQUNDLE1BQU0sR0FBR0UsUUFBUTtJQUM5QnNDLFlBQVlsRCxNQUFBUyxDQUFDLENBQUNnQixPQUFPLEdBQUdDLE9BQU8sQ0FBQztJQUNoQ3lCLFlBQVluRCxNQUFBUyxDQUFDLENBQUMyQyxJQUFJLENBQUM7UUFBQztRQUFZO1FBQWdCO0tBQVcsRUFBRTFCLE9BQU8sQ0FBQzs7QUFLdkUseUJBQXlCO0FBQ1p0QixrQ0FBMEIsR0FBR0osTUFBQVMsQ0FBQyxDQUFDc0MsTUFBTSxDQUFDO0lBQ2pETyxTQUFTdEQsTUFBQVMsQ0FBQyxDQUFDZ0IsT0FBTztJQUNsQjhCLFFBQVF2RCxNQUFBUyxDQUFDLENBQUNRLEtBQUssQ0FBQ2pCLE1BQUFTLENBQUMsQ0FBQ3NDLE1BQU0sQ0FBQztRQUN2QlMsUUFBUXhELE1BQUFTLENBQUMsQ0FBQ0MsTUFBTSxHQUFHRSxRQUFRO1FBQzNCNkMsY0FBY3pELE1BQUFTLENBQUMsQ0FBQ0MsTUFBTSxHQUFHRSxRQUFRO1FBQ2pDOEMsU0FBUzFELE1BQUFTLENBQUMsQ0FBQ0MsTUFBTTtRQUNqQmlELE1BQU0zRCxNQUFBUyxDQUFDLENBQUMyQyxJQUFJLENBQUM7WUFBQztZQUFTO1NBQVUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AZmxvd3dpc2UtY2xvbmUvdWkvLi4vLi4vc3JjL3R5cGVzL2Zsb3cudHM/YTczZCJdLCJuYW1lcyI6WyJ6b2RfMSIsInJlcXVpcmUiLCJjb21tb25fMSIsIm5vZGVfMSIsImV4cG9ydHMiLCJGbG93U2NoZW1hIiwiQmFzZUVudGl0eVNjaGVtYSIsImV4dGVuZCIsIm5hbWUiLCJ6Iiwic3RyaW5nIiwiZGVzY3JpcHRpb24iLCJvcHRpb25hbCIsInN0YXR1cyIsIm5hdGl2ZUVudW0iLCJGbG93U3RhdHVzIiwibm9kZXMiLCJhcnJheSIsIk5vZGVTY2hlbWEiLCJjb25uZWN0aW9ucyIsIkZsb3dDb25uZWN0aW9uU2NoZW1hIiwidXNlcklkIiwidXVpZCIsIndvcmtzcGFjZUlkIiwiaXNQdWJsaWMiLCJib29sZWFuIiwiZGVmYXVsdCIsInRhZ3MiLCJ2ZXJzaW9uIiwibWV0YWRhdGEiLCJyZWNvcmQiLCJhbnkiLCJGbG93RXhlY3V0aW9uU2NoZW1hIiwiZmxvd0lkIiwiRXhlY3V0aW9uU3RhdHVzIiwiaW5wdXQiLCJvdXRwdXQiLCJlcnJvciIsInN0YXJ0VGltZSIsImRhdGV0aW1lIiwiZW5kVGltZSIsImV4ZWN1dGlvblRpbWUiLCJudW1iZXIiLCJub2RlUmVzdWx0cyIsIk5vZGVFeGVjdXRpb25SZXN1bHRTY2hlbWEiLCJzZXNzaW9uSWQiLCJGbG93VGVtcGxhdGVTY2hlbWEiLCJvYmplY3QiLCJjYXRlZ29yeSIsInRodW1ibmFpbCIsImlzT2ZmaWNpYWwiLCJkaWZmaWN1bHR5IiwiZW51bSIsIkZsb3dWYWxpZGF0aW9uUmVzdWx0U2NoZW1hIiwiaXNWYWxpZCIsImVycm9ycyIsIm5vZGVJZCIsImNvbm5lY3Rpb25JZCIsIm1lc3NhZ2UiLCJ0eXBlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../shared/dist/types/flow.js\n");

/***/ }),

/***/ "(ssr)/../shared/dist/types/index.js":
/*!*************************************!*\
  !*** ../shared/dist/types/index.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nvar __createBinding = (void 0) && (void 0).__createBinding || (Object.create ? function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n        desc = {\n            enumerable: true,\n            get: function() {\n                return m[k];\n            }\n        };\n    }\n    Object.defineProperty(o, k2, desc);\n} : function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n});\nvar __exportStar = (void 0) && (void 0).__exportStar || function(m, exports1) {\n    for(var p in m)if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports1, p)) __createBinding(exports1, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n__exportStar(__webpack_require__(/*! ./node */ \"(ssr)/../shared/dist/types/node.js\"), exports);\n__exportStar(__webpack_require__(/*! ./flow */ \"(ssr)/../shared/dist/types/flow.js\"), exports);\n__exportStar(__webpack_require__(/*! ./chat */ \"(ssr)/../shared/dist/types/chat.js\"), exports);\n__exportStar(__webpack_require__(/*! ./user */ \"(ssr)/../shared/dist/types/user.js\"), exports);\n__exportStar(__webpack_require__(/*! ./agent */ \"(ssr)/../shared/dist/types/agent.js\"), exports);\n__exportStar(__webpack_require__(/*! ./common */ \"(ssr)/../shared/dist/types/common.js\"), exports); //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vc2hhcmVkL2Rpc3QvdHlwZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQUEsYUFBQUMsbUJBQUFBLENBQUEscURBQUFDO0FBQ0FGLGFBQUFDLG1CQUFBQSxDQUFBLHFEQUFBQztBQUNBRixhQUFBQyxtQkFBQUEsQ0FBQSxxREFBQUM7QUFDQUYsYUFBQUMsbUJBQUFBLENBQUEscURBQUFDO0FBQ0FGLGFBQUFDLG1CQUFBQSxDQUFBLHVEQUFBQztBQUNBRixhQUFBQyxtQkFBQUEsQ0FBQSx5REFBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AZmxvd3dpc2UtY2xvbmUvdWkvLi4vLi4vc3JjL3R5cGVzL2luZGV4LnRzP2JkZGQiXSwibmFtZXMiOlsiX19leHBvcnRTdGFyIiwicmVxdWlyZSIsImV4cG9ydHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../shared/dist/types/index.js\n");

/***/ }),

/***/ "(ssr)/../shared/dist/types/node.js":
/*!************************************!*\
  !*** ../shared/dist/types/node.js ***!
  \************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.NodeTemplateSchema = exports.NodeExecutionResultSchema = exports.FlowConnectionSchema = exports.NodeSchema = exports.NodeConfigSchema = exports.NodeConnectionSchema = void 0;\nconst zod_1 = __webpack_require__(/*! zod */ \"(ssr)/../../node_modules/.pnpm/zod@3.25.67/node_modules/zod/dist/cjs/index.js\");\nconst common_1 = __webpack_require__(/*! ./common */ \"(ssr)/../shared/dist/types/common.js\");\n// Node connection schema\nexports.NodeConnectionSchema = zod_1.z.object({\n    id: zod_1.z.string(),\n    type: zod_1.z.nativeEnum(common_1.ConnectionType),\n    dataType: zod_1.z.nativeEnum(common_1.DataType),\n    label: zod_1.z.string(),\n    required: zod_1.z.boolean().default(false),\n    multiple: zod_1.z.boolean().default(false)\n});\n// Node configuration schema\nexports.NodeConfigSchema = zod_1.z.record(zod_1.z.any());\n// Node schema\nexports.NodeSchema = common_1.BaseEntitySchema.extend({\n    type: zod_1.z.nativeEnum(common_1.NodeType),\n    name: zod_1.z.string(),\n    label: zod_1.z.string(),\n    description: zod_1.z.string().optional(),\n    category: zod_1.z.string(),\n    version: zod_1.z.string().default(\"1.0.0\"),\n    position: common_1.PositionSchema,\n    inputs: zod_1.z.array(exports.NodeConnectionSchema),\n    outputs: zod_1.z.array(exports.NodeConnectionSchema),\n    config: exports.NodeConfigSchema,\n    flowId: zod_1.z.string().uuid(),\n    isCustom: zod_1.z.boolean().default(false),\n    icon: zod_1.z.string().optional(),\n    color: zod_1.z.string().optional(),\n    documentation: zod_1.z.string().optional()\n});\n// Flow connection schema (connections between nodes)\nexports.FlowConnectionSchema = zod_1.z.object({\n    id: zod_1.z.string(),\n    sourceNodeId: zod_1.z.string().uuid(),\n    sourceHandle: zod_1.z.string(),\n    targetNodeId: zod_1.z.string().uuid(),\n    targetHandle: zod_1.z.string(),\n    flowId: zod_1.z.string().uuid()\n});\n// Node execution result schema\nexports.NodeExecutionResultSchema = zod_1.z.object({\n    nodeId: zod_1.z.string().uuid(),\n    status: zod_1.z.enum([\n        \"success\",\n        \"error\",\n        \"pending\"\n    ]),\n    output: zod_1.z.any().optional(),\n    error: zod_1.z.string().optional(),\n    executionTime: zod_1.z.number().optional(),\n    timestamp: zod_1.z.string().datetime()\n});\n// Node template schema for creating new nodes\nexports.NodeTemplateSchema = zod_1.z.object({\n    type: zod_1.z.nativeEnum(common_1.NodeType),\n    name: zod_1.z.string(),\n    label: zod_1.z.string(),\n    description: zod_1.z.string(),\n    category: zod_1.z.string(),\n    version: zod_1.z.string(),\n    inputs: zod_1.z.array(exports.NodeConnectionSchema),\n    outputs: zod_1.z.array(exports.NodeConnectionSchema),\n    defaultConfig: exports.NodeConfigSchema,\n    icon: zod_1.z.string().optional(),\n    color: zod_1.z.string().optional(),\n    documentation: zod_1.z.string().optional(),\n    isCustom: zod_1.z.boolean().default(false)\n}); //# sourceMappingURL=node.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../shared/dist/types/node.js\n");

/***/ }),

/***/ "(ssr)/../shared/dist/types/user.js":
/*!************************************!*\
  !*** ../shared/dist/types/user.js ***!
  \************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.ApiKeySchema = exports.WorkspaceMemberSchema = exports.WorkspaceSchema = exports.UserSchema = exports.UserRole = void 0;\nconst zod_1 = __webpack_require__(/*! zod */ \"(ssr)/../../node_modules/.pnpm/zod@3.25.67/node_modules/zod/dist/cjs/index.js\");\nconst common_1 = __webpack_require__(/*! ./common */ \"(ssr)/../shared/dist/types/common.js\");\n// User role enum\nvar UserRole;\n(function(UserRole) {\n    UserRole[\"ADMIN\"] = \"admin\";\n    UserRole[\"USER\"] = \"user\";\n    UserRole[\"VIEWER\"] = \"viewer\";\n})(UserRole || (exports.UserRole = UserRole = {}));\n// User schema\nexports.UserSchema = common_1.BaseEntitySchema.extend({\n    email: zod_1.z.string().email(),\n    name: zod_1.z.string(),\n    avatar: zod_1.z.string().optional(),\n    role: zod_1.z.nativeEnum(UserRole),\n    isActive: zod_1.z.boolean().default(true),\n    lastLoginAt: zod_1.z.string().datetime().optional(),\n    preferences: zod_1.z.record(zod_1.z.any()).optional()\n});\n// Workspace schema\nexports.WorkspaceSchema = common_1.BaseEntitySchema.extend({\n    name: zod_1.z.string(),\n    description: zod_1.z.string().optional(),\n    ownerId: zod_1.z.string().uuid(),\n    isActive: zod_1.z.boolean().default(true),\n    settings: zod_1.z.record(zod_1.z.any()).optional()\n});\n// Workspace member schema\nexports.WorkspaceMemberSchema = common_1.BaseEntitySchema.extend({\n    workspaceId: zod_1.z.string().uuid(),\n    userId: zod_1.z.string().uuid(),\n    role: zod_1.z.nativeEnum(UserRole),\n    joinedAt: zod_1.z.string().datetime()\n});\n// API key schema\nexports.ApiKeySchema = common_1.BaseEntitySchema.extend({\n    name: zod_1.z.string(),\n    key: zod_1.z.string(),\n    userId: zod_1.z.string().uuid(),\n    workspaceId: zod_1.z.string().uuid().optional(),\n    isActive: zod_1.z.boolean().default(true),\n    expiresAt: zod_1.z.string().datetime().optional(),\n    lastUsedAt: zod_1.z.string().datetime().optional(),\n    permissions: zod_1.z.array(zod_1.z.string()).default([])\n}); //# sourceMappingURL=user.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../shared/dist/types/user.js\n");

/***/ }),

/***/ "(ssr)/../shared/dist/utils/crypto.js":
/*!**************************************!*\
  !*** ../shared/dist/utils/crypto.js ***!
  \**************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.generateUUID = generateUUID;\nexports.generateSessionId = generateSessionId;\nexports.generateApiKey = generateApiKey;\nexports.hashString = hashString;\nexports.generateRandomString = generateRandomString;\nconst crypto_1 = __webpack_require__(/*! crypto */ \"crypto\");\n/**\n * Generates a random UUID v4\n */ function generateUUID() {\n    const bytes = (0, crypto_1.randomBytes)(16);\n    bytes[6] = bytes[6] & 0x0f | 0x40; // Version 4\n    bytes[8] = bytes[8] & 0x3f | 0x80; // Variant 10\n    const hex = bytes.toString(\"hex\");\n    return [\n        hex.substring(0, 8),\n        hex.substring(8, 12),\n        hex.substring(12, 16),\n        hex.substring(16, 20),\n        hex.substring(20, 32)\n    ].join(\"-\");\n}\n/**\n * Generates a random session ID\n */ function generateSessionId() {\n    return (0, crypto_1.randomBytes)(16).toString(\"hex\");\n}\n/**\n * Generates a random API key\n */ function generateApiKey() {\n    return \"fc_\" + (0, crypto_1.randomBytes)(32).toString(\"hex\");\n}\n/**\n * Hashes a string using SHA-256\n */ function hashString(input) {\n    return (0, crypto_1.createHash)(\"sha256\").update(input).digest(\"hex\");\n}\n/**\n * Generates a random string of specified length\n */ function generateRandomString(length) {\n    const chars = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\";\n    let result = \"\";\n    for(let i = 0; i < length; i++){\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n} //# sourceMappingURL=crypto.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vc2hhcmVkL2Rpc3QvdXRpbHMvY3J5cHRvLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFLQUEsb0JBQUEsR0FBQUM7QUFrQkFELHlCQUFBLEdBQUFFO0FBT0FGLHNCQUFBLEdBQUFHO0FBT0FILGtCQUFBLEdBQUFJO0FBT0FKLDRCQUFBLEdBQUFLO0FBNUNBLE1BQUFDLFdBQUFDLG1CQUFBQSxDQUFBO0FBRUE7O0lBR0EsU0FBZ0JOO0lBQ2QsTUFBTU8sUUFBUSxJQUFBRixTQUFBRyxXQUFXLEVBQUM7SUFDMUJELEtBQUssQ0FBQyxFQUFFLEdBQUcsS0FBTSxDQUFDLEVBQUUsR0FBRyxPQUFRLE1BQU0sWUFBWTtJQUNqREEsS0FBSyxDQUFDLEVBQUUsR0FBRyxLQUFNLENBQUMsRUFBRSxHQUFHLE9BQVEsTUFBTSxhQUFhO0lBRWxELE1BQU1FLE1BQU1GLE1BQU1HLFFBQVEsQ0FBQztJQUMzQixPQUFPO1FBQ0xELElBQUlFLFNBQVMsQ0FBQyxHQUFHO1FBQ2pCRixJQUFJRSxTQUFTLENBQUMsR0FBRztRQUNqQkYsSUFBSUUsU0FBUyxDQUFDLElBQUk7UUFDbEJGLElBQUlFLFNBQVMsQ0FBQyxJQUFJO1FBQ2xCRixJQUFJRSxTQUFTLENBQUMsSUFBSTtLQUNuQixDQUFDQyxJQUFJLENBQUM7QUFDVDtBQUVBOztJQUdBLFNBQWdCWDtJQUNkLE9BQU8sSUFBQUksU0FBQUcsV0FBVyxFQUFDLElBQUlFLFFBQVEsQ0FBQztBQUNsQztBQUVBOztJQUdBLFNBQWdCUjtJQUNkLE9BQU8sUUFBUSxJQUFBRyxTQUFBRyxXQUFXLEVBQUMsSUFBSUUsUUFBUSxDQUFDO0FBQzFDO0FBRUE7O0lBR0EsU0FBZ0JQLFdBQVdVLEtBQWE7SUFDdEMsT0FBTyxJQUFBUixTQUFBUyxVQUFVLEVBQUMsVUFBVUMsTUFBTSxDQUFDRixPQUFPRyxNQUFNLENBQUM7QUFDbkQ7QUFFQTs7SUFHQSxTQUFnQloscUJBQXFCYSxNQUFjO0lBQ2pELE1BQU1DLFFBQVE7SUFDZCxJQUFJQyxTQUFTO0lBQ2IsSUFBSyxJQUFJQyxJQUFJLEdBQUdBLElBQUlILFFBQVFHLElBQUs7UUFDL0JELFVBQVVELE1BQU1HLE1BQU0sQ0FBQ0MsS0FBS0MsS0FBSyxDQUFDRCxLQUFLRSxNQUFNLEtBQUtOLE1BQU1ELE1BQU07SUFDaEU7SUFDQSxPQUFPRTtBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGZsb3d3aXNlLWNsb25lL3VpLy4uLy4uL3NyYy91dGlscy9jcnlwdG8udHM/ZjBjYyJdLCJuYW1lcyI6WyJleHBvcnRzIiwiZ2VuZXJhdGVVVUlEIiwiZ2VuZXJhdGVTZXNzaW9uSWQiLCJnZW5lcmF0ZUFwaUtleSIsImhhc2hTdHJpbmciLCJnZW5lcmF0ZVJhbmRvbVN0cmluZyIsImNyeXB0b18xIiwicmVxdWlyZSIsImJ5dGVzIiwicmFuZG9tQnl0ZXMiLCJoZXgiLCJ0b1N0cmluZyIsInN1YnN0cmluZyIsImpvaW4iLCJpbnB1dCIsImNyZWF0ZUhhc2giLCJ1cGRhdGUiLCJkaWdlc3QiLCJsZW5ndGgiLCJjaGFycyIsInJlc3VsdCIsImkiLCJjaGFyQXQiLCJNYXRoIiwiZmxvb3IiLCJyYW5kb20iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../shared/dist/utils/crypto.js\n");

/***/ }),

/***/ "(ssr)/../shared/dist/utils/formatting.js":
/*!******************************************!*\
  !*** ../shared/dist/utils/formatting.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.formatTimestamp = formatTimestamp;\nexports.formatExecutionTime = formatExecutionTime;\nexports.truncateText = truncateText;\nexports.capitalize = capitalize;\nexports.camelToTitle = camelToTitle;\nexports.generateNodeColor = generateNodeColor;\n/**\n * Formats a timestamp to a human-readable string\n */ function formatTimestamp(timestamp) {\n    return new Date(timestamp).toLocaleString();\n}\n/**\n * Formats execution time in milliseconds to a human-readable string\n */ function formatExecutionTime(ms) {\n    if (ms < 1000) {\n        return `${ms}ms`;\n    }\n    if (ms < 60000) {\n        return `${(ms / 1000).toFixed(1)}s`;\n    }\n    return `${(ms / 60000).toFixed(1)}m`;\n}\n/**\n * Truncates text to a specified length with ellipsis\n */ function truncateText(text, maxLength) {\n    if (text.length <= maxLength) {\n        return text;\n    }\n    return text.substring(0, maxLength - 3) + \"...\";\n}\n/**\n * Capitalizes the first letter of a string\n */ function capitalize(str) {\n    return str.charAt(0).toUpperCase() + str.slice(1);\n}\n/**\n * Converts camelCase to Title Case\n */ function camelToTitle(str) {\n    return str.replace(/([A-Z])/g, \" $1\").replace(/^./, (str)=>str.toUpperCase()).trim();\n}\n/**\n * Generates a random color for nodes\n */ function generateNodeColor() {\n    const colors = [\n        \"#3B82F6\",\n        \"#EF4444\",\n        \"#10B981\",\n        \"#F59E0B\",\n        \"#8B5CF6\",\n        \"#EC4899\",\n        \"#06B6D4\",\n        \"#84CC16\"\n    ];\n    return colors[Math.floor(Math.random() * colors.length)];\n} //# sourceMappingURL=formatting.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vc2hhcmVkL2Rpc3QvdXRpbHMvZm9ybWF0dGluZy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBR0FBLHVCQUFBLEdBQUFDO0FBT0FELDJCQUFBLEdBQUFFO0FBYUFGLG9CQUFBLEdBQUFHO0FBVUFILGtCQUFBLEdBQUFJO0FBT0FKLG9CQUFBLEdBQUFLO0FBVUFMLHlCQUFBLEdBQUFNO0FBbERBOztJQUdBLFNBQWdCTCxnQkFBZ0JNLFNBQWlCO0lBQy9DLE9BQU8sSUFBSUMsS0FBS0QsV0FBV0UsY0FBYztBQUMzQztBQUVBOztJQUdBLFNBQWdCUCxvQkFBb0JRLEVBQVU7SUFDNUMsSUFBSUEsS0FBSyxNQUFNO1FBQ2IsT0FBTyxHQUFHQSxHQUFFLEdBQUk7SUFDbEI7SUFDQSxJQUFJQSxLQUFLLE9BQU87UUFDZCxPQUFPLEdBQUcsQ0FBQ0EsS0FBSyxNQUFNQyxPQUFPLENBQUMsR0FBRSxFQUFHO0lBQ3JDO0lBQ0EsT0FBTyxHQUFHLENBQUNELEtBQUssT0FBT0MsT0FBTyxDQUFDLEdBQUUsRUFBRztBQUN0QztBQUVBOztJQUdBLFNBQWdCUixhQUFhUyxJQUFZLEVBQUVDLFNBQWlCO0lBQzFELElBQUlELEtBQUtFLE1BQU0sSUFBSUQsV0FBVztRQUM1QixPQUFPRDtJQUNUO0lBQ0EsT0FBT0EsS0FBS0csU0FBUyxDQUFDLEdBQUdGLFlBQVksS0FBSztBQUM1QztBQUVBOztJQUdBLFNBQWdCVCxXQUFXWSxHQUFXO0lBQ3BDLE9BQU9BLElBQUlDLE1BQU0sQ0FBQyxHQUFHQyxXQUFXLEtBQUtGLElBQUlHLEtBQUssQ0FBQztBQUNqRDtBQUVBOztJQUdBLFNBQWdCZCxhQUFhVyxHQUFXO0lBQ3RDLE9BQU9BLElBQ0pJLE9BQU8sQ0FBQyxZQUFZLE9BQ3BCQSxPQUFPLENBQUMsTUFBTSxDQUFDSixNQUFRQSxJQUFJRSxXQUFXLElBQ3RDRyxJQUFJO0FBQ1Q7QUFFQTs7SUFHQSxTQUFnQmY7SUFDZCxNQUFNZ0IsU0FBUztRQUNiO1FBQVc7UUFBVztRQUFXO1FBQ2pDO1FBQVc7UUFBVztRQUFXO0tBQ2xDO0lBQ0QsT0FBT0EsTUFBTSxDQUFDQyxLQUFLQyxLQUFLLENBQUNELEtBQUtFLE1BQU0sS0FBS0gsT0FBT1IsTUFBTSxFQUFFO0FBQzFEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGZsb3d3aXNlLWNsb25lL3VpLy4uLy4uL3NyYy91dGlscy9mb3JtYXR0aW5nLnRzPzI0YmEiXSwibmFtZXMiOlsiZXhwb3J0cyIsImZvcm1hdFRpbWVzdGFtcCIsImZvcm1hdEV4ZWN1dGlvblRpbWUiLCJ0cnVuY2F0ZVRleHQiLCJjYXBpdGFsaXplIiwiY2FtZWxUb1RpdGxlIiwiZ2VuZXJhdGVOb2RlQ29sb3IiLCJ0aW1lc3RhbXAiLCJEYXRlIiwidG9Mb2NhbGVTdHJpbmciLCJtcyIsInRvRml4ZWQiLCJ0ZXh0IiwibWF4TGVuZ3RoIiwibGVuZ3RoIiwic3Vic3RyaW5nIiwic3RyIiwiY2hhckF0IiwidG9VcHBlckNhc2UiLCJzbGljZSIsInJlcGxhY2UiLCJ0cmltIiwiY29sb3JzIiwiTWF0aCIsImZsb29yIiwicmFuZG9tIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../shared/dist/utils/formatting.js\n");

/***/ }),

/***/ "(ssr)/../shared/dist/utils/index.js":
/*!*************************************!*\
  !*** ../shared/dist/utils/index.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nvar __createBinding = (void 0) && (void 0).__createBinding || (Object.create ? function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n        desc = {\n            enumerable: true,\n            get: function() {\n                return m[k];\n            }\n        };\n    }\n    Object.defineProperty(o, k2, desc);\n} : function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n});\nvar __exportStar = (void 0) && (void 0).__exportStar || function(m, exports1) {\n    for(var p in m)if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports1, p)) __createBinding(exports1, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n__exportStar(__webpack_require__(/*! ./validation */ \"(ssr)/../shared/dist/utils/validation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./formatting */ \"(ssr)/../shared/dist/utils/formatting.js\"), exports);\n__exportStar(__webpack_require__(/*! ./crypto */ \"(ssr)/../shared/dist/utils/crypto.js\"), exports); //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vc2hhcmVkL2Rpc3QvdXRpbHMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQUEsYUFBQUMsbUJBQUFBLENBQUEsaUVBQUFDO0FBQ0FGLGFBQUFDLG1CQUFBQSxDQUFBLGlFQUFBQztBQUNBRixhQUFBQyxtQkFBQUEsQ0FBQSx5REFBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AZmxvd3dpc2UtY2xvbmUvdWkvLi4vLi4vc3JjL3V0aWxzL2luZGV4LnRzP2EzN2MiXSwibmFtZXMiOlsiX19leHBvcnRTdGFyIiwicmVxdWlyZSIsImV4cG9ydHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../shared/dist/utils/index.js\n");

/***/ }),

/***/ "(ssr)/../shared/dist/utils/validation.js":
/*!******************************************!*\
  !*** ../shared/dist/utils/validation.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.validateData = validateData;\nexports.safeParseData = safeParseData;\nexports.validateArray = validateArray;\nconst zod_1 = __webpack_require__(/*! zod */ \"(ssr)/../../node_modules/.pnpm/zod@3.25.67/node_modules/zod/dist/cjs/index.js\");\n/**\n * Validates data against a Zod schema and returns a result object\n */ function validateData(schema, data) {\n    try {\n        const result = schema.parse(data);\n        return {\n            success: true,\n            data: result\n        };\n    } catch (error) {\n        if (error instanceof zod_1.z.ZodError) {\n            const errors = error.errors.map((err)=>`${err.path.join(\".\")}: ${err.message}`);\n            return {\n                success: false,\n                errors\n            };\n        }\n        return {\n            success: false,\n            errors: [\n                \"Unknown validation error\"\n            ]\n        };\n    }\n}\n/**\n * Safely parses data with a Zod schema, returning null on error\n */ function safeParseData(schema, data) {\n    try {\n        return schema.parse(data);\n    } catch  {\n        return null;\n    }\n}\n/**\n * Validates an array of data against a schema\n */ function validateArray(schema, data) {\n    const arraySchema = zod_1.z.array(schema);\n    return validateData(arraySchema, data);\n} //# sourceMappingURL=validation.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vc2hhcmVkL2Rpc3QvdXRpbHMvdmFsaWRhdGlvbi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBS0FBLG9CQUFBLEdBQUFDO0FBcUJBRCxxQkFBQSxHQUFBRTtBQWNBRixxQkFBQSxHQUFBRztBQXhDQSxNQUFBQyxRQUFBQyxtQkFBQUEsQ0FBQTtBQUVBOztJQUdBLFNBQWdCSixhQUNkSyxNQUFzQixFQUN0QkMsSUFBYTtJQUViLElBQUk7UUFDRixNQUFNQyxTQUFTRixPQUFPRyxLQUFLLENBQUNGO1FBQzVCLE9BQU87WUFBRUcsU0FBUztZQUFNSCxNQUFNQztRQUFNO0lBQ3RDLEVBQUUsT0FBT0csT0FBTztRQUNkLElBQUlBLGlCQUFpQlAsTUFBQVEsQ0FBQyxDQUFDQyxRQUFRLEVBQUU7WUFDL0IsTUFBTUMsU0FBU0gsTUFBTUcsTUFBTSxDQUFDQyxHQUFHLENBQUNDLENBQUFBLE1BQzlCLEdBQUdBLElBQUlDLElBQUksQ0FBQ0MsSUFBSSxDQUFDLEtBQUksSUFBS0YsSUFBSUcsT0FBTyxFQUFFO1lBRXpDLE9BQU87Z0JBQUVULFNBQVM7Z0JBQU9JO1lBQU07UUFDakM7UUFDQSxPQUFPO1lBQUVKLFNBQVM7WUFBT0ksUUFBUTtnQkFBQzthQUEyQjtRQUFBO0lBQy9EO0FBQ0Y7QUFFQTs7SUFHQSxTQUFnQlosY0FDZEksTUFBc0IsRUFDdEJDLElBQWE7SUFFYixJQUFJO1FBQ0YsT0FBT0QsT0FBT0csS0FBSyxDQUFDRjtJQUN0QixFQUFFLE9BQU07UUFDTixPQUFPO0lBQ1Q7QUFDRjtBQUVBOztJQUdBLFNBQWdCSixjQUNkRyxNQUFzQixFQUN0QkMsSUFBZTtJQUVmLE1BQU1hLGNBQWNoQixNQUFBUSxDQUFDLENBQUNTLEtBQUssQ0FBQ2Y7SUFDNUIsT0FBT0wsYUFBYW1CLGFBQWFiO0FBQ25DIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGZsb3d3aXNlLWNsb25lL3VpLy4uLy4uL3NyYy91dGlscy92YWxpZGF0aW9uLnRzP2JiMjUiXSwibmFtZXMiOlsiZXhwb3J0cyIsInZhbGlkYXRlRGF0YSIsInNhZmVQYXJzZURhdGEiLCJ2YWxpZGF0ZUFycmF5Iiwiem9kXzEiLCJyZXF1aXJlIiwic2NoZW1hIiwiZGF0YSIsInJlc3VsdCIsInBhcnNlIiwic3VjY2VzcyIsImVycm9yIiwieiIsIlpvZEVycm9yIiwiZXJyb3JzIiwibWFwIiwiZXJyIiwicGF0aCIsImpvaW4iLCJtZXNzYWdlIiwiYXJyYXlTY2hlbWEiLCJhcnJheSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../shared/dist/utils/validation.js\n");

/***/ }),

/***/ "(ssr)/./src/app/flows/page.tsx":
/*!********************************!*\
  !*** ./src/app/flows/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FlowsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Filter_Grid_List_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Filter,Grid,List,Plus,Search!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Filter_Grid_List_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Filter,Grid,List,Plus,Search!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Filter_Grid_List_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Filter,Grid,List,Plus,Search!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_Filter_Grid_List_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Filter,Grid,List,Plus,Search!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Filter_Grid_List_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Filter,Grid,List,Plus,Search!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _components_flow_FlowCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/flow/FlowCard */ \"(ssr)/./src/components/flow/FlowCard.tsx\");\n/* harmony import */ var _components_flow_CreateFlowDialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/flow/CreateFlowDialog */ \"(ssr)/./src/components/flow/CreateFlowDialog.tsx\");\n/* harmony import */ var _flowwise_clone_shared__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @flowwise-clone/shared */ \"(ssr)/../shared/dist/index.js\");\n/* harmony import */ var _flowwise_clone_shared__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_flowwise_clone_shared__WEBPACK_IMPORTED_MODULE_5__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n// Mock data - replace with actual API calls\nconst mockFlows = [\n    {\n        id: \"1\",\n        name: \"Customer Support Bot\",\n        description: \"AI-powered customer support chatbot with knowledge base integration\",\n        status: _flowwise_clone_shared__WEBPACK_IMPORTED_MODULE_5__.FlowStatus.ACTIVE,\n        createdAt: \"2024-01-15T10:00:00Z\",\n        updatedAt: \"2024-01-20T15:30:00Z\",\n        nodeCount: 8,\n        tags: [\n            \"chatbot\",\n            \"support\",\n            \"rag\"\n        ]\n    },\n    {\n        id: \"2\",\n        name: \"Document Analyzer\",\n        description: \"Multi-agent system for analyzing and summarizing documents\",\n        status: _flowwise_clone_shared__WEBPACK_IMPORTED_MODULE_5__.FlowStatus.DRAFT,\n        createdAt: \"2024-01-18T09:15:00Z\",\n        updatedAt: \"2024-01-18T16:45:00Z\",\n        nodeCount: 12,\n        tags: [\n            \"document\",\n            \"analysis\",\n            \"multi-agent\"\n        ]\n    },\n    {\n        id: \"3\",\n        name: \"Code Review Assistant\",\n        description: \"AI agent that reviews code and provides suggestions\",\n        status: _flowwise_clone_shared__WEBPACK_IMPORTED_MODULE_5__.FlowStatus.ACTIVE,\n        createdAt: \"2024-01-10T14:20:00Z\",\n        updatedAt: \"2024-01-22T11:10:00Z\",\n        nodeCount: 6,\n        tags: [\n            \"code\",\n            \"review\",\n            \"development\"\n        ]\n    }\n];\nfunction FlowsPage() {\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"grid\");\n    const [showCreateDialog, setShowCreateDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const filteredFlows = mockFlows.filter((flow)=>flow.name.toLowerCase().includes(searchQuery.toLowerCase()) || flow.description.toLowerCase().includes(searchQuery.toLowerCase()) || flow.tags.some((tag)=>tag.toLowerCase().includes(searchQuery.toLowerCase())));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: \"Flows\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\app\\\\flows\\\\page.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Build and manage your AI workflows\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\app\\\\flows\\\\page.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\app\\\\flows\\\\page.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        onClick: ()=>setShowCreateDialog(true),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Filter_Grid_List_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\app\\\\flows\\\\page.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, this),\n                            \"Create Flow\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\app\\\\flows\\\\page.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\app\\\\flows\\\\page.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex-1 max-w-md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Filter_Grid_List_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\app\\\\flows\\\\page.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"Search flows...\",\n                                value: searchQuery,\n                                onChange: (e)=>setSearchQuery(e.target.value),\n                                className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\app\\\\flows\\\\page.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\app\\\\flows\\\\page.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        variant: \"outline\",\n                        size: \"sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Filter_Grid_List_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\app\\\\flows\\\\page.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this),\n                            \"Filter\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\app\\\\flows\\\\page.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center border border-gray-300 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: viewMode === \"grid\" ? \"default\" : \"ghost\",\n                                size: \"sm\",\n                                onClick: ()=>setViewMode(\"grid\"),\n                                className: \"rounded-r-none\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Filter_Grid_List_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\app\\\\flows\\\\page.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\app\\\\flows\\\\page.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: viewMode === \"list\" ? \"default\" : \"ghost\",\n                                size: \"sm\",\n                                onClick: ()=>setViewMode(\"list\"),\n                                className: \"rounded-l-none\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Filter_Grid_List_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\app\\\\flows\\\\page.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\app\\\\flows\\\\page.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\app\\\\flows\\\\page.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\app\\\\flows\\\\page.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, this),\n            filteredFlows.length === 0 && searchQuery === \"\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Filter_Grid_List_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"h-8 w-8 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\app\\\\flows\\\\page.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\app\\\\flows\\\\page.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"No flows yet\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\app\\\\flows\\\\page.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: \"Get started by creating your first AI workflow\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\app\\\\flows\\\\page.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        onClick: ()=>setShowCreateDialog(true),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Filter_Grid_List_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\app\\\\flows\\\\page.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 13\n                            }, this),\n                            \"Create Your First Flow\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\app\\\\flows\\\\page.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\app\\\\flows\\\\page.tsx\",\n                lineNumber: 111,\n                columnNumber: 9\n            }, this),\n            filteredFlows.length === 0 && searchQuery !== \"\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Filter_Grid_List_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"h-8 w-8 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\app\\\\flows\\\\page.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\app\\\\flows\\\\page.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"No flows found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\app\\\\flows\\\\page.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Try adjusting your search terms\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\app\\\\flows\\\\page.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\app\\\\flows\\\\page.tsx\",\n                lineNumber: 130,\n                columnNumber: 9\n            }, this),\n            filteredFlows.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: viewMode === \"grid\" ? \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\" : \"space-y-4\",\n                children: filteredFlows.map((flow)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_flow_FlowCard__WEBPACK_IMPORTED_MODULE_3__.FlowCard, {\n                        flow: flow,\n                        viewMode: viewMode\n                    }, flow.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\app\\\\flows\\\\page.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\app\\\\flows\\\\page.tsx\",\n                lineNumber: 145,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_flow_CreateFlowDialog__WEBPACK_IMPORTED_MODULE_4__.CreateFlowDialog, {\n                open: showCreateDialog,\n                onOpenChange: setShowCreateDialog\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\app\\\\flows\\\\page.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\app\\\\flows\\\\page.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/flows/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/flow/CreateFlowDialog.tsx":
/*!**************************************************!*\
  !*** ./src/components/flow/CreateFlowDialog.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CreateFlowDialog: () => (/* binding */ CreateFlowDialog)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/textarea */ \"(ssr)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(ssr)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/dialog */ \"(ssr)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/select */ \"(ssr)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,X!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,X!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ CreateFlowDialog auto */ \n\n\n\n\n\n\n\n\n\n\n\nconst flowTemplates = [\n    {\n        id: \"blank\",\n        name: \"Blank Flow\",\n        description: \"Start with an empty canvas\",\n        category: \"Basic\"\n    },\n    {\n        id: \"chatbot\",\n        name: \"Chatbot\",\n        description: \"Simple conversational AI\",\n        category: \"Chat\"\n    },\n    {\n        id: \"rag\",\n        name: \"RAG Assistant\",\n        description: \"Document-based Q&A system\",\n        category: \"Knowledge\"\n    },\n    {\n        id: \"multi-agent\",\n        name: \"Multi-Agent System\",\n        description: \"Coordinated agent workflow\",\n        category: \"Advanced\"\n    }\n];\nfunction CreateFlowDialog({ open, onOpenChange }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        description: \"\",\n        template: \"blank\",\n        tags: []\n    });\n    const [newTag, setNewTag] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isCreating, setIsCreating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsCreating(true);\n        try {\n            // TODO: Replace with actual API call\n            const flowId = (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.generateId)();\n            // Simulate API delay\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            // Navigate to the new flow\n            router.push(`/flows/${flowId}`);\n            onOpenChange(false);\n            // Reset form\n            setFormData({\n                name: \"\",\n                description: \"\",\n                template: \"blank\",\n                tags: []\n            });\n        } catch (error) {\n            console.error(\"Failed to create flow:\", error);\n        } finally{\n            setIsCreating(false);\n        }\n    };\n    const addTag = ()=>{\n        if (newTag.trim() && !formData.tags.includes(newTag.trim())) {\n            setFormData((prev)=>({\n                    ...prev,\n                    tags: [\n                        ...prev.tags,\n                        newTag.trim()\n                    ]\n                }));\n            setNewTag(\"\");\n        }\n    };\n    const removeTag = (tagToRemove)=>{\n        setFormData((prev)=>({\n                ...prev,\n                tags: prev.tags.filter((tag)=>tag !== tagToRemove)\n            }));\n    };\n    const selectedTemplate = flowTemplates.find((t)=>t.id === formData.template);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.Dialog, {\n        open: open,\n        onOpenChange: onOpenChange,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogContent, {\n            className: \"sm:max-w-[500px]\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogTitle, {\n                            children: \"Create New Flow\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\CreateFlowDialog.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogDescription, {\n                            children: \"Set up your new AI workflow. You can always change these settings later.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\CreateFlowDialog.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\CreateFlowDialog.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    htmlFor: \"name\",\n                                    children: \"Flow Name\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\CreateFlowDialog.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                    id: \"name\",\n                                    placeholder: \"My Awesome Flow\",\n                                    value: formData.name,\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                name: e.target.value\n                                            })),\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\CreateFlowDialog.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\CreateFlowDialog.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    htmlFor: \"description\",\n                                    children: \"Description\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\CreateFlowDialog.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                    id: \"description\",\n                                    placeholder: \"Describe what this flow does...\",\n                                    value: formData.description,\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                description: e.target.value\n                                            })),\n                                    rows: 3\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\CreateFlowDialog.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\CreateFlowDialog.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    htmlFor: \"template\",\n                                    children: \"Template\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\CreateFlowDialog.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                    value: formData.template,\n                                    onValueChange: (value)=>setFormData((prev)=>({\n                                                ...prev,\n                                                template: value\n                                            })),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\CreateFlowDialog.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\CreateFlowDialog.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                            children: flowTemplates.map((template)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                    value: template.id,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-start\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: template.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\CreateFlowDialog.tsx\",\n                                                                lineNumber: 152,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: template.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\CreateFlowDialog.tsx\",\n                                                                lineNumber: 153,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\CreateFlowDialog.tsx\",\n                                                        lineNumber: 151,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, template.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\CreateFlowDialog.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\CreateFlowDialog.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\CreateFlowDialog.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this),\n                                selectedTemplate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        \"Category: \",\n                                        selectedTemplate.category\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\CreateFlowDialog.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\CreateFlowDialog.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    children: \"Tags\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\CreateFlowDialog.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2 mb-2\",\n                                    children: formData.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                            variant: \"secondary\",\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                tag,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>removeTag(tag),\n                                                    className: \"ml-1 hover:bg-gray-200 rounded-full p-0.5\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\CreateFlowDialog.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\CreateFlowDialog.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, tag, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\CreateFlowDialog.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\CreateFlowDialog.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            placeholder: \"Add a tag...\",\n                                            value: newTag,\n                                            onChange: (e)=>setNewTag(e.target.value),\n                                            onKeyPress: (e)=>{\n                                                if (e.key === \"Enter\") {\n                                                    e.preventDefault();\n                                                    addTag();\n                                                }\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\CreateFlowDialog.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            type: \"button\",\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: addTag,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\CreateFlowDialog.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\CreateFlowDialog.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\CreateFlowDialog.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\CreateFlowDialog.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    type: \"button\",\n                                    variant: \"outline\",\n                                    onClick: ()=>onOpenChange(false),\n                                    disabled: isCreating,\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\CreateFlowDialog.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    type: \"submit\",\n                                    disabled: !formData.name.trim() || isCreating,\n                                    children: isCreating ? \"Creating...\" : \"Create Flow\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\CreateFlowDialog.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\CreateFlowDialog.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\CreateFlowDialog.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\CreateFlowDialog.tsx\",\n            lineNumber: 108,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\CreateFlowDialog.tsx\",\n        lineNumber: 107,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/flow/CreateFlowDialog.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/flow/FlowCard.tsx":
/*!******************************************!*\
  !*** ./src/components/flow/FlowCard.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FlowCard: () => (/* binding */ FlowCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_Calendar_Copy_Edit_Layers_MoreHorizontal_Play_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Calendar,Copy,Edit,Layers,MoreHorizontal,Play!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Calendar_Copy_Edit_Layers_MoreHorizontal_Play_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Calendar,Copy,Edit,Layers,MoreHorizontal,Play!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Calendar_Copy_Edit_Layers_MoreHorizontal_Play_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Calendar,Copy,Edit,Layers,MoreHorizontal,Play!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Calendar_Copy_Edit_Layers_MoreHorizontal_Play_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Calendar,Copy,Edit,Layers,MoreHorizontal,Play!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/layers.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Calendar_Copy_Edit_Layers_MoreHorizontal_Play_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Calendar,Copy,Edit,Layers,MoreHorizontal,Play!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Calendar_Copy_Edit_Layers_MoreHorizontal_Play_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Calendar,Copy,Edit,Layers,MoreHorizontal,Play!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/more-horizontal.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Calendar_Copy_Edit_Layers_MoreHorizontal_Play_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Calendar,Copy,Edit,Layers,MoreHorizontal,Play!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _flowwise_clone_shared__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @flowwise-clone/shared */ \"(ssr)/../shared/dist/index.js\");\n/* harmony import */ var _flowwise_clone_shared__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_flowwise_clone_shared__WEBPACK_IMPORTED_MODULE_6__);\n/* __next_internal_client_entry_do_not_use__ FlowCard auto */ \n\n\n\n\n\n\n\nconst statusColors = {\n    [_flowwise_clone_shared__WEBPACK_IMPORTED_MODULE_6__.FlowStatus.ACTIVE]: \"bg-green-100 text-green-800\",\n    [_flowwise_clone_shared__WEBPACK_IMPORTED_MODULE_6__.FlowStatus.DRAFT]: \"bg-yellow-100 text-yellow-800\",\n    [_flowwise_clone_shared__WEBPACK_IMPORTED_MODULE_6__.FlowStatus.INACTIVE]: \"bg-gray-100 text-gray-800\",\n    [_flowwise_clone_shared__WEBPACK_IMPORTED_MODULE_6__.FlowStatus.ARCHIVED]: \"bg-red-100 text-red-800\"\n};\nconst statusIcons = {\n    [_flowwise_clone_shared__WEBPACK_IMPORTED_MODULE_6__.FlowStatus.ACTIVE]: _barrel_optimize_names_Activity_Calendar_Copy_Edit_Layers_MoreHorizontal_Play_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n    [_flowwise_clone_shared__WEBPACK_IMPORTED_MODULE_6__.FlowStatus.DRAFT]: _barrel_optimize_names_Activity_Calendar_Copy_Edit_Layers_MoreHorizontal_Play_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n    [_flowwise_clone_shared__WEBPACK_IMPORTED_MODULE_6__.FlowStatus.INACTIVE]: _barrel_optimize_names_Activity_Calendar_Copy_Edit_Layers_MoreHorizontal_Play_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n    [_flowwise_clone_shared__WEBPACK_IMPORTED_MODULE_6__.FlowStatus.ARCHIVED]: _barrel_optimize_names_Activity_Calendar_Copy_Edit_Layers_MoreHorizontal_Play_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n};\nfunction FlowCard({ flow, viewMode }) {\n    const StatusIcon = statusIcons[flow.status];\n    if (viewMode === \"list\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n            className: \"hover:shadow-md transition-shadow\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                className: \"p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4 flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 mb-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-lg\",\n                                                    children: flow.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                                                    lineNumber: 60,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                    className: statusColors[flow.status],\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusIcon, {\n                                                            className: \"h-3 w-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                                                            lineNumber: 62,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        flow.status\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                                                    lineNumber: 61,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm mb-2\",\n                                            children: flow.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4 text-sm text-gray-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Calendar_Copy_Edit_Layers_MoreHorizontal_Play_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                                                            lineNumber: 69,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        flow.nodeCount,\n                                                        \" nodes\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                                                    lineNumber: 68,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Calendar_Copy_Edit_Layers_MoreHorizontal_Play_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                                                            lineNumber: 73,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Updated \",\n                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatDate)(flow.updatedAt)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                                                    lineNumber: 72,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-1\",\n                                    children: [\n                                        flow.tags.slice(0, 3).map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                variant: \"secondary\",\n                                                className: \"text-xs\",\n                                                children: tag\n                                            }, tag, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 19\n                                            }, this)),\n                                        flow.tags.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                            variant: \"secondary\",\n                                            className: \"text-xs\",\n                                            children: [\n                                                \"+\",\n                                                flow.tags.length - 3\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 ml-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    size: \"sm\",\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        href: `/flows/${flow.id}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Calendar_Copy_Edit_Layers_MoreHorizontal_Play_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Edit\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    size: \"sm\",\n                                    variant: \"outline\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Calendar_Copy_Edit_Layers_MoreHorizontal_Play_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Test\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    size: \"sm\",\n                                    variant: \"ghost\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Calendar_Copy_Edit_Layers_MoreHorizontal_Play_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                lineNumber: 55,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n            lineNumber: 54,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n        className: \"hover:shadow-md transition-shadow\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                className: \"pb-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 mb-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                                            className: \"text-lg\",\n                                            children: flow.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                            className: statusColors[flow.status],\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusIcon, {\n                                                    className: \"h-3 w-3 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 17\n                                                }, this),\n                                                flow.status\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardDescription, {\n                                    className: \"line-clamp-2\",\n                                    children: flow.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            size: \"sm\",\n                            variant: \"ghost\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Calendar_Copy_Edit_Layers_MoreHorizontal_Play_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                className: \"pt-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4 text-sm text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Calendar_Copy_Edit_Layers_MoreHorizontal_Play_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, this),\n                                        flow.nodeCount,\n                                        \" nodes\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Calendar_Copy_Edit_Layers_MoreHorizontal_Play_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, this),\n                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatDate)(flow.updatedAt)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-1 mb-4\",\n                        children: [\n                            flow.tags.slice(0, 3).map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                    variant: \"secondary\",\n                                    className: \"text-xs\",\n                                    children: tag\n                                }, tag, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this)),\n                            flow.tags.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                variant: \"secondary\",\n                                className: \"text-xs\",\n                                children: [\n                                    \"+\",\n                                    flow.tags.length - 3\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                size: \"sm\",\n                                className: \"flex-1\",\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    href: `/flows/${flow.id}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Calendar_Copy_Edit_Layers_MoreHorizontal_Play_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Edit\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                size: \"sm\",\n                                variant: \"outline\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Calendar_Copy_Edit_Layers_MoreHorizontal_Play_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                size: \"sm\",\n                                variant: \"outline\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Calendar_Copy_Edit_Layers_MoreHorizontal_Play_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\flow\\\\FlowCard.tsx\",\n        lineNumber: 113,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/flow/FlowCard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers.tsx":
/*!**************************************!*\
  !*** ./src/components/providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/../../node_modules/.pnpm/@tanstack+query-core@5.81.5/node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/../../node_modules/.pnpm/@tanstack+react-query@5.81.5_react@18.3.1/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query-devtools */ \"(ssr)/../../node_modules/.pnpm/@tanstack+react-query-devtools@5.81.5_@tanstack+react-query@5.81.5_react@18.3.1/node_modules/@tanstack/react-query-devtools/build/modern/index.js\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-themes */ \"(ssr)/../../node_modules/.pnpm/next-themes@0.2.1_next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next-themes/dist/index.module.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\n\n\nfunction Providers({ children }) {\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(()=>new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.QueryClient({\n            defaultOptions: {\n                queries: {\n                    staleTime: 60 * 1000,\n                    retry: 1\n                }\n            }\n        }));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.QueryClientProvider, {\n        client: queryClient,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_1__.ThemeProvider, {\n                attribute: \"class\",\n                defaultTheme: \"light\",\n                enableSystem: true,\n                disableTransitionOnChange: true,\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\providers.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_5__.ReactQueryDevtools, {\n                initialIsOpen: false\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\providers.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\providers.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFFeUU7QUFDTDtBQUN4QjtBQUNYO0FBRTFCLFNBQVNLLFVBQVUsRUFBRUMsUUFBUSxFQUFpQztJQUNuRSxNQUFNLENBQUNDLFlBQVksR0FBR0gsK0NBQVFBLENBQzVCLElBQ0UsSUFBSUosOERBQVdBLENBQUM7WUFDZFEsZ0JBQWdCO2dCQUNkQyxTQUFTO29CQUNQQyxXQUFXLEtBQUs7b0JBQ2hCQyxPQUFPO2dCQUNUO1lBQ0Y7UUFDRjtJQUdKLHFCQUNFLDhEQUFDVixzRUFBbUJBO1FBQUNXLFFBQVFMOzswQkFDM0IsOERBQUNKLHNEQUFhQTtnQkFDWlUsV0FBVTtnQkFDVkMsY0FBYTtnQkFDYkMsWUFBWTtnQkFDWkMseUJBQXlCOzBCQUV4QlY7Ozs7OzswQkFFSCw4REFBQ0osOEVBQWtCQTtnQkFBQ2UsZUFBZTs7Ozs7Ozs7Ozs7O0FBR3pDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGZsb3d3aXNlLWNsb25lL3VpLy4vc3JjL2NvbXBvbmVudHMvcHJvdmlkZXJzLnRzeD9iZTg3Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgUXVlcnlDbGllbnQsIFF1ZXJ5Q2xpZW50UHJvdmlkZXIgfSBmcm9tICdAdGFuc3RhY2svcmVhY3QtcXVlcnknO1xuaW1wb3J0IHsgUmVhY3RRdWVyeURldnRvb2xzIH0gZnJvbSAnQHRhbnN0YWNrL3JlYWN0LXF1ZXJ5LWRldnRvb2xzJztcbmltcG9ydCB7IFRoZW1lUHJvdmlkZXIgfSBmcm9tICduZXh0LXRoZW1lcyc7XG5pbXBvcnQgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcblxuZXhwb3J0IGZ1bmN0aW9uIFByb3ZpZGVycyh7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZSB9KSB7XG4gIGNvbnN0IFtxdWVyeUNsaWVudF0gPSB1c2VTdGF0ZShcbiAgICAoKSA9PlxuICAgICAgbmV3IFF1ZXJ5Q2xpZW50KHtcbiAgICAgICAgZGVmYXVsdE9wdGlvbnM6IHtcbiAgICAgICAgICBxdWVyaWVzOiB7XG4gICAgICAgICAgICBzdGFsZVRpbWU6IDYwICogMTAwMCwgLy8gMSBtaW51dGVcbiAgICAgICAgICAgIHJldHJ5OiAxLFxuICAgICAgICAgIH0sXG4gICAgICAgIH0sXG4gICAgICB9KVxuICApO1xuXG4gIHJldHVybiAoXG4gICAgPFF1ZXJ5Q2xpZW50UHJvdmlkZXIgY2xpZW50PXtxdWVyeUNsaWVudH0+XG4gICAgICA8VGhlbWVQcm92aWRlclxuICAgICAgICBhdHRyaWJ1dGU9XCJjbGFzc1wiXG4gICAgICAgIGRlZmF1bHRUaGVtZT1cImxpZ2h0XCJcbiAgICAgICAgZW5hYmxlU3lzdGVtXG4gICAgICAgIGRpc2FibGVUcmFuc2l0aW9uT25DaGFuZ2VcbiAgICAgID5cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgPC9UaGVtZVByb3ZpZGVyPlxuICAgICAgPFJlYWN0UXVlcnlEZXZ0b29scyBpbml0aWFsSXNPcGVuPXtmYWxzZX0gLz5cbiAgICA8L1F1ZXJ5Q2xpZW50UHJvdmlkZXI+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiUXVlcnlDbGllbnQiLCJRdWVyeUNsaWVudFByb3ZpZGVyIiwiUmVhY3RRdWVyeURldnRvb2xzIiwiVGhlbWVQcm92aWRlciIsInVzZVN0YXRlIiwiUHJvdmlkZXJzIiwiY2hpbGRyZW4iLCJxdWVyeUNsaWVudCIsImRlZmF1bHRPcHRpb25zIiwicXVlcmllcyIsInN0YWxlVGltZSIsInJldHJ5IiwiY2xpZW50IiwiYXR0cmlidXRlIiwiZGVmYXVsdFRoZW1lIiwiZW5hYmxlU3lzdGVtIiwiZGlzYWJsZVRyYW5zaXRpb25PbkNoYW5nZSIsImluaXRpYWxJc09wZW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/badge.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/../../node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n            outline: \"text-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-slot@1.2.3_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/../../node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 45,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 8,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 23,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 31,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 46,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 58,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 66,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/dialog.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/dialog.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Dialog: () => (/* binding */ Dialog),\n/* harmony export */   DialogClose: () => (/* binding */ DialogClose),\n/* harmony export */   DialogContent: () => (/* binding */ DialogContent),\n/* harmony export */   DialogDescription: () => (/* binding */ DialogDescription),\n/* harmony export */   DialogFooter: () => (/* binding */ DialogFooter),\n/* harmony export */   DialogHeader: () => (/* binding */ DialogHeader),\n/* harmony export */   DialogOverlay: () => (/* binding */ DialogOverlay),\n/* harmony export */   DialogPortal: () => (/* binding */ DialogPortal),\n/* harmony export */   DialogTitle: () => (/* binding */ DialogTitle),\n/* harmony export */   DialogTrigger: () => (/* binding */ DialogTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-dialog */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-dialog@1.1.14_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@radix-ui/react-dialog/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst Dialog = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst DialogTrigger = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Trigger;\nconst DialogPortal = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Portal;\nconst DialogClose = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Close;\nconst DialogOverlay = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Overlay, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 18,\n        columnNumber: 3\n    }, undefined));\nDialogOverlay.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Overlay.displayName;\nconst DialogContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogPortal, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogOverlay, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n                lineNumber: 34,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Content, {\n                ref: ref,\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\", className),\n                ...props,\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Close, {\n                        className: \"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"sr-only\",\n                                children: \"Close\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n                lineNumber: 35,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 33,\n        columnNumber: 3\n    }, undefined));\nDialogContent.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\nconst DialogHeader = ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 text-center sm:text-left\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 57,\n        columnNumber: 3\n    }, undefined);\nDialogHeader.displayName = \"DialogHeader\";\nconst DialogFooter = ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined);\nDialogFooter.displayName = \"DialogFooter\";\nconst DialogTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Title, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-lg font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 85,\n        columnNumber: 3\n    }, undefined));\nDialogTitle.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Title.displayName;\nconst DialogDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Description, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 100,\n        columnNumber: 3\n    }, undefined));\nDialogDescription.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Description.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/dialog.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 10,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9pbnB1dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUE4QjtBQUNFO0FBS2hDLE1BQU1FLHNCQUFRRiw2Q0FBZ0IsQ0FDNUIsQ0FBQyxFQUFFSSxTQUFTLEVBQUVDLElBQUksRUFBRSxHQUFHQyxPQUFPLEVBQUVDO0lBQzlCLHFCQUNFLDhEQUFDQztRQUNDSCxNQUFNQTtRQUNORCxXQUFXSCw4Q0FBRUEsQ0FDWCxnV0FDQUc7UUFFRkcsS0FBS0E7UUFDSixHQUFHRCxLQUFLOzs7Ozs7QUFHZjtBQUVGSixNQUFNTyxXQUFXLEdBQUc7QUFFSiIsInNvdXJjZXMiOlsid2VicGFjazovL0BmbG93d2lzZS1jbG9uZS91aS8uL3NyYy9jb21wb25lbnRzL3VpL2lucHV0LnRzeD9jOTgzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmV4cG9ydCBpbnRlcmZhY2UgSW5wdXRQcm9wc1xuICBleHRlbmRzIFJlYWN0LklucHV0SFRNTEF0dHJpYnV0ZXM8SFRNTElucHV0RWxlbWVudD4ge31cblxuY29uc3QgSW5wdXQgPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxJbnB1dEVsZW1lbnQsIElucHV0UHJvcHM+KFxuICAoeyBjbGFzc05hbWUsIHR5cGUsIC4uLnByb3BzIH0sIHJlZikgPT4ge1xuICAgIHJldHVybiAoXG4gICAgICA8aW5wdXRcbiAgICAgICAgdHlwZT17dHlwZX1cbiAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICBcImZsZXggaC0xMCB3LWZ1bGwgcm91bmRlZC1tZCBib3JkZXIgYm9yZGVyLWlucHV0IGJnLWJhY2tncm91bmQgcHgtMyBweS0yIHRleHQtc20gcmluZy1vZmZzZXQtYmFja2dyb3VuZCBmaWxlOmJvcmRlci0wIGZpbGU6YmctdHJhbnNwYXJlbnQgZmlsZTp0ZXh0LXNtIGZpbGU6Zm9udC1tZWRpdW0gcGxhY2Vob2xkZXI6dGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGZvY3VzLXZpc2libGU6b3V0bGluZS1ub25lIGZvY3VzLXZpc2libGU6cmluZy0yIGZvY3VzLXZpc2libGU6cmluZy1yaW5nIGZvY3VzLXZpc2libGU6cmluZy1vZmZzZXQtMiBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgZGlzYWJsZWQ6b3BhY2l0eS01MFwiLFxuICAgICAgICAgIGNsYXNzTmFtZVxuICAgICAgICApfVxuICAgICAgICByZWY9e3JlZn1cbiAgICAgICAgey4uLnByb3BzfVxuICAgICAgLz5cbiAgICApXG4gIH1cbilcbklucHV0LmRpc3BsYXlOYW1lID0gXCJJbnB1dFwiXG5cbmV4cG9ydCB7IElucHV0IH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNuIiwiSW5wdXQiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwidHlwZSIsInByb3BzIiwicmVmIiwiaW5wdXQiLCJkaXNwbGF5TmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/label.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/label.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: () => (/* binding */ Label)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-label */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-label@2.1.7_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@radix-ui/react-label/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/../../node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst labelVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\");\nconst Label = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(labelVariants(), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\ui\\\\label.tsx\",\n        lineNumber: 15,\n        columnNumber: 3\n    }, undefined));\nLabel.displayName = _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9sYWJlbC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQThCO0FBQ3lCO0FBQ1U7QUFDakM7QUFFaEMsTUFBTUksZ0JBQWdCRiw2REFBR0EsQ0FDdkI7QUFHRixNQUFNRyxzQkFBUUwsNkNBQWdCLENBSTVCLENBQUMsRUFBRU8sU0FBUyxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQzFCLDhEQUFDUix1REFBbUI7UUFDbEJRLEtBQUtBO1FBQ0xGLFdBQVdKLDhDQUFFQSxDQUFDQyxpQkFBaUJHO1FBQzlCLEdBQUdDLEtBQUs7Ozs7OztBQUdiSCxNQUFNTSxXQUFXLEdBQUdWLHVEQUFtQixDQUFDVSxXQUFXO0FBRW5DIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGZsb3d3aXNlLWNsb25lL3VpLy4vc3JjL2NvbXBvbmVudHMvdWkvbGFiZWwudHN4PzEzZWIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCAqIGFzIExhYmVsUHJpbWl0aXZlIGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtbGFiZWxcIlxuaW1wb3J0IHsgY3ZhLCB0eXBlIFZhcmlhbnRQcm9wcyB9IGZyb20gXCJjbGFzcy12YXJpYW5jZS1hdXRob3JpdHlcIlxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5jb25zdCBsYWJlbFZhcmlhbnRzID0gY3ZhKFxuICBcInRleHQtc20gZm9udC1tZWRpdW0gbGVhZGluZy1ub25lIHBlZXItZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIHBlZXItZGlzYWJsZWQ6b3BhY2l0eS03MFwiXG4pXG5cbmNvbnN0IExhYmVsID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgUmVhY3QuRWxlbWVudFJlZjx0eXBlb2YgTGFiZWxQcmltaXRpdmUuUm9vdD4sXG4gIFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgTGFiZWxQcmltaXRpdmUuUm9vdD4gJlxuICAgIFZhcmlhbnRQcm9wczx0eXBlb2YgbGFiZWxWYXJpYW50cz5cbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPExhYmVsUHJpbWl0aXZlLlJvb3RcbiAgICByZWY9e3JlZn1cbiAgICBjbGFzc05hbWU9e2NuKGxhYmVsVmFyaWFudHMoKSwgY2xhc3NOYW1lKX1cbiAgICB7Li4ucHJvcHN9XG4gIC8+XG4pKVxuTGFiZWwuZGlzcGxheU5hbWUgPSBMYWJlbFByaW1pdGl2ZS5Sb290LmRpc3BsYXlOYW1lXG5cbmV4cG9ydCB7IExhYmVsIH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkxhYmVsUHJpbWl0aXZlIiwiY3ZhIiwiY24iLCJsYWJlbFZhcmlhbnRzIiwiTGFiZWwiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwicHJvcHMiLCJyZWYiLCJSb290IiwiZGlzcGxheU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/label.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/select.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/select.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Select: () => (/* binding */ Select),\n/* harmony export */   SelectContent: () => (/* binding */ SelectContent),\n/* harmony export */   SelectGroup: () => (/* binding */ SelectGroup),\n/* harmony export */   SelectItem: () => (/* binding */ SelectItem),\n/* harmony export */   SelectLabel: () => (/* binding */ SelectLabel),\n/* harmony export */   SelectScrollDownButton: () => (/* binding */ SelectScrollDownButton),\n/* harmony export */   SelectScrollUpButton: () => (/* binding */ SelectScrollUpButton),\n/* harmony export */   SelectSeparator: () => (/* binding */ SelectSeparator),\n/* harmony export */   SelectTrigger: () => (/* binding */ SelectTrigger),\n/* harmony export */   SelectValue: () => (/* binding */ SelectValue)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-select */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-select@2.2.5_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@radix-ui/react-select/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst Select = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst SelectGroup = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Group;\nconst SelectValue = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Value;\nconst SelectTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Trigger, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\", className),\n        ...props,\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Icon, {\n                asChild: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-4 w-4 opacity-50\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\ui\\\\select.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\ui\\\\select.tsx\",\n                lineNumber: 25,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 16,\n        columnNumber: 3\n    }, undefined));\nSelectTrigger.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Trigger.displayName;\nconst SelectScrollUpButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ScrollUpButton, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex cursor-default items-center justify-center py-1\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\ui\\\\select.tsx\",\n            lineNumber: 44,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nSelectScrollUpButton.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ScrollUpButton.displayName;\nconst SelectScrollDownButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ScrollDownButton, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex cursor-default items-center justify-center py-1\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\ui\\\\select.tsx\",\n            lineNumber: 61,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 53,\n        columnNumber: 3\n    }, undefined));\nSelectScrollDownButton.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ScrollDownButton.displayName;\nconst SelectContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, position = \"popper\", ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Portal, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Content, {\n            ref: ref,\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", position === \"popper\" && \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\", className),\n            position: position,\n            ...props,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectScrollUpButton, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\ui\\\\select.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Viewport, {\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-1\", position === \"popper\" && \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"),\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\ui\\\\select.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectScrollDownButton, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\ui\\\\select.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\ui\\\\select.tsx\",\n            lineNumber: 72,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nSelectContent.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\nconst SelectLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Label, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 103,\n        columnNumber: 3\n    }, undefined));\nSelectLabel.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Label.displayName;\nconst SelectItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Item, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\ui\\\\select.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\ui\\\\select.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\ui\\\\select.tsx\",\n                lineNumber: 123,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ItemText, {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\ui\\\\select.tsx\",\n                lineNumber: 129,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 115,\n        columnNumber: 3\n    }, undefined));\nSelectItem.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Item.displayName;\nconst SelectSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Separator, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"-mx-1 my-1 h-px bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 138,\n        columnNumber: 3\n    }, undefined));\nSelectSeparator.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Separator.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/select.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/textarea.tsx":
/*!****************************************!*\
  !*** ./src/components/ui/textarea.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Textarea: () => (/* binding */ Textarea)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Textarea = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\components\\\\ui\\\\textarea.tsx\",\n        lineNumber: 10,\n        columnNumber: 7\n    }, undefined);\n});\nTextarea.displayName = \"Textarea\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS90ZXh0YXJlYS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUE4QjtBQUNFO0FBS2hDLE1BQU1FLHlCQUFXRiw2Q0FBZ0IsQ0FDL0IsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQztJQUN4QixxQkFDRSw4REFBQ0M7UUFDQ0gsV0FBV0gsOENBQUVBLENBQ1gsd1NBQ0FHO1FBRUZFLEtBQUtBO1FBQ0osR0FBR0QsS0FBSzs7Ozs7O0FBR2Y7QUFFRkgsU0FBU00sV0FBVyxHQUFHO0FBRUoiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AZmxvd3dpc2UtY2xvbmUvdWkvLi9zcmMvY29tcG9uZW50cy91aS90ZXh0YXJlYS50c3g/NTkzMCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5leHBvcnQgaW50ZXJmYWNlIFRleHRhcmVhUHJvcHNcbiAgZXh0ZW5kcyBSZWFjdC5UZXh0YXJlYUhUTUxBdHRyaWJ1dGVzPEhUTUxUZXh0QXJlYUVsZW1lbnQ+IHt9XG5cbmNvbnN0IFRleHRhcmVhID0gUmVhY3QuZm9yd2FyZFJlZjxIVE1MVGV4dEFyZWFFbGVtZW50LCBUZXh0YXJlYVByb3BzPihcbiAgKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IHtcbiAgICByZXR1cm4gKFxuICAgICAgPHRleHRhcmVhXG4gICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgXCJmbGV4IG1pbi1oLVs4MHB4XSB3LWZ1bGwgcm91bmRlZC1tZCBib3JkZXIgYm9yZGVyLWlucHV0IGJnLWJhY2tncm91bmQgcHgtMyBweS0yIHRleHQtc20gcmluZy1vZmZzZXQtYmFja2dyb3VuZCBwbGFjZWhvbGRlcjp0ZXh0LW11dGVkLWZvcmVncm91bmQgZm9jdXMtdmlzaWJsZTpvdXRsaW5lLW5vbmUgZm9jdXMtdmlzaWJsZTpyaW5nLTIgZm9jdXMtdmlzaWJsZTpyaW5nLXJpbmcgZm9jdXMtdmlzaWJsZTpyaW5nLW9mZnNldC0yIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBkaXNhYmxlZDpvcGFjaXR5LTUwXCIsXG4gICAgICAgICAgY2xhc3NOYW1lXG4gICAgICAgICl9XG4gICAgICAgIHJlZj17cmVmfVxuICAgICAgICB7Li4ucHJvcHN9XG4gICAgICAvPlxuICAgIClcbiAgfVxuKVxuVGV4dGFyZWEuZGlzcGxheU5hbWUgPSBcIlRleHRhcmVhXCJcblxuZXhwb3J0IHsgVGV4dGFyZWEgfVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY24iLCJUZXh0YXJlYSIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJwcm9wcyIsInJlZiIsInRleHRhcmVhIiwiZGlzcGxheU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/textarea.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDateTime: () => (/* binding */ formatDateTime),\n/* harmony export */   formatDuration: () => (/* binding */ formatDuration),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   truncate: () => (/* binding */ truncate)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/../../node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/../../node_modules/.pnpm/tailwind-merge@2.6.0/node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatDate(date) {\n    return new Date(date).toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"short\",\n        day: \"numeric\"\n    });\n}\nfunction formatDateTime(date) {\n    return new Date(date).toLocaleString(\"en-US\", {\n        year: \"numeric\",\n        month: \"short\",\n        day: \"numeric\",\n        hour: \"2-digit\",\n        minute: \"2-digit\"\n    });\n}\nfunction formatDuration(ms) {\n    if (ms < 1000) {\n        return `${ms}ms`;\n    }\n    if (ms < 60000) {\n        return `${(ms / 1000).toFixed(1)}s`;\n    }\n    return `${(ms / 60000).toFixed(1)}m`;\n}\nfunction truncate(str, length) {\n    if (str.length <= length) return str;\n    return str.slice(0, length) + \"...\";\n}\nfunction generateId() {\n    return Math.random().toString(36).substr(2, 9);\n}\nfunction debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\nfunction throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"1f322e870d8b\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGZsb3d3aXNlLWNsb25lL3VpLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz81YWFhIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMWYzMjJlODcwZDhiXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/flows/page.tsx":
/*!********************************!*\
  !*** ./src/app/flows/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\agent_framework\packages\ui\src\app\flows\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers */ \"(rsc)/./src/components/providers.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/../../node_modules/.pnpm/react-hot-toast@2.5.2_react-dom@18.3.1_react@18.3.1/node_modules/react-hot-toast/dist/index.mjs\");\n\n\n\n\n\nconst metadata = {\n    title: \"Flowwise Clone - Visual AI Agent Builder\",\n    description: \"Build AI agents, chatbots, and multi-agent systems visually through a drag-and-drop interface.\",\n    keywords: [\n        \"AI\",\n        \"chatbot\",\n        \"agent\",\n        \"visual builder\",\n        \"no-code\",\n        \"LLM\"\n    ],\n    authors: [\n        {\n            name: \"Flowwise Clone Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\",\n    themeColor: \"#3B82F6\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                        position: \"top-right\",\n                        toastOptions: {\n                            duration: 4000,\n                            style: {\n                                background: \"hsl(var(--card))\",\n                                color: \"hsl(var(--card-foreground))\",\n                                border: \"1px solid hsl(var(--border))\"\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 26,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\agent_framework\\\\packages\\\\ui\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/providers.tsx":
/*!**************************************!*\
  !*** ./src/components/providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\agent_framework\packages\ui\src\components\providers.tsx#Providers`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@tanstack+query-devtools@5.81.2","vendor-chunks/next@14.2.30_react-dom@18.3.1_react@18.3.1","vendor-chunks/tailwind-merge@2.6.0","vendor-chunks/@tanstack+query-core@5.81.5","vendor-chunks/lucide-react@0.294.0_react@18.3.1","vendor-chunks/react-hot-toast@2.5.2_react-dom@18.3.1_react@18.3.1","vendor-chunks/@tanstack+react-query-devtools@5.81.5_@tanstack+react-query@5.81.5_react@18.3.1","vendor-chunks/@radix-ui+react-slot@1.2.3_@types+react@18.3.23_react@18.3.1","vendor-chunks/next-themes@0.2.1_next@14.2.30_react-dom@18.3.1_react@18.3.1","vendor-chunks/class-variance-authority@0.7.1","vendor-chunks/goober@2.1.16_csstype@3.1.3","vendor-chunks/@swc+helpers@0.5.5","vendor-chunks/@radix-ui+react-compose-refs@1.1.2_@types+react@18.3.23_react@18.3.1","vendor-chunks/@tanstack+react-query@5.81.5_react@18.3.1","vendor-chunks/clsx@2.1.1","vendor-chunks/zod@3.25.67","vendor-chunks/@radix-ui+react-select@2.2.5_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1","vendor-chunks/@floating-ui+core@1.7.2","vendor-chunks/@floating-ui+dom@1.7.2","vendor-chunks/@radix-ui+react-collection@1.1.7_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1","vendor-chunks/tslib@2.8.1","vendor-chunks/react-remove-scroll@2.7.1_@types+react@18.3.23_react@18.3.1","vendor-chunks/@radix-ui+react-popper@1.2.7_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1","vendor-chunks/@floating-ui+react-dom@2.1.4_react-dom@18.3.1_react@18.3.1","vendor-chunks/@floating-ui+utils@0.2.10","vendor-chunks/@radix-ui+react-dismissable-layer@1.1.10_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1","vendor-chunks/@radix-ui+react-focus-scope@1.1.7_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1","vendor-chunks/aria-hidden@1.2.6","vendor-chunks/@radix-ui+react-presence@1.1.4_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1","vendor-chunks/react-remove-scroll-bar@2.3.8_@types+react@18.3.23_react@18.3.1","vendor-chunks/@radix-ui+react-use-controllable-state@1.2.2_@types+react@18.3.23_react@18.3.1","vendor-chunks/use-callback-ref@1.3.3_@types+react@18.3.23_react@18.3.1","vendor-chunks/@radix-ui+react-context@1.1.2_@types+react@18.3.23_react@18.3.1","vendor-chunks/use-sidecar@1.1.3_@types+react@18.3.23_react@18.3.1","vendor-chunks/react-style-singleton@2.2.3_@types+react@18.3.23_react@18.3.1","vendor-chunks/@radix-ui+react-use-size@1.1.1_@types+react@18.3.23_react@18.3.1","vendor-chunks/@radix-ui+react-focus-guards@1.1.2_@types+react@18.3.23_react@18.3.1","vendor-chunks/@radix-ui+react-primitive@2.1.3_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1","vendor-chunks/@radix-ui+react-portal@1.1.9_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1","vendor-chunks/@radix-ui+react-visually-hidden@1.2.3_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1","vendor-chunks/@radix-ui+react-use-effect-event@0.0.2_@types+react@18.3.23_react@18.3.1","vendor-chunks/@radix-ui+react-label@2.1.7_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1","vendor-chunks/@radix-ui+react-use-escape-keydown@1.1.1_@types+react@18.3.23_react@18.3.1","vendor-chunks/@radix-ui+react-arrow@1.1.7_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1","vendor-chunks/@radix-ui+react-direction@1.1.1_@types+react@18.3.23_react@18.3.1","vendor-chunks/@radix-ui+react-id@1.1.1_@types+react@18.3.23_react@18.3.1","vendor-chunks/@radix-ui+react-use-previous@1.1.1_@types+react@18.3.23_react@18.3.1","vendor-chunks/@radix-ui+primitive@1.1.2","vendor-chunks/@radix-ui+react-use-callback-ref@1.1.1_@types+react@18.3.23_react@18.3.1","vendor-chunks/get-nonce@1.0.1","vendor-chunks/@radix-ui+react-use-layout-effect@1.1.1_@types+react@18.3.23_react@18.3.1","vendor-chunks/@radix-ui+number@1.1.1","vendor-chunks/@radix-ui+react-dialog@1.1.14_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1"], () => (__webpack_exec__("(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fflows%2Fpage&page=%2Fflows%2Fpage&appPaths=%2Fflows%2Fpage&pagePath=private-next-app-dir%2Fflows%2Fpage.tsx&appDir=C%3A%5CUsers%5CAgaran%5CDocuments%5Caugment-projects%5Cagent_framework%5Cpackages%5Cui%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAgaran%5CDocuments%5Caugment-projects%5Cagent_framework%5Cpackages%5Cui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();