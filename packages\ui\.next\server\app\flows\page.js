(()=>{var e={};e.id=805,e.ids=[805],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},6350:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d}),s(7512),s(2367),s(5999);var a=s(7188),r=s(8659),n=s(2067),i=s.n(n),l=s(9786),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let d=["",{children:["flows",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,7512)),"C:\\Users\\<USER>\\Documents\\augment-projects\\agent_framework\\packages\\ui\\src\\app\\flows\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,2367)),"C:\\Users\\<USER>\\Documents\\augment-projects\\agent_framework\\packages\\ui\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,5999,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Documents\\augment-projects\\agent_framework\\packages\\ui\\src\\app\\flows\\page.tsx"],m="/flows/page",u={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/flows/page",pathname:"/flows",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},294:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,3143,23)),Promise.resolve().then(s.t.bind(s,3627,23)),Promise.resolve().then(s.t.bind(s,8958,23)),Promise.resolve().then(s.t.bind(s,9501,23)),Promise.resolve().then(s.t.bind(s,543,23)),Promise.resolve().then(s.t.bind(s,8789,23))},1459:(e,t,s)=>{Promise.resolve().then(s.bind(s,6600)),Promise.resolve().then(s.bind(s,5682))},4683:(e,t,s)=>{Promise.resolve().then(s.bind(s,8611))},8611:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>eF});var a=s(7491),r=s(6364),n=s(2812),i=s(2371),l=s(3764),o=s(3509);let d=(0,o.Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]),c=(0,o.Z)("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]]),m=(0,o.Z)("List",[["line",{x1:"8",x2:"21",y1:"6",y2:"6",key:"7ey8pc"}],["line",{x1:"8",x2:"21",y1:"12",y2:"12",key:"rjfblc"}],["line",{x1:"8",x2:"21",y1:"18",y2:"18",key:"c3b1m8"}],["line",{x1:"3",x2:"3.01",y1:"6",y2:"6",key:"1g7gq3"}],["line",{x1:"3",x2:"3.01",y1:"12",y2:"12",key:"1pjlvk"}],["line",{x1:"3",x2:"3.01",y1:"18",y2:"18",key:"28t2mc"}]]);var u=s(3651),x=s(5532);let p=(0,o.Z)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]]),f=(0,o.Z)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]]),g=(0,o.Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]]),h=(0,o.Z)("Layers",[["path",{d:"m12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83Z",key:"8b97xw"}],["path",{d:"m22 17.65-9.17 4.16a2 2 0 0 1-1.66 0L2 17.65",key:"dd6zsq"}],["path",{d:"m22 12.65-9.17 4.16a2 2 0 0 1-1.66 0L2 12.65",key:"ep9fru"}]]);var y=s(3407),j=s(4669),v=s(5297),b=s(3594),w=s(7107);let N={active:"bg-green-100 text-green-800",draft:"bg-yellow-100 text-yellow-800",inactive:"bg-gray-100 text-gray-800"},C={active:p,draft:f,inactive:g};function k({flow:e,viewMode:t}){let s=C[e.status];return"list"===t?a.jsx(u.Zb,{className:"hover:shadow-md transition-shadow",children:a.jsx(u.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4 flex-1",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[a.jsx("h3",{className:"font-semibold text-lg",children:e.name}),(0,a.jsxs)(x.C,{className:N[e.status],children:[a.jsx(s,{className:"h-3 w-3 mr-1"}),e.status]})]}),a.jsx("p",{className:"text-gray-600 text-sm mb-2",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx(h,{className:"h-4 w-4 mr-1"}),e.nodeCount," nodes"]}),(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx(g,{className:"h-4 w-4 mr-1"}),"Updated ",(0,w.p6)(e.updatedAt)]})]})]}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-1",children:[e.tags.slice(0,3).map(e=>a.jsx(x.C,{variant:"secondary",className:"text-xs",children:e},e)),e.tags.length>3&&(0,a.jsxs)(x.C,{variant:"secondary",className:"text-xs",children:["+",e.tags.length-3]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 ml-4",children:[a.jsx(n.z,{size:"sm",asChild:!0,children:(0,a.jsxs)(b.default,{href:`/flows/${e.id}`,children:[a.jsx(f,{className:"h-4 w-4 mr-1"}),"Edit"]})}),(0,a.jsxs)(n.z,{size:"sm",variant:"outline",children:[a.jsx(y.Z,{className:"h-4 w-4 mr-1"}),"Test"]}),a.jsx(n.z,{size:"sm",variant:"ghost",children:a.jsx(j.Z,{className:"h-4 w-4"})})]})]})})}):(0,a.jsxs)(u.Zb,{className:"hover:shadow-md transition-shadow",children:[a.jsx(u.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[a.jsx(u.ll,{className:"text-lg",children:e.name}),(0,a.jsxs)(x.C,{className:N[e.status],children:[a.jsx(s,{className:"h-3 w-3 mr-1"}),e.status]})]}),a.jsx(u.SZ,{className:"line-clamp-2",children:e.description})]}),a.jsx(n.z,{size:"sm",variant:"ghost",children:a.jsx(j.Z,{className:"h-4 w-4"})})]})}),(0,a.jsxs)(u.aY,{className:"pt-0",children:[a.jsx("div",{className:"flex items-center justify-between mb-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx(h,{className:"h-4 w-4 mr-1"}),e.nodeCount," nodes"]}),(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx(g,{className:"h-4 w-4 mr-1"}),(0,w.p6)(e.updatedAt)]})]})}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-1 mb-4",children:[e.tags.slice(0,3).map(e=>a.jsx(x.C,{variant:"secondary",className:"text-xs",children:e},e)),e.tags.length>3&&(0,a.jsxs)(x.C,{variant:"secondary",className:"text-xs",children:["+",e.tags.length-3]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(n.z,{size:"sm",className:"flex-1",asChild:!0,children:(0,a.jsxs)(b.default,{href:`/flows/${e.id}`,children:[a.jsx(f,{className:"h-4 w-4 mr-1"}),"Edit"]})}),a.jsx(n.z,{size:"sm",variant:"outline",children:a.jsx(y.Z,{className:"h-4 w-4"})}),a.jsx(n.z,{size:"sm",variant:"outline",children:a.jsx(v.Z,{className:"h-4 w-4"})})]})]})]})}var R=s(1082),z=s(5460),A=s(4578),D=s(1936),_=s(6295),Z=s(6958),P=s(8687),F=s(5353),I=s(3485),M=s(40),O=s(7044),S=s(1288),T=s(9830),E=s(4582),L=s(8425),q=s(3205),B=s(9128),V=s(7723),W="Dialog",[$,G]=(0,P.b)(W),[U,H]=$(W),Y=e=>{let{__scopeDialog:t,children:s,open:n,defaultOpen:i,onOpenChange:l,modal:o=!0}=e,d=r.useRef(null),c=r.useRef(null),[m,u]=(0,I.T)({prop:n,defaultProp:i??!1,onChange:l,caller:W});return(0,a.jsx)(U,{scope:t,triggerRef:d,contentRef:c,contentId:(0,F.M)(),titleId:(0,F.M)(),descriptionId:(0,F.M)(),open:m,onOpenChange:u,onOpenToggle:r.useCallback(()=>u(e=>!e),[u]),modal:o,children:s})};Y.displayName=W;var K="DialogTrigger";r.forwardRef((e,t)=>{let{__scopeDialog:s,...r}=e,n=H(K,s),i=(0,Z.e)(t,n.triggerRef);return(0,a.jsx)(E.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":n.open,"aria-controls":n.contentId,"data-state":eg(n.open),...r,ref:i,onClick:(0,_.M)(e.onClick,n.onOpenToggle)})}).displayName=K;var Q="DialogPortal",[X,J]=$(Q,{forceMount:void 0}),ee=e=>{let{__scopeDialog:t,forceMount:s,children:n,container:i}=e,l=H(Q,t);return(0,a.jsx)(X,{scope:t,forceMount:s,children:r.Children.map(n,e=>(0,a.jsx)(T.z,{present:s||l.open,children:(0,a.jsx)(S.h,{asChild:!0,container:i,children:e})}))})};ee.displayName=Q;var et="DialogOverlay",es=r.forwardRef((e,t)=>{let s=J(et,e.__scopeDialog),{forceMount:r=s.forceMount,...n}=e,i=H(et,e.__scopeDialog);return i.modal?(0,a.jsx)(T.z,{present:r||i.open,children:(0,a.jsx)(er,{...n,ref:t})}):null});es.displayName=et;var ea=(0,V.Z8)("DialogOverlay.RemoveScroll"),er=r.forwardRef((e,t)=>{let{__scopeDialog:s,...r}=e,n=H(et,s);return(0,a.jsx)(q.Z,{as:ea,allowPinchZoom:!0,shards:[n.contentRef],children:(0,a.jsx)(E.WV.div,{"data-state":eg(n.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),en="DialogContent",ei=r.forwardRef((e,t)=>{let s=J(en,e.__scopeDialog),{forceMount:r=s.forceMount,...n}=e,i=H(en,e.__scopeDialog);return(0,a.jsx)(T.z,{present:r||i.open,children:i.modal?(0,a.jsx)(el,{...n,ref:t}):(0,a.jsx)(eo,{...n,ref:t})})});ei.displayName=en;var el=r.forwardRef((e,t)=>{let s=H(en,e.__scopeDialog),n=r.useRef(null),i=(0,Z.e)(t,s.contentRef,n);return r.useEffect(()=>{let e=n.current;if(e)return(0,B.Ry)(e)},[]),(0,a.jsx)(ed,{...e,ref:i,trapFocus:s.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,_.M)(e.onCloseAutoFocus,e=>{e.preventDefault(),s.triggerRef.current?.focus()}),onPointerDownOutside:(0,_.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,s=0===t.button&&!0===t.ctrlKey;(2===t.button||s)&&e.preventDefault()}),onFocusOutside:(0,_.M)(e.onFocusOutside,e=>e.preventDefault())})}),eo=r.forwardRef((e,t)=>{let s=H(en,e.__scopeDialog),n=r.useRef(!1),i=r.useRef(!1);return(0,a.jsx)(ed,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(n.current||s.triggerRef.current?.focus(),t.preventDefault()),n.current=!1,i.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(n.current=!0,"pointerdown"!==t.detail.originalEvent.type||(i.current=!0));let a=t.target;s.triggerRef.current?.contains(a)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),ed=r.forwardRef((e,t)=>{let{__scopeDialog:s,trapFocus:n,onOpenAutoFocus:i,onCloseAutoFocus:l,...o}=e,d=H(en,s),c=r.useRef(null),m=(0,Z.e)(t,c);return(0,L.EW)(),(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(O.M,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:i,onUnmountAutoFocus:l,children:(0,a.jsx)(M.XB,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":eg(d.open),...o,ref:m,onDismiss:()=>d.onOpenChange(!1)})}),(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(ev,{titleId:d.titleId}),(0,a.jsx)(eb,{contentRef:c,descriptionId:d.descriptionId})]})]})}),ec="DialogTitle",em=r.forwardRef((e,t)=>{let{__scopeDialog:s,...r}=e,n=H(ec,s);return(0,a.jsx)(E.WV.h2,{id:n.titleId,...r,ref:t})});em.displayName=ec;var eu="DialogDescription",ex=r.forwardRef((e,t)=>{let{__scopeDialog:s,...r}=e,n=H(eu,s);return(0,a.jsx)(E.WV.p,{id:n.descriptionId,...r,ref:t})});ex.displayName=eu;var ep="DialogClose",ef=r.forwardRef((e,t)=>{let{__scopeDialog:s,...r}=e,n=H(ep,s);return(0,a.jsx)(E.WV.button,{type:"button",...r,ref:t,onClick:(0,_.M)(e.onClick,()=>n.onOpenChange(!1))})});function eg(e){return e?"open":"closed"}ef.displayName=ep;var eh="DialogTitleWarning",[ey,ej]=(0,P.k)(eh,{contentName:en,titleName:ec,docsSlug:"dialog"}),ev=({titleId:e})=>{let t=ej(eh),s=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return r.useEffect(()=>{e&&!document.getElementById(e)&&console.error(s)},[s,e]),null},eb=({contentRef:e,descriptionId:t})=>{let s=ej("DialogDescriptionWarning"),a=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${s.contentName}}.`;return r.useEffect(()=>{let s=e.current?.getAttribute("aria-describedby");t&&s&&!document.getElementById(t)&&console.warn(a)},[a,e,t]),null},ew=s(7637);let eN=r.forwardRef(({className:e,...t},s)=>a.jsx(es,{ref:s,className:(0,w.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));eN.displayName=es.displayName;let eC=r.forwardRef(({className:e,children:t,...s},r)=>(0,a.jsxs)(ee,{children:[a.jsx(eN,{}),(0,a.jsxs)(ei,{ref:r,className:(0,w.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...s,children:[t,(0,a.jsxs)(ef,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[a.jsx(ew.Z,{className:"h-4 w-4"}),a.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));eC.displayName=ei.displayName;let ek=({className:e,...t})=>a.jsx("div",{className:(0,w.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});ek.displayName="DialogHeader";let eR=({className:e,...t})=>a.jsx("div",{className:(0,w.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});eR.displayName="DialogFooter";let ez=r.forwardRef(({className:e,...t},s)=>a.jsx(em,{ref:s,className:(0,w.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));ez.displayName=em.displayName;let eA=r.forwardRef(({className:e,...t},s)=>a.jsx(ex,{ref:s,className:(0,w.cn)("text-sm text-muted-foreground",e),...t}));eA.displayName=ex.displayName;var eD=s(6464);let e_=[{id:"blank",name:"Blank Flow",description:"Start with an empty canvas",category:"Basic"},{id:"chatbot",name:"Chatbot",description:"Simple conversational AI",category:"Chat"},{id:"rag",name:"RAG Assistant",description:"Document-based Q&A system",category:"Knowledge"},{id:"multi-agent",name:"Multi-Agent System",description:"Coordinated agent workflow",category:"Advanced"}];function eZ({open:e,onOpenChange:t}){let s=(0,R.useRouter)(),[l,o]=(0,r.useState)({name:"",description:"",template:"blank",tags:[]}),[d,c]=(0,r.useState)(""),[m,u]=(0,r.useState)(!1),p=async e=>{e.preventDefault(),u(!0);try{let e=(0,w.Ox)();await new Promise(e=>setTimeout(e,1e3)),s.push(`/flows/${e}`),t(!1),o({name:"",description:"",template:"blank",tags:[]})}catch(e){console.error("Failed to create flow:",e)}finally{u(!1)}},f=()=>{d.trim()&&!l.tags.includes(d.trim())&&(o(e=>({...e,tags:[...e.tags,d.trim()]})),c(""))},g=e=>{o(t=>({...t,tags:t.tags.filter(t=>t!==e)}))},h=e_.find(e=>e.id===l.template);return a.jsx(Y,{open:e,onOpenChange:t,children:(0,a.jsxs)(eC,{className:"sm:max-w-[500px]",children:[(0,a.jsxs)(ek,{children:[a.jsx(ez,{children:"Create New Flow"}),a.jsx(eA,{children:"Set up your new AI workflow. You can always change these settings later."})]}),(0,a.jsxs)("form",{onSubmit:p,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(D._,{htmlFor:"name",children:"Flow Name"}),a.jsx(z.I,{id:"name",placeholder:"My Awesome Flow",value:l.name,onChange:e=>o(t=>({...t,name:e.target.value})),required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(D._,{htmlFor:"description",children:"Description"}),a.jsx(A.g,{id:"description",placeholder:"Describe what this flow does...",value:l.description,onChange:e=>o(t=>({...t,description:e.target.value})),rows:3})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(D._,{htmlFor:"template",children:"Template"}),(0,a.jsxs)(eD.Ph,{value:l.template,onValueChange:e=>o(t=>({...t,template:e})),children:[a.jsx(eD.i4,{children:a.jsx(eD.ki,{})}),a.jsx(eD.Bw,{children:e_.map(e=>a.jsx(eD.Ql,{value:e.id,children:(0,a.jsxs)("div",{className:"flex flex-col items-start",children:[a.jsx("span",{className:"font-medium",children:e.name}),a.jsx("span",{className:"text-sm text-gray-500",children:e.description})]})},e.id))})]}),h&&(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Category: ",h.category]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(D._,{children:"Tags"}),a.jsx("div",{className:"flex flex-wrap gap-2 mb-2",children:l.tags.map(e=>(0,a.jsxs)(x.C,{variant:"secondary",className:"flex items-center gap-1",children:[e,a.jsx("button",{type:"button",onClick:()=>g(e),className:"ml-1 hover:bg-gray-200 rounded-full p-0.5",children:a.jsx(ew.Z,{className:"h-3 w-3"})})]},e))}),(0,a.jsxs)("div",{className:"flex gap-2",children:[a.jsx(z.I,{placeholder:"Add a tag...",value:d,onChange:e=>c(e.target.value),onKeyPress:e=>{"Enter"===e.key&&(e.preventDefault(),f())}}),a.jsx(n.z,{type:"button",variant:"outline",size:"sm",onClick:f,children:a.jsx(i.Z,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)(eR,{children:[a.jsx(n.z,{type:"button",variant:"outline",onClick:()=>t(!1),disabled:m,children:"Cancel"}),a.jsx(n.z,{type:"submit",disabled:!l.name.trim()||m,children:m?"Creating...":"Create Flow"})]})]})]})})}let eP=[{id:"1",name:"Customer Support Bot",description:"AI-powered customer support chatbot with knowledge base integration",status:"active",createdAt:"2024-01-15T10:00:00Z",updatedAt:"2024-01-20T15:30:00Z",nodeCount:8,tags:["chatbot","support","rag"]},{id:"2",name:"Document Analyzer",description:"Multi-agent system for analyzing and summarizing documents",status:"draft",createdAt:"2024-01-18T09:15:00Z",updatedAt:"2024-01-18T16:45:00Z",nodeCount:12,tags:["document","analysis","multi-agent"]},{id:"3",name:"Code Review Assistant",description:"AI agent that reviews code and provides suggestions",status:"active",createdAt:"2024-01-10T14:20:00Z",updatedAt:"2024-01-22T11:10:00Z",nodeCount:6,tags:["code","review","development"]}];function eF(){let[e,t]=(0,r.useState)(""),[s,o]=(0,r.useState)("grid"),[u,x]=(0,r.useState)(!1),p=eP.filter(t=>t.name.toLowerCase().includes(e.toLowerCase())||t.description.toLowerCase().includes(e.toLowerCase())||t.tags.some(t=>t.toLowerCase().includes(e.toLowerCase())));return(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Flows"}),a.jsx("p",{className:"text-gray-600 mt-1",children:"Build and manage your AI workflows"})]}),(0,a.jsxs)(n.z,{onClick:()=>x(!0),children:[a.jsx(i.Z,{className:"h-4 w-4 mr-2"}),"Create Flow"]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 mb-6",children:[(0,a.jsxs)("div",{className:"relative flex-1 max-w-md",children:[a.jsx(l.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),a.jsx("input",{type:"text",placeholder:"Search flows...",value:e,onChange:e=>t(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,a.jsxs)(n.z,{variant:"outline",size:"sm",children:[a.jsx(d,{className:"h-4 w-4 mr-2"}),"Filter"]}),(0,a.jsxs)("div",{className:"flex items-center border border-gray-300 rounded-lg",children:[a.jsx(n.z,{variant:"grid"===s?"default":"ghost",size:"sm",onClick:()=>o("grid"),className:"rounded-r-none",children:a.jsx(c,{className:"h-4 w-4"})}),a.jsx(n.z,{variant:"list"===s?"default":"ghost",size:"sm",onClick:()=>o("list"),className:"rounded-l-none",children:a.jsx(m,{className:"h-4 w-4"})})]})]}),0===p.length&&""===e&&(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx("div",{className:"mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4",children:a.jsx(i.Z,{className:"h-8 w-8 text-gray-400"})}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No flows yet"}),a.jsx("p",{className:"text-gray-600 mb-4",children:"Get started by creating your first AI workflow"}),(0,a.jsxs)(n.z,{onClick:()=>x(!0),children:[a.jsx(i.Z,{className:"h-4 w-4 mr-2"}),"Create Your First Flow"]})]}),0===p.length&&""!==e&&(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx("div",{className:"mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4",children:a.jsx(l.Z,{className:"h-8 w-8 text-gray-400"})}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No flows found"}),a.jsx("p",{className:"text-gray-600",children:"Try adjusting your search terms"})]}),p.length>0&&a.jsx("div",{className:"grid"===s?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6":"space-y-4",children:p.map(e=>a.jsx(k,{flow:e,viewMode:s},e.id))}),a.jsx(eZ,{open:u,onOpenChange:x})]})}},5682:(e,t,s)=>{"use strict";s.d(t,{Providers:()=>d});var a=s(7491),r=s(843),n=s(5668),i=s(675),l=s(2781),o=s(6364);function d({children:e}){let[t]=(0,o.useState)(()=>new r.S({defaultOptions:{queries:{staleTime:6e4,retry:1}}}));return(0,a.jsxs)(n.aH,{client:t,children:[a.jsx(l.f,{attribute:"class",defaultTheme:"light",enableSystem:!0,disableTransitionOnChange:!0,children:e}),a.jsx(i.t,{initialIsOpen:!1})]})}},5532:(e,t,s)=>{"use strict";s.d(t,{C:()=>l});var a=s(7491);s(6364);var r=s(4295),n=s(7107);let i=(0,r.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,...s}){return a.jsx("div",{className:(0,n.cn)(i({variant:t}),e),...s})}},2812:(e,t,s)=>{"use strict";s.d(t,{z:()=>d});var a=s(7491),r=s(6364),n=s(7723),i=s(4295),l=s(7107);let o=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=r.forwardRef(({className:e,variant:t,size:s,asChild:r=!1,...i},d)=>{let c=r?n.g7:"button";return a.jsx(c,{className:(0,l.cn)(o({variant:t,size:s,className:e})),ref:d,...i})});d.displayName="Button"},3651:(e,t,s)=>{"use strict";s.d(t,{Ol:()=>l,SZ:()=>d,Zb:()=>i,aY:()=>c,ll:()=>o});var a=s(7491),r=s(6364),n=s(7107);let i=r.forwardRef(({className:e,...t},s)=>a.jsx("div",{ref:s,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));i.displayName="Card";let l=r.forwardRef(({className:e,...t},s)=>a.jsx("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...t}));l.displayName="CardHeader";let o=r.forwardRef(({className:e,...t},s)=>a.jsx("h3",{ref:s,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));o.displayName="CardTitle";let d=r.forwardRef(({className:e,...t},s)=>a.jsx("p",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",e),...t}));d.displayName="CardDescription";let c=r.forwardRef(({className:e,...t},s)=>a.jsx("div",{ref:s,className:(0,n.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent",r.forwardRef(({className:e,...t},s)=>a.jsx("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},5460:(e,t,s)=>{"use strict";s.d(t,{I:()=>i});var a=s(7491),r=s(6364),n=s(7107);let i=r.forwardRef(({className:e,type:t,...s},r)=>a.jsx("input",{type:t,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...s}));i.displayName="Input"},1936:(e,t,s)=>{"use strict";s.d(t,{_:()=>d});var a=s(7491),r=s(6364),n=s(2245),i=s(4295),l=s(7107);let o=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=r.forwardRef(({className:e,...t},s)=>a.jsx(n.f,{ref:s,className:(0,l.cn)(o(),e),...t}));d.displayName=n.f.displayName},6464:(e,t,s)=>{"use strict";s.d(t,{Bw:()=>f,Ph:()=>c,Ql:()=>g,i4:()=>u,ki:()=>m});var a=s(7491),r=s(6364),n=s(7670),i=s(6e3),l=s(5990),o=s(6532),d=s(7107);let c=n.fC;n.ZA;let m=n.B4,u=r.forwardRef(({className:e,children:t,...s},r)=>(0,a.jsxs)(n.xz,{ref:r,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...s,children:[t,a.jsx(n.JO,{asChild:!0,children:a.jsx(i.Z,{className:"h-4 w-4 opacity-50"})})]}));u.displayName=n.xz.displayName;let x=r.forwardRef(({className:e,...t},s)=>a.jsx(n.u_,{ref:s,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:a.jsx(l.Z,{className:"h-4 w-4"})}));x.displayName=n.u_.displayName;let p=r.forwardRef(({className:e,...t},s)=>a.jsx(n.$G,{ref:s,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:a.jsx(i.Z,{className:"h-4 w-4"})}));p.displayName=n.$G.displayName;let f=r.forwardRef(({className:e,children:t,position:s="popper",...r},i)=>a.jsx(n.h_,{children:(0,a.jsxs)(n.VY,{ref:i,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...r,children:[a.jsx(x,{}),a.jsx(n.l_,{className:(0,d.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),a.jsx(p,{})]})}));f.displayName=n.VY.displayName,r.forwardRef(({className:e,...t},s)=>a.jsx(n.__,{ref:s,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=n.__.displayName;let g=r.forwardRef(({className:e,children:t,...s},r)=>(0,a.jsxs)(n.ck,{ref:r,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...s,children:[a.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:a.jsx(n.wU,{children:a.jsx(o.Z,{className:"h-4 w-4"})})}),a.jsx(n.eT,{children:t})]}));g.displayName=n.ck.displayName,r.forwardRef(({className:e,...t},s)=>a.jsx(n.Z0,{ref:s,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=n.Z0.displayName},4578:(e,t,s)=>{"use strict";s.d(t,{g:()=>i});var a=s(7491),r=s(6364),n=s(7107);let i=r.forwardRef(({className:e,...t},s)=>a.jsx("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:s,...t}));i.displayName="Textarea"},7107:(e,t,s)=>{"use strict";s.d(t,{Ox:()=>l,cn:()=>n,p6:()=>i});var a=s(7672),r=s(2154);function n(...e){return(0,r.m6)((0,a.W)(e))}function i(e){return new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})}function l(){return Math.random().toString(36).substr(2,9)}},7512:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(4798).createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\agent_framework\packages\ui\src\app\flows\page.tsx#default`)},2367:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d,metadata:()=>o});var a=s(4519),r=s(1283),n=s.n(r);s(1369);let i=(0,s(4798).createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\agent_framework\packages\ui\src\components\providers.tsx#Providers`);var l=s(6533);let o={title:"Flowwise Clone - Visual AI Agent Builder",description:"Build AI agents, chatbots, and multi-agent systems visually through a drag-and-drop interface.",keywords:["AI","chatbot","agent","visual builder","no-code","LLM"],authors:[{name:"Flowwise Clone Team"}],viewport:"width=device-width, initial-scale=1",themeColor:"#3B82F6"};function d({children:e}){return a.jsx("html",{lang:"en",suppressHydrationWarning:!0,children:a.jsx("body",{className:n().className,children:(0,a.jsxs)(i,{children:[e,a.jsx(l.x7,{position:"top-right",toastOptions:{duration:4e3,style:{background:"hsl(var(--card))",color:"hsl(var(--card-foreground))",border:"1px solid hsl(var(--border))"}}})]})})})}},1369:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[903,763,864,594],()=>s(6350));module.exports=a})();