import { z } from 'zod';
import { BaseEntitySchema, ExecutionStatus } from './common';

// Agent type enum
export enum AgentType {
  CONVERSATIONAL = 'conversational',
  REACT = 'react',
  PLAN_AND_EXECUTE = 'planAndExecute',
  OPENAI_FUNCTIONS = 'openAiFunctions',
  STRUCTURED_CHAT = 'structuredChat',
  CUSTOM = 'custom'
}

// Agent schema
export const AgentSchema = BaseEntitySchema.extend({
  name: z.string(),
  description: z.string().optional(),
  type: z.nativeEnum(AgentType),
  llmId: z.string().uuid(),
  tools: z.array(z.string().uuid()),
  memory: z.string().uuid().optional(),
  systemMessage: z.string().optional(),
  maxIterations: z.number().default(10),
  temperature: z.number().min(0).max(2).default(0.7),
  topP: z.number().min(0).max(1).default(1),
  frequencyPenalty: z.number().min(-2).max(2).default(0),
  presencePenalty: z.number().min(-2).max(2).default(0),
  stopSequences: z.array(z.string()).default([]),
  userId: z.string().uuid(),
  workspaceId: z.string().uuid().optional(),
  isActive: z.boolean().default(true),
  metadata: z.record(z.any()).optional()
});

export type Agent = z.infer<typeof AgentSchema>;

// Multi-agent system schema
export const MultiAgentSystemSchema = BaseEntitySchema.extend({
  name: z.string(),
  description: z.string().optional(),
  agents: z.array(z.string().uuid()),
  orchestrator: z.string().uuid().optional(),
  workflow: z.record(z.any()),
  userId: z.string().uuid(),
  workspaceId: z.string().uuid().optional(),
  isActive: z.boolean().default(true),
  metadata: z.record(z.any()).optional()
});

export type MultiAgentSystem = z.infer<typeof MultiAgentSystemSchema>;

// Agent execution schema
export const AgentExecutionSchema = BaseEntitySchema.extend({
  agentId: z.string().uuid(),
  systemId: z.string().uuid().optional(),
  status: z.nativeEnum(ExecutionStatus),
  input: z.any(),
  output: z.any().optional(),
  error: z.string().optional(),
  steps: z.array(z.object({
    step: z.number(),
    action: z.string(),
    observation: z.string().optional(),
    thought: z.string().optional(),
    timestamp: z.string().datetime()
  })),
  startTime: z.string().datetime(),
  endTime: z.string().datetime().optional(),
  executionTime: z.number().optional(),
  userId: z.string().uuid().optional(),
  sessionId: z.string().optional(),
  metadata: z.record(z.any()).optional()
});

export type AgentExecution = z.infer<typeof AgentExecutionSchema>;

// Tool schema
export const ToolSchema = BaseEntitySchema.extend({
  name: z.string(),
  description: z.string(),
  type: z.enum(['api', 'function', 'webhook', 'database', 'custom']),
  config: z.record(z.any()),
  schema: z.record(z.any()).optional(),
  userId: z.string().uuid(),
  workspaceId: z.string().uuid().optional(),
  isActive: z.boolean().default(true),
  isPublic: z.boolean().default(false),
  metadata: z.record(z.any()).optional()
});

export type Tool = z.infer<typeof ToolSchema>;
