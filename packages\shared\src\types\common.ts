import { z } from 'zod';

// Base schemas
export const IdSchema = z.string().uuid();
export const TimestampSchema = z.string().datetime();

// Common enums
export enum NodeType {
  START = 'start',
  LLM = 'llm',
  TOOL = 'tool',
  MEMORY = 'memory',
  VECTOR_STORE = 'vectorStore',
  DOCUMENT_LOADER = 'documentLoader',
  TEXT_SPLITTER = 'textSplitter',
  EMBEDDINGS = 'embeddings',
  RETRIEVER = 'retriever',
  CHAIN = 'chain',
  AGENT = 'agent',
  OUTPUT_PARSER = 'outputParser',
  PROMPT_TEMPLATE = 'promptTemplate',
  CONDITIONAL = 'conditional',
  LOOP = 'loop',
  WEBHOOK = 'webhook',
  API_CALL = 'apiCall',
  CUSTOM = 'custom'
}

export enum ConnectionType {
  INPUT = 'input',
  OUTPUT = 'output'
}

export enum DataType {
  STRING = 'string',
  NUMBER = 'number',
  BOOLEAN = 'boolean',
  OBJECT = 'object',
  ARRAY = 'array',
  LLM = 'llm',
  MEMORY = 'memory',
  VECTOR_STORE = 'vectorStore',
  DOCUMENT = 'document',
  TOOL = 'tool',
  AGENT = 'agent'
}

export enum FlowStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  ARCHIVED = 'archived'
}

export enum ExecutionStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

// Position schema for node positioning
export const PositionSchema = z.object({
  x: z.number(),
  y: z.number()
});

export type Position = z.infer<typeof PositionSchema>;

// Base entity schema
export const BaseEntitySchema = z.object({
  id: IdSchema,
  createdAt: TimestampSchema,
  updatedAt: TimestampSchema
});

export type BaseEntity = z.infer<typeof BaseEntitySchema>;
