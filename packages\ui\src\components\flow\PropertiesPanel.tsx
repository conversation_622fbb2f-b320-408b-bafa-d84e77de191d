'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Settings, 
  Info, 
  Code, 
  Trash2,
  Copy,
  Eye,
  EyeOff
} from 'lucide-react';
import { useFlowStore } from '@/store/flowStore';

export function PropertiesPanel() {
  const { selectedNodeId, selectedEdgeId, nodes, edges, updateNode } = useFlowStore();
  const [formData, setFormData] = useState<{
    label?: string;
    description?: string;
    config?: Record<string, any>;
  }>({});

  const selectedNode = selectedNodeId ? nodes.find(n => n.id === selectedNodeId) : null;
  const selectedEdge = selectedEdgeId ? edges.find(e => e.id === selectedEdgeId) : null;

  useEffect(() => {
    if (selectedNode) {
      setFormData({
        label: selectedNode.data?.label || '',
        description: selectedNode.data?.description || '',
        config: selectedNode.data?.config || {},
      });
    } else {
      setFormData({});
    }
  }, [selectedNode]);

  const handleConfigChange = (key: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      config: {
        ...prev.config,
        [key]: value,
      },
    }));
  };

  const handleSave = () => {
    if (selectedNode) {
      updateNode(selectedNode.id, {
        data: {
          ...selectedNode.data,
          label: formData.label,
          description: formData.description,
          config: formData.config,
        },
      });
    }
  };

  const handleDuplicate = () => {
    if (selectedNode) {
      // TODO: Implement node duplication
      console.log('Duplicate node:', selectedNode.id);
    }
  };

  const handleDelete = () => {
    if (selectedNode) {
      // TODO: Implement node deletion with confirmation
      console.log('Delete node:', selectedNode.id);
    }
  };

  if (!selectedNode && !selectedEdge) {
    return (
      <div className="h-full flex flex-col">
        <div className="p-4 border-b border-gray-200">
          <h2 className="font-semibold text-gray-900">Properties</h2>
        </div>
        <div className="flex-1 flex items-center justify-center p-4">
          <div className="text-center">
            <Settings className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-600 text-sm">Select a node or edge</p>
            <p className="text-gray-500 text-xs">to view its properties</p>
          </div>
        </div>
      </div>
    );
  }

  if (selectedEdge) {
    return (
      <div className="h-full flex flex-col">
        <div className="p-4 border-b border-gray-200">
          <h2 className="font-semibold text-gray-900">Edge Properties</h2>
        </div>
        <div className="flex-1 p-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Connection Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <Label className="text-xs">Source Node</Label>
                <p className="text-sm text-gray-600">{selectedEdge.source}</p>
              </div>
              <div>
                <Label className="text-xs">Target Node</Label>
                <p className="text-sm text-gray-600">{selectedEdge.target}</p>
              </div>
              <div>
                <Label className="text-xs">Source Handle</Label>
                <p className="text-sm text-gray-600">{selectedEdge.sourceHandle}</p>
              </div>
              <div>
                <Label className="text-xs">Target Handle</Label>
                <p className="text-sm text-gray-600">{selectedEdge.targetHandle}</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h2 className="font-semibold text-gray-900">Node Properties</h2>
          <div className="flex items-center space-x-1">
            <Button variant="ghost" size="sm" onClick={handleDuplicate}>
              <Copy className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={handleDelete}>
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
        {selectedNode && (
          <div className="mt-2">
            <Badge variant="secondary" className="text-xs">
              {selectedNode.type}
            </Badge>
          </div>
        )}
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto">
        <Tabs defaultValue="general" className="h-full">
          <TabsList className="grid w-full grid-cols-3 m-4">
            <TabsTrigger value="general" className="text-xs">
              <Info className="h-3 w-3 mr-1" />
              General
            </TabsTrigger>
            <TabsTrigger value="config" className="text-xs">
              <Settings className="h-3 w-3 mr-1" />
              Config
            </TabsTrigger>
            <TabsTrigger value="advanced" className="text-xs">
              <Code className="h-3 w-3 mr-1" />
              Advanced
            </TabsTrigger>
          </TabsList>

          <TabsContent value="general" className="p-4 space-y-4">
            <div className="space-y-2">
              <Label htmlFor="label">Label</Label>
              <Input
                id="label"
                value={formData.label || ''}
                onChange={(e) => setFormData((prev) => ({ ...prev, label: e.target.value }))}
                placeholder="Node label"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description || ''}
                onChange={(e) => setFormData((prev) => ({ ...prev, description: e.target.value }))}
                placeholder="Node description"
                rows={3}
              />
            </div>

            {selectedNode && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Node Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2 text-xs">
                  <div className="flex justify-between">
                    <span className="text-gray-500">ID:</span>
                    <span className="font-mono">{selectedNode.id}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">Type:</span>
                    <span>{selectedNode.type}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">Category:</span>
                    <span>{selectedNode.data?.category}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">Inputs:</span>
                    <span>{selectedNode.data?.inputs?.length || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">Outputs:</span>
                    <span>{selectedNode.data?.outputs?.length || 0}</span>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="config" className="p-4 space-y-4">
            {selectedNode?.type === 'llm' && (
              <LLMConfigForm 
                config={formData.config || {}}
                onChange={handleConfigChange}
              />
            )}
            
            {selectedNode?.type === 'tool' && (
              <ToolConfigForm 
                config={formData.config || {}}
                onChange={handleConfigChange}
              />
            )}
            
            {selectedNode?.type === 'memory' && (
              <MemoryConfigForm 
                config={formData.config || {}}
                onChange={handleConfigChange}
              />
            )}

            {/* Generic config for other node types */}
            {!['llm', 'tool', 'memory'].includes(selectedNode?.type || '') && (
              <div className="space-y-4">
                <p className="text-sm text-gray-600">
                  Configuration options for this node type are not yet implemented.
                </p>
                <pre className="text-xs bg-gray-100 p-3 rounded">
                  {JSON.stringify(formData.config, null, 2)}
                </pre>
              </div>
            )}
          </TabsContent>

          <TabsContent value="advanced" className="p-4 space-y-4">
            <div className="space-y-2">
              <Label>Raw Configuration</Label>
              <Textarea
                value={JSON.stringify(formData.config, null, 2)}
                onChange={(e) => {
                  try {
                    const config = JSON.parse(e.target.value);
                    setFormData((prev) => ({ ...prev, config }));
                  } catch (error) {
                    // Invalid JSON, ignore
                  }
                }}
                rows={10}
                className="font-mono text-xs"
              />
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200">
        <Button onClick={handleSave} className="w-full" size="sm">
          Save Changes
        </Button>
      </div>
    </div>
  );
}

// LLM Configuration Form
function LLMConfigForm({ config, onChange }: { config: any; onChange: (key: string, value: any) => void }) {
  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label>Model</Label>
        <Select
          value={config.model || 'gpt-4'}
          onValueChange={(value) => onChange('model', value)}
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="gpt-4">GPT-4</SelectItem>
            <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
            <SelectItem value="claude-3-opus">Claude 3 Opus</SelectItem>
            <SelectItem value="claude-3-sonnet">Claude 3 Sonnet</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label>Temperature</Label>
        <Input
          type="number"
          min="0"
          max="2"
          step="0.1"
          value={config.temperature || 0.7}
          onChange={(e) => onChange('temperature', parseFloat(e.target.value))}
        />
      </div>

      <div className="space-y-2">
        <Label>Max Tokens</Label>
        <Input
          type="number"
          min="1"
          max="4000"
          value={config.maxTokens || 1000}
          onChange={(e) => onChange('maxTokens', parseInt(e.target.value))}
        />
      </div>

      <div className="space-y-2">
        <Label>System Message</Label>
        <Textarea
          value={config.systemMessage || ''}
          onChange={(e) => onChange('systemMessage', e.target.value)}
          placeholder="You are a helpful assistant..."
          rows={3}
        />
      </div>
    </div>
  );
}

// Tool Configuration Form
function ToolConfigForm({ config, onChange }: { config: any; onChange: (key: string, value: any) => void }) {
  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label>Tool Type</Label>
        <Select
          value={config.toolType || 'web-search'}
          onValueChange={(value) => onChange('toolType', value)}
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="web-search">Web Search</SelectItem>
            <SelectItem value="calculator">Calculator</SelectItem>
            <SelectItem value="api-call">API Call</SelectItem>
            <SelectItem value="custom">Custom</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label>API Endpoint</Label>
        <Input
          value={config.apiEndpoint || ''}
          onChange={(e) => onChange('apiEndpoint', e.target.value)}
          placeholder="https://api.example.com/search"
        />
      </div>

      <div className="space-y-2">
        <Label>API Key</Label>
        <Input
          type="password"
          value={config.apiKey || ''}
          onChange={(e) => onChange('apiKey', e.target.value)}
          placeholder="Your API key"
        />
      </div>
    </div>
  );
}

// Memory Configuration Form
function MemoryConfigForm({ config, onChange }: { config: any; onChange: (key: string, value: any) => void }) {
  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label>Memory Type</Label>
        <Select
          value={config.memoryType || 'conversation'}
          onValueChange={(value) => onChange('memoryType', value)}
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="conversation">Conversation Memory</SelectItem>
            <SelectItem value="buffer">Buffer Memory</SelectItem>
            <SelectItem value="summary">Summary Memory</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label>Max Messages</Label>
        <Input
          type="number"
          min="1"
          max="100"
          value={config.maxMessages || 10}
          onChange={(e) => onChange('maxMessages', parseInt(e.target.value))}
        />
      </div>

      <div className="space-y-2">
        <Label>Return Messages</Label>
        <Input
          type="number"
          min="1"
          max="20"
          value={config.returnMessages || 5}
          onChange={(e) => onChange('returnMessages', parseInt(e.target.value))}
        />
      </div>
    </div>
  );
}
