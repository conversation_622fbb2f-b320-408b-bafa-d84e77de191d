(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[805],{366:function(e,s,t){Promise.resolve().then(t.bind(t,3849))},3849:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return P}});var a=t(8980),r=t(2208),n=t(9838),l=t(9008),i=t(4507),d=t(8055),o=t(1929),c=t(3281),m=t(3815),u=t(9713),f=t(6202),x=t(9484),p=t(1792),h=t(3730),g=t(5207),j=t(9808),N=t(3744),y=t(9014),v=t(2424);let b={active:"bg-green-100 text-green-800",draft:"bg-yellow-100 text-yellow-800",inactive:"bg-gray-100 text-gray-800"},w={active:f.Z,draft:x.Z,inactive:p.Z};function C(e){let{flow:s,viewMode:t}=e,r=w[s.status];return"list"===t?(0,a.jsx)(m.Zb,{className:"hover:shadow-md transition-shadow",children:(0,a.jsx)(m.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4 flex-1",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,a.jsx)("h3",{className:"font-semibold text-lg",children:s.name}),(0,a.jsxs)(u.C,{className:b[s.status],children:[(0,a.jsx)(r,{className:"h-3 w-3 mr-1"}),s.status]})]}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-2",children:s.description}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(h.Z,{className:"h-4 w-4 mr-1"}),s.nodeCount," nodes"]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(p.Z,{className:"h-4 w-4 mr-1"}),"Updated ",(0,v.p6)(s.updatedAt)]})]})]}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-1",children:[s.tags.slice(0,3).map(e=>(0,a.jsx)(u.C,{variant:"secondary",className:"text-xs",children:e},e)),s.tags.length>3&&(0,a.jsxs)(u.C,{variant:"secondary",className:"text-xs",children:["+",s.tags.length-3]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 ml-4",children:[(0,a.jsx)(n.z,{size:"sm",asChild:!0,children:(0,a.jsxs)(y.default,{href:"/flows/".concat(s.id),children:[(0,a.jsx)(x.Z,{className:"h-4 w-4 mr-1"}),"Edit"]})}),(0,a.jsxs)(n.z,{size:"sm",variant:"outline",children:[(0,a.jsx)(g.Z,{className:"h-4 w-4 mr-1"}),"Test"]}),(0,a.jsx)(n.z,{size:"sm",variant:"ghost",children:(0,a.jsx)(j.Z,{className:"h-4 w-4"})})]})]})})}):(0,a.jsxs)(m.Zb,{className:"hover:shadow-md transition-shadow",children:[(0,a.jsx)(m.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,a.jsx)(m.ll,{className:"text-lg",children:s.name}),(0,a.jsxs)(u.C,{className:b[s.status],children:[(0,a.jsx)(r,{className:"h-3 w-3 mr-1"}),s.status]})]}),(0,a.jsx)(m.SZ,{className:"line-clamp-2",children:s.description})]}),(0,a.jsx)(n.z,{size:"sm",variant:"ghost",children:(0,a.jsx)(j.Z,{className:"h-4 w-4"})})]})}),(0,a.jsxs)(m.aY,{className:"pt-0",children:[(0,a.jsx)("div",{className:"flex items-center justify-between mb-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(h.Z,{className:"h-4 w-4 mr-1"}),s.nodeCount," nodes"]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(p.Z,{className:"h-4 w-4 mr-1"}),(0,v.p6)(s.updatedAt)]})]})}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-1 mb-4",children:[s.tags.slice(0,3).map(e=>(0,a.jsx)(u.C,{variant:"secondary",className:"text-xs",children:e},e)),s.tags.length>3&&(0,a.jsxs)(u.C,{variant:"secondary",className:"text-xs",children:["+",s.tags.length-3]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(n.z,{size:"sm",className:"flex-1",asChild:!0,children:(0,a.jsxs)(y.default,{href:"/flows/".concat(s.id),children:[(0,a.jsx)(x.Z,{className:"h-4 w-4 mr-1"}),"Edit"]})}),(0,a.jsx)(n.z,{size:"sm",variant:"outline",children:(0,a.jsx)(g.Z,{className:"h-4 w-4"})}),(0,a.jsx)(n.z,{size:"sm",variant:"outline",children:(0,a.jsx)(N.Z,{className:"h-4 w-4"})})]})]})]})}var Z=t(9211),k=t(9906),z=t(4740),A=t(2594),R=t(6198),_=t(4164);let S=R.fC;R.xz;let F=R.h_;R.x8;let T=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(R.aV,{ref:s,className:(0,v.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...r})});T.displayName=R.aV.displayName;let D=r.forwardRef((e,s)=>{let{className:t,children:r,...n}=e;return(0,a.jsxs)(F,{children:[(0,a.jsx)(T,{}),(0,a.jsxs)(R.VY,{ref:s,className:(0,v.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",t),...n,children:[r,(0,a.jsxs)(R.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(_.Z,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});D.displayName=R.VY.displayName;let I=e=>{let{className:s,...t}=e;return(0,a.jsx)("div",{className:(0,v.cn)("flex flex-col space-y-1.5 text-center sm:text-left",s),...t})};I.displayName="DialogHeader";let O=e=>{let{className:s,...t}=e;return(0,a.jsx)("div",{className:(0,v.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",s),...t})};O.displayName="DialogFooter";let V=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(R.Dx,{ref:s,className:(0,v.cn)("text-lg font-semibold leading-none tracking-tight",t),...r})});V.displayName=R.Dx.displayName;let Y=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(R.dk,{ref:s,className:(0,v.cn)("text-sm text-muted-foreground",t),...r})});Y.displayName=R.dk.displayName;var B=t(7731);let L=[{id:"blank",name:"Blank Flow",description:"Start with an empty canvas",category:"Basic"},{id:"chatbot",name:"Chatbot",description:"Simple conversational AI",category:"Chat"},{id:"rag",name:"RAG Assistant",description:"Document-based Q&A system",category:"Knowledge"},{id:"multi-agent",name:"Multi-Agent System",description:"Coordinated agent workflow",category:"Advanced"}];function E(e){let{open:s,onOpenChange:t}=e,i=(0,Z.useRouter)(),[d,o]=(0,r.useState)({name:"",description:"",template:"blank",tags:[]}),[c,m]=(0,r.useState)(""),[f,x]=(0,r.useState)(!1),p=async e=>{e.preventDefault(),x(!0);try{let e=(0,v.Ox)();await new Promise(e=>setTimeout(e,1e3)),i.push("/flows/".concat(e)),t(!1),o({name:"",description:"",template:"blank",tags:[]})}catch(e){console.error("Failed to create flow:",e)}finally{x(!1)}},h=()=>{c.trim()&&!d.tags.includes(c.trim())&&(o(e=>({...e,tags:[...e.tags,c.trim()]})),m(""))},g=e=>{o(s=>({...s,tags:s.tags.filter(s=>s!==e)}))},j=L.find(e=>e.id===d.template);return(0,a.jsx)(S,{open:s,onOpenChange:t,children:(0,a.jsxs)(D,{className:"sm:max-w-[500px]",children:[(0,a.jsxs)(I,{children:[(0,a.jsx)(V,{children:"Create New Flow"}),(0,a.jsx)(Y,{children:"Set up your new AI workflow. You can always change these settings later."})]}),(0,a.jsxs)("form",{onSubmit:p,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(A._,{htmlFor:"name",children:"Flow Name"}),(0,a.jsx)(k.I,{id:"name",placeholder:"My Awesome Flow",value:d.name,onChange:e=>o(s=>({...s,name:e.target.value})),required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(A._,{htmlFor:"description",children:"Description"}),(0,a.jsx)(z.g,{id:"description",placeholder:"Describe what this flow does...",value:d.description,onChange:e=>o(s=>({...s,description:e.target.value})),rows:3})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(A._,{htmlFor:"template",children:"Template"}),(0,a.jsxs)(B.Ph,{value:d.template,onValueChange:e=>o(s=>({...s,template:e})),children:[(0,a.jsx)(B.i4,{children:(0,a.jsx)(B.ki,{})}),(0,a.jsx)(B.Bw,{children:L.map(e=>(0,a.jsx)(B.Ql,{value:e.id,children:(0,a.jsxs)("div",{className:"flex flex-col items-start",children:[(0,a.jsx)("span",{className:"font-medium",children:e.name}),(0,a.jsx)("span",{className:"text-sm text-gray-500",children:e.description})]})},e.id))})]}),j&&(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Category: ",j.category]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(A._,{children:"Tags"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mb-2",children:d.tags.map(e=>(0,a.jsxs)(u.C,{variant:"secondary",className:"flex items-center gap-1",children:[e,(0,a.jsx)("button",{type:"button",onClick:()=>g(e),className:"ml-1 hover:bg-gray-200 rounded-full p-0.5",children:(0,a.jsx)(_.Z,{className:"h-3 w-3"})})]},e))}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(k.I,{placeholder:"Add a tag...",value:c,onChange:e=>m(e.target.value),onKeyPress:e=>{"Enter"===e.key&&(e.preventDefault(),h())}}),(0,a.jsx)(n.z,{type:"button",variant:"outline",size:"sm",onClick:h,children:(0,a.jsx)(l.Z,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)(O,{children:[(0,a.jsx)(n.z,{type:"button",variant:"outline",onClick:()=>t(!1),disabled:f,children:"Cancel"}),(0,a.jsx)(n.z,{type:"submit",disabled:!d.name.trim()||f,children:f?"Creating...":"Create Flow"})]})]})]})})}let M=[{id:"1",name:"Customer Support Bot",description:"AI-powered customer support chatbot with knowledge base integration",status:"active",createdAt:"2024-01-15T10:00:00Z",updatedAt:"2024-01-20T15:30:00Z",nodeCount:8,tags:["chatbot","support","rag"]},{id:"2",name:"Document Analyzer",description:"Multi-agent system for analyzing and summarizing documents",status:"draft",createdAt:"2024-01-18T09:15:00Z",updatedAt:"2024-01-18T16:45:00Z",nodeCount:12,tags:["document","analysis","multi-agent"]},{id:"3",name:"Code Review Assistant",description:"AI agent that reviews code and provides suggestions",status:"active",createdAt:"2024-01-10T14:20:00Z",updatedAt:"2024-01-22T11:10:00Z",nodeCount:6,tags:["code","review","development"]}];function P(){let[e,s]=(0,r.useState)(""),[t,m]=(0,r.useState)("grid"),[u,f]=(0,r.useState)(!1),x=M.filter(s=>s.name.toLowerCase().includes(e.toLowerCase())||s.description.toLowerCase().includes(e.toLowerCase())||s.tags.some(s=>s.toLowerCase().includes(e.toLowerCase())));return(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Flows"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"Build and manage your AI workflows"})]}),(0,a.jsxs)(n.z,{onClick:()=>f(!0),children:[(0,a.jsx)(l.Z,{className:"h-4 w-4 mr-2"}),"Create Flow"]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 mb-6",children:[(0,a.jsxs)("div",{className:"relative flex-1 max-w-md",children:[(0,a.jsx)(i.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,a.jsx)("input",{type:"text",placeholder:"Search flows...",value:e,onChange:e=>s(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,a.jsxs)(n.z,{variant:"outline",size:"sm",children:[(0,a.jsx)(d.Z,{className:"h-4 w-4 mr-2"}),"Filter"]}),(0,a.jsxs)("div",{className:"flex items-center border border-gray-300 rounded-lg",children:[(0,a.jsx)(n.z,{variant:"grid"===t?"default":"ghost",size:"sm",onClick:()=>m("grid"),className:"rounded-r-none",children:(0,a.jsx)(o.Z,{className:"h-4 w-4"})}),(0,a.jsx)(n.z,{variant:"list"===t?"default":"ghost",size:"sm",onClick:()=>m("list"),className:"rounded-l-none",children:(0,a.jsx)(c.Z,{className:"h-4 w-4"})})]})]}),0===x.length&&""===e&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4",children:(0,a.jsx)(l.Z,{className:"h-8 w-8 text-gray-400"})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No flows yet"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Get started by creating your first AI workflow"}),(0,a.jsxs)(n.z,{onClick:()=>f(!0),children:[(0,a.jsx)(l.Z,{className:"h-4 w-4 mr-2"}),"Create Your First Flow"]})]}),0===x.length&&""!==e&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4",children:(0,a.jsx)(i.Z,{className:"h-8 w-8 text-gray-400"})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No flows found"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Try adjusting your search terms"})]}),x.length>0&&(0,a.jsx)("div",{className:"grid"===t?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6":"space-y-4",children:x.map(e=>(0,a.jsx)(C,{flow:e,viewMode:t},e.id))}),(0,a.jsx)(E,{open:u,onOpenChange:f})]})}},9713:function(e,s,t){"use strict";t.d(s,{C:function(){return i}});var a=t(8980);t(2208);var r=t(3868),n=t(2424);let l=(0,r.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:s,variant:t,...r}=e;return(0,a.jsx)("div",{className:(0,n.cn)(l({variant:t}),s),...r})}},9838:function(e,s,t){"use strict";t.d(s,{z:function(){return o}});var a=t(8980),r=t(2208),n=t(9382),l=t(3868),i=t(2424);let d=(0,l.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=r.forwardRef((e,s)=>{let{className:t,variant:r,size:l,asChild:o=!1,...c}=e,m=o?n.g7:"button";return(0,a.jsx)(m,{className:(0,i.cn)(d({variant:r,size:l,className:t})),ref:s,...c})});o.displayName="Button"},3815:function(e,s,t){"use strict";t.d(s,{Ol:function(){return i},SZ:function(){return o},Zb:function(){return l},aY:function(){return c},ll:function(){return d}});var a=t(8980),r=t(2208),n=t(2424);let l=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...r})});l.displayName="Card";let i=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",t),...r})});i.displayName="CardHeader";let d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("h3",{ref:s,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",t),...r})});d.displayName="CardTitle";let o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("p",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",t),...r})});o.displayName="CardDescription";let c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("p-6 pt-0",t),...r})});c.displayName="CardContent",r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",t),...r})}).displayName="CardFooter"},9906:function(e,s,t){"use strict";t.d(s,{I:function(){return l}});var a=t(8980),r=t(2208),n=t(2424);let l=r.forwardRef((e,s)=>{let{className:t,type:r,...l}=e;return(0,a.jsx)("input",{type:r,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...l})});l.displayName="Input"},2594:function(e,s,t){"use strict";t.d(s,{_:function(){return o}});var a=t(8980),r=t(2208),n=t(3747),l=t(3868),i=t(2424);let d=(0,l.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(n.f,{ref:s,className:(0,i.cn)(d(),t),...r})});o.displayName=n.f.displayName},7731:function(e,s,t){"use strict";t.d(s,{Bw:function(){return p},Ph:function(){return c},Ql:function(){return h},i4:function(){return u},ki:function(){return m}});var a=t(8980),r=t(2208),n=t(242),l=t(5837),i=t(6887),d=t(4362),o=t(2424);let c=n.fC;n.ZA;let m=n.B4,u=r.forwardRef((e,s)=>{let{className:t,children:r,...i}=e;return(0,a.jsxs)(n.xz,{ref:s,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),...i,children:[r,(0,a.jsx)(n.JO,{asChild:!0,children:(0,a.jsx)(l.Z,{className:"h-4 w-4 opacity-50"})})]})});u.displayName=n.xz.displayName;let f=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(n.u_,{ref:s,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...r,children:(0,a.jsx)(i.Z,{className:"h-4 w-4"})})});f.displayName=n.u_.displayName;let x=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(n.$G,{ref:s,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...r,children:(0,a.jsx)(l.Z,{className:"h-4 w-4"})})});x.displayName=n.$G.displayName;let p=r.forwardRef((e,s)=>{let{className:t,children:r,position:l="popper",...i}=e;return(0,a.jsx)(n.h_,{children:(0,a.jsxs)(n.VY,{ref:s,className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===l&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:l,...i,children:[(0,a.jsx)(f,{}),(0,a.jsx)(n.l_,{className:(0,o.cn)("p-1","popper"===l&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:r}),(0,a.jsx)(x,{})]})})});p.displayName=n.VY.displayName,r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(n.__,{ref:s,className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",t),...r})}).displayName=n.__.displayName;let h=r.forwardRef((e,s)=>{let{className:t,children:r,...l}=e;return(0,a.jsxs)(n.ck,{ref:s,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...l,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(n.wU,{children:(0,a.jsx)(d.Z,{className:"h-4 w-4"})})}),(0,a.jsx)(n.eT,{children:r})]})});h.displayName=n.ck.displayName,r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(n.Z0,{ref:s,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",t),...r})}).displayName=n.Z0.displayName},4740:function(e,s,t){"use strict";t.d(s,{g:function(){return l}});var a=t(8980),r=t(2208),n=t(2424);let l=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...r})});l.displayName="Textarea"},2424:function(e,s,t){"use strict";t.d(s,{Ox:function(){return i},cn:function(){return n},p6:function(){return l}});var a=t(2240),r=t(3946);function n(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,r.m6)((0,a.W)(s))}function l(e){return new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})}function i(){return Math.random().toString(36).substr(2,9)}}},function(e){e.O(0,[720,606,765,940,14,512,744],function(){return e(e.s=366)}),_N_E=e.O()}]);