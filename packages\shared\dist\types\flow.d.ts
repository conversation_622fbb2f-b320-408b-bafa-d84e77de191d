import { z } from 'zod';
import { FlowStatus, ExecutionStatus } from './common';
export declare const FlowSchema: z.ZodObject<{
    id: z.ZodString;
    createdAt: z.ZodString;
    updatedAt: z.ZodString;
} & {
    name: z.ZodString;
    description: z.ZodOptional<z.ZodString>;
    status: z.ZodNativeEnum<typeof FlowStatus>;
    nodes: z.Zod<PERSON>rray<z.ZodObject<{
        id: z.ZodString;
        createdAt: z.ZodString;
        updatedAt: z.ZodString;
    } & {
        type: z.ZodNativeEnum<typeof import("./common").NodeType>;
        name: z.ZodString;
        label: z.ZodString;
        description: z.ZodOptional<z.ZodString>;
        category: z.ZodString;
        version: z.ZodDefault<z.ZodString>;
        position: z.ZodObject<{
            x: z.ZodNumber;
            y: z.ZodNumber;
        }, "strip", z.ZodTypeAny, {
            x: number;
            y: number;
        }, {
            x: number;
            y: number;
        }>;
        inputs: z.ZodArray<z.ZodObject<{
            id: z.ZodString;
            type: z.ZodNativeEnum<typeof import("./common").ConnectionType>;
            dataType: z.ZodNativeEnum<typeof import("./common").DataType>;
            label: z.ZodString;
            required: z.ZodDefault<z.ZodBoolean>;
            multiple: z.ZodDefault<z.ZodBoolean>;
        }, "strip", z.ZodTypeAny, {
            type: import("./common").ConnectionType;
            id: string;
            dataType: import("./common").DataType;
            label: string;
            required: boolean;
            multiple: boolean;
        }, {
            type: import("./common").ConnectionType;
            id: string;
            dataType: import("./common").DataType;
            label: string;
            required?: boolean | undefined;
            multiple?: boolean | undefined;
        }>, "many">;
        outputs: z.ZodArray<z.ZodObject<{
            id: z.ZodString;
            type: z.ZodNativeEnum<typeof import("./common").ConnectionType>;
            dataType: z.ZodNativeEnum<typeof import("./common").DataType>;
            label: z.ZodString;
            required: z.ZodDefault<z.ZodBoolean>;
            multiple: z.ZodDefault<z.ZodBoolean>;
        }, "strip", z.ZodTypeAny, {
            type: import("./common").ConnectionType;
            id: string;
            dataType: import("./common").DataType;
            label: string;
            required: boolean;
            multiple: boolean;
        }, {
            type: import("./common").ConnectionType;
            id: string;
            dataType: import("./common").DataType;
            label: string;
            required?: boolean | undefined;
            multiple?: boolean | undefined;
        }>, "many">;
        config: z.ZodRecord<z.ZodString, z.ZodAny>;
        flowId: z.ZodString;
        isCustom: z.ZodDefault<z.ZodBoolean>;
        icon: z.ZodOptional<z.ZodString>;
        color: z.ZodOptional<z.ZodString>;
        documentation: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        type: import("./common").NodeType;
        id: string;
        createdAt: string;
        updatedAt: string;
        label: string;
        name: string;
        category: string;
        version: string;
        position: {
            x: number;
            y: number;
        };
        inputs: {
            type: import("./common").ConnectionType;
            id: string;
            dataType: import("./common").DataType;
            label: string;
            required: boolean;
            multiple: boolean;
        }[];
        outputs: {
            type: import("./common").ConnectionType;
            id: string;
            dataType: import("./common").DataType;
            label: string;
            required: boolean;
            multiple: boolean;
        }[];
        config: Record<string, any>;
        flowId: string;
        isCustom: boolean;
        description?: string | undefined;
        icon?: string | undefined;
        color?: string | undefined;
        documentation?: string | undefined;
    }, {
        type: import("./common").NodeType;
        id: string;
        createdAt: string;
        updatedAt: string;
        label: string;
        name: string;
        category: string;
        position: {
            x: number;
            y: number;
        };
        inputs: {
            type: import("./common").ConnectionType;
            id: string;
            dataType: import("./common").DataType;
            label: string;
            required?: boolean | undefined;
            multiple?: boolean | undefined;
        }[];
        outputs: {
            type: import("./common").ConnectionType;
            id: string;
            dataType: import("./common").DataType;
            label: string;
            required?: boolean | undefined;
            multiple?: boolean | undefined;
        }[];
        config: Record<string, any>;
        flowId: string;
        description?: string | undefined;
        version?: string | undefined;
        isCustom?: boolean | undefined;
        icon?: string | undefined;
        color?: string | undefined;
        documentation?: string | undefined;
    }>, "many">;
    connections: z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        sourceNodeId: z.ZodString;
        sourceHandle: z.ZodString;
        targetNodeId: z.ZodString;
        targetHandle: z.ZodString;
        flowId: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        id: string;
        flowId: string;
        sourceNodeId: string;
        sourceHandle: string;
        targetNodeId: string;
        targetHandle: string;
    }, {
        id: string;
        flowId: string;
        sourceNodeId: string;
        sourceHandle: string;
        targetNodeId: string;
        targetHandle: string;
    }>, "many">;
    userId: z.ZodString;
    workspaceId: z.ZodOptional<z.ZodString>;
    isPublic: z.ZodDefault<z.ZodBoolean>;
    tags: z.ZodDefault<z.ZodArray<z.ZodString, "many">>;
    version: z.ZodDefault<z.ZodString>;
    metadata: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
}, "strip", z.ZodTypeAny, {
    status: FlowStatus;
    id: string;
    createdAt: string;
    updatedAt: string;
    name: string;
    version: string;
    nodes: {
        type: import("./common").NodeType;
        id: string;
        createdAt: string;
        updatedAt: string;
        label: string;
        name: string;
        category: string;
        version: string;
        position: {
            x: number;
            y: number;
        };
        inputs: {
            type: import("./common").ConnectionType;
            id: string;
            dataType: import("./common").DataType;
            label: string;
            required: boolean;
            multiple: boolean;
        }[];
        outputs: {
            type: import("./common").ConnectionType;
            id: string;
            dataType: import("./common").DataType;
            label: string;
            required: boolean;
            multiple: boolean;
        }[];
        config: Record<string, any>;
        flowId: string;
        isCustom: boolean;
        description?: string | undefined;
        icon?: string | undefined;
        color?: string | undefined;
        documentation?: string | undefined;
    }[];
    connections: {
        id: string;
        flowId: string;
        sourceNodeId: string;
        sourceHandle: string;
        targetNodeId: string;
        targetHandle: string;
    }[];
    userId: string;
    isPublic: boolean;
    tags: string[];
    description?: string | undefined;
    workspaceId?: string | undefined;
    metadata?: Record<string, any> | undefined;
}, {
    status: FlowStatus;
    id: string;
    createdAt: string;
    updatedAt: string;
    name: string;
    nodes: {
        type: import("./common").NodeType;
        id: string;
        createdAt: string;
        updatedAt: string;
        label: string;
        name: string;
        category: string;
        position: {
            x: number;
            y: number;
        };
        inputs: {
            type: import("./common").ConnectionType;
            id: string;
            dataType: import("./common").DataType;
            label: string;
            required?: boolean | undefined;
            multiple?: boolean | undefined;
        }[];
        outputs: {
            type: import("./common").ConnectionType;
            id: string;
            dataType: import("./common").DataType;
            label: string;
            required?: boolean | undefined;
            multiple?: boolean | undefined;
        }[];
        config: Record<string, any>;
        flowId: string;
        description?: string | undefined;
        version?: string | undefined;
        isCustom?: boolean | undefined;
        icon?: string | undefined;
        color?: string | undefined;
        documentation?: string | undefined;
    }[];
    connections: {
        id: string;
        flowId: string;
        sourceNodeId: string;
        sourceHandle: string;
        targetNodeId: string;
        targetHandle: string;
    }[];
    userId: string;
    description?: string | undefined;
    version?: string | undefined;
    workspaceId?: string | undefined;
    isPublic?: boolean | undefined;
    tags?: string[] | undefined;
    metadata?: Record<string, any> | undefined;
}>;
export type Flow = z.infer<typeof FlowSchema>;
export declare const FlowExecutionSchema: z.ZodObject<{
    id: z.ZodString;
    createdAt: z.ZodString;
    updatedAt: z.ZodString;
} & {
    flowId: z.ZodString;
    status: z.ZodNativeEnum<typeof ExecutionStatus>;
    input: z.ZodOptional<z.ZodAny>;
    output: z.ZodOptional<z.ZodAny>;
    error: z.ZodOptional<z.ZodString>;
    startTime: z.ZodString;
    endTime: z.ZodOptional<z.ZodString>;
    executionTime: z.ZodOptional<z.ZodNumber>;
    nodeResults: z.ZodArray<z.ZodObject<{
        nodeId: z.ZodString;
        status: z.ZodEnum<["success", "error", "pending"]>;
        output: z.ZodOptional<z.ZodAny>;
        error: z.ZodOptional<z.ZodString>;
        executionTime: z.ZodOptional<z.ZodNumber>;
        timestamp: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        status: "pending" | "success" | "error";
        nodeId: string;
        timestamp: string;
        output?: any;
        error?: string | undefined;
        executionTime?: number | undefined;
    }, {
        status: "pending" | "success" | "error";
        nodeId: string;
        timestamp: string;
        output?: any;
        error?: string | undefined;
        executionTime?: number | undefined;
    }>, "many">;
    userId: z.ZodOptional<z.ZodString>;
    sessionId: z.ZodOptional<z.ZodString>;
    metadata: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
}, "strip", z.ZodTypeAny, {
    status: ExecutionStatus;
    id: string;
    createdAt: string;
    updatedAt: string;
    flowId: string;
    startTime: string;
    nodeResults: {
        status: "pending" | "success" | "error";
        nodeId: string;
        timestamp: string;
        output?: any;
        error?: string | undefined;
        executionTime?: number | undefined;
    }[];
    input?: any;
    output?: any;
    error?: string | undefined;
    executionTime?: number | undefined;
    userId?: string | undefined;
    metadata?: Record<string, any> | undefined;
    endTime?: string | undefined;
    sessionId?: string | undefined;
}, {
    status: ExecutionStatus;
    id: string;
    createdAt: string;
    updatedAt: string;
    flowId: string;
    startTime: string;
    nodeResults: {
        status: "pending" | "success" | "error";
        nodeId: string;
        timestamp: string;
        output?: any;
        error?: string | undefined;
        executionTime?: number | undefined;
    }[];
    input?: any;
    output?: any;
    error?: string | undefined;
    executionTime?: number | undefined;
    userId?: string | undefined;
    metadata?: Record<string, any> | undefined;
    endTime?: string | undefined;
    sessionId?: string | undefined;
}>;
export type FlowExecution = z.infer<typeof FlowExecutionSchema>;
export declare const FlowTemplateSchema: z.ZodObject<{
    name: z.ZodString;
    description: z.ZodString;
    category: z.ZodString;
    tags: z.ZodArray<z.ZodString, "many">;
    nodes: z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        createdAt: z.ZodString;
        updatedAt: z.ZodString;
    } & {
        type: z.ZodNativeEnum<typeof import("./common").NodeType>;
        name: z.ZodString;
        label: z.ZodString;
        description: z.ZodOptional<z.ZodString>;
        category: z.ZodString;
        version: z.ZodDefault<z.ZodString>;
        position: z.ZodObject<{
            x: z.ZodNumber;
            y: z.ZodNumber;
        }, "strip", z.ZodTypeAny, {
            x: number;
            y: number;
        }, {
            x: number;
            y: number;
        }>;
        inputs: z.ZodArray<z.ZodObject<{
            id: z.ZodString;
            type: z.ZodNativeEnum<typeof import("./common").ConnectionType>;
            dataType: z.ZodNativeEnum<typeof import("./common").DataType>;
            label: z.ZodString;
            required: z.ZodDefault<z.ZodBoolean>;
            multiple: z.ZodDefault<z.ZodBoolean>;
        }, "strip", z.ZodTypeAny, {
            type: import("./common").ConnectionType;
            id: string;
            dataType: import("./common").DataType;
            label: string;
            required: boolean;
            multiple: boolean;
        }, {
            type: import("./common").ConnectionType;
            id: string;
            dataType: import("./common").DataType;
            label: string;
            required?: boolean | undefined;
            multiple?: boolean | undefined;
        }>, "many">;
        outputs: z.ZodArray<z.ZodObject<{
            id: z.ZodString;
            type: z.ZodNativeEnum<typeof import("./common").ConnectionType>;
            dataType: z.ZodNativeEnum<typeof import("./common").DataType>;
            label: z.ZodString;
            required: z.ZodDefault<z.ZodBoolean>;
            multiple: z.ZodDefault<z.ZodBoolean>;
        }, "strip", z.ZodTypeAny, {
            type: import("./common").ConnectionType;
            id: string;
            dataType: import("./common").DataType;
            label: string;
            required: boolean;
            multiple: boolean;
        }, {
            type: import("./common").ConnectionType;
            id: string;
            dataType: import("./common").DataType;
            label: string;
            required?: boolean | undefined;
            multiple?: boolean | undefined;
        }>, "many">;
        config: z.ZodRecord<z.ZodString, z.ZodAny>;
        flowId: z.ZodString;
        isCustom: z.ZodDefault<z.ZodBoolean>;
        icon: z.ZodOptional<z.ZodString>;
        color: z.ZodOptional<z.ZodString>;
        documentation: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        type: import("./common").NodeType;
        id: string;
        createdAt: string;
        updatedAt: string;
        label: string;
        name: string;
        category: string;
        version: string;
        position: {
            x: number;
            y: number;
        };
        inputs: {
            type: import("./common").ConnectionType;
            id: string;
            dataType: import("./common").DataType;
            label: string;
            required: boolean;
            multiple: boolean;
        }[];
        outputs: {
            type: import("./common").ConnectionType;
            id: string;
            dataType: import("./common").DataType;
            label: string;
            required: boolean;
            multiple: boolean;
        }[];
        config: Record<string, any>;
        flowId: string;
        isCustom: boolean;
        description?: string | undefined;
        icon?: string | undefined;
        color?: string | undefined;
        documentation?: string | undefined;
    }, {
        type: import("./common").NodeType;
        id: string;
        createdAt: string;
        updatedAt: string;
        label: string;
        name: string;
        category: string;
        position: {
            x: number;
            y: number;
        };
        inputs: {
            type: import("./common").ConnectionType;
            id: string;
            dataType: import("./common").DataType;
            label: string;
            required?: boolean | undefined;
            multiple?: boolean | undefined;
        }[];
        outputs: {
            type: import("./common").ConnectionType;
            id: string;
            dataType: import("./common").DataType;
            label: string;
            required?: boolean | undefined;
            multiple?: boolean | undefined;
        }[];
        config: Record<string, any>;
        flowId: string;
        description?: string | undefined;
        version?: string | undefined;
        isCustom?: boolean | undefined;
        icon?: string | undefined;
        color?: string | undefined;
        documentation?: string | undefined;
    }>, "many">;
    connections: z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        sourceNodeId: z.ZodString;
        sourceHandle: z.ZodString;
        targetNodeId: z.ZodString;
        targetHandle: z.ZodString;
        flowId: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        id: string;
        flowId: string;
        sourceNodeId: string;
        sourceHandle: string;
        targetNodeId: string;
        targetHandle: string;
    }, {
        id: string;
        flowId: string;
        sourceNodeId: string;
        sourceHandle: string;
        targetNodeId: string;
        targetHandle: string;
    }>, "many">;
    thumbnail: z.ZodOptional<z.ZodString>;
    isOfficial: z.ZodDefault<z.ZodBoolean>;
    difficulty: z.ZodDefault<z.ZodEnum<["beginner", "intermediate", "advanced"]>>;
}, "strip", z.ZodTypeAny, {
    name: string;
    description: string;
    category: string;
    nodes: {
        type: import("./common").NodeType;
        id: string;
        createdAt: string;
        updatedAt: string;
        label: string;
        name: string;
        category: string;
        version: string;
        position: {
            x: number;
            y: number;
        };
        inputs: {
            type: import("./common").ConnectionType;
            id: string;
            dataType: import("./common").DataType;
            label: string;
            required: boolean;
            multiple: boolean;
        }[];
        outputs: {
            type: import("./common").ConnectionType;
            id: string;
            dataType: import("./common").DataType;
            label: string;
            required: boolean;
            multiple: boolean;
        }[];
        config: Record<string, any>;
        flowId: string;
        isCustom: boolean;
        description?: string | undefined;
        icon?: string | undefined;
        color?: string | undefined;
        documentation?: string | undefined;
    }[];
    connections: {
        id: string;
        flowId: string;
        sourceNodeId: string;
        sourceHandle: string;
        targetNodeId: string;
        targetHandle: string;
    }[];
    tags: string[];
    isOfficial: boolean;
    difficulty: "beginner" | "intermediate" | "advanced";
    thumbnail?: string | undefined;
}, {
    name: string;
    description: string;
    category: string;
    nodes: {
        type: import("./common").NodeType;
        id: string;
        createdAt: string;
        updatedAt: string;
        label: string;
        name: string;
        category: string;
        position: {
            x: number;
            y: number;
        };
        inputs: {
            type: import("./common").ConnectionType;
            id: string;
            dataType: import("./common").DataType;
            label: string;
            required?: boolean | undefined;
            multiple?: boolean | undefined;
        }[];
        outputs: {
            type: import("./common").ConnectionType;
            id: string;
            dataType: import("./common").DataType;
            label: string;
            required?: boolean | undefined;
            multiple?: boolean | undefined;
        }[];
        config: Record<string, any>;
        flowId: string;
        description?: string | undefined;
        version?: string | undefined;
        isCustom?: boolean | undefined;
        icon?: string | undefined;
        color?: string | undefined;
        documentation?: string | undefined;
    }[];
    connections: {
        id: string;
        flowId: string;
        sourceNodeId: string;
        sourceHandle: string;
        targetNodeId: string;
        targetHandle: string;
    }[];
    tags: string[];
    thumbnail?: string | undefined;
    isOfficial?: boolean | undefined;
    difficulty?: "beginner" | "intermediate" | "advanced" | undefined;
}>;
export type FlowTemplate = z.infer<typeof FlowTemplateSchema>;
export declare const FlowValidationResultSchema: z.ZodObject<{
    isValid: z.ZodBoolean;
    errors: z.ZodArray<z.ZodObject<{
        nodeId: z.ZodOptional<z.ZodString>;
        connectionId: z.ZodOptional<z.ZodString>;
        message: z.ZodString;
        type: z.ZodEnum<["error", "warning"]>;
    }, "strip", z.ZodTypeAny, {
        message: string;
        type: "error" | "warning";
        nodeId?: string | undefined;
        connectionId?: string | undefined;
    }, {
        message: string;
        type: "error" | "warning";
        nodeId?: string | undefined;
        connectionId?: string | undefined;
    }>, "many">;
}, "strip", z.ZodTypeAny, {
    isValid: boolean;
    errors: {
        message: string;
        type: "error" | "warning";
        nodeId?: string | undefined;
        connectionId?: string | undefined;
    }[];
}, {
    isValid: boolean;
    errors: {
        message: string;
        type: "error" | "warning";
        nodeId?: string | undefined;
        connectionId?: string | undefined;
    }[];
}>;
export type FlowValidationResult = z.infer<typeof FlowValidationResultSchema>;
//# sourceMappingURL=flow.d.ts.map