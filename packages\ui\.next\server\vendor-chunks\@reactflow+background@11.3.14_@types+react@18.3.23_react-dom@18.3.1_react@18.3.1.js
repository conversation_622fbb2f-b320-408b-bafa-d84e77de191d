"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@reactflow+background@11.3.14_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1";
exports.ids = ["vendor-chunks/@reactflow+background@11.3.14_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@reactflow+background@11.3.14_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@reactflow/background/dist/esm/index.mjs":
/*!***********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@reactflow+background@11.3.14_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@reactflow/background/dist/esm/index.mjs ***!
  \***********************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Background: () => (/* binding */ Background$1),\n/* harmony export */   BackgroundVariant: () => (/* binding */ BackgroundVariant)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var classcat__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classcat */ \"(ssr)/../../node_modules/.pnpm/classcat@5.0.5/node_modules/classcat/index.js\");\n/* harmony import */ var _reactflow_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @reactflow/core */ \"(ssr)/../../node_modules/.pnpm/@reactflow+core@11.11.4_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@reactflow/core/dist/esm/index.mjs\");\n/* harmony import */ var zustand_shallow__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zustand/shallow */ \"(ssr)/../../node_modules/.pnpm/zustand@4.5.7_@types+react@18.3.23_react@18.3.1/node_modules/zustand/esm/shallow.mjs\");\n\n\n\n\n\nvar BackgroundVariant;\n(function (BackgroundVariant) {\n    BackgroundVariant[\"Lines\"] = \"lines\";\n    BackgroundVariant[\"Dots\"] = \"dots\";\n    BackgroundVariant[\"Cross\"] = \"cross\";\n})(BackgroundVariant || (BackgroundVariant = {}));\n\nfunction LinePattern({ color, dimensions, lineWidth }) {\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", { stroke: color, strokeWidth: lineWidth, d: `M${dimensions[0] / 2} 0 V${dimensions[1]} M0 ${dimensions[1] / 2} H${dimensions[0]}` }));\n}\nfunction DotPattern({ color, radius }) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"circle\", { cx: radius, cy: radius, r: radius, fill: color });\n}\n\nconst defaultColor = {\n    [BackgroundVariant.Dots]: '#91919a',\n    [BackgroundVariant.Lines]: '#eee',\n    [BackgroundVariant.Cross]: '#e2e2e2',\n};\nconst defaultSize = {\n    [BackgroundVariant.Dots]: 1,\n    [BackgroundVariant.Lines]: 1,\n    [BackgroundVariant.Cross]: 6,\n};\nconst selector = (s) => ({ transform: s.transform, patternId: `pattern-${s.rfId}` });\nfunction Background({ id, variant = BackgroundVariant.Dots, \n// only used for dots and cross\ngap = 20, \n// only used for lines and cross\nsize, lineWidth = 1, offset = 2, color, style, className, }) {\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const { transform, patternId } = (0,_reactflow_core__WEBPACK_IMPORTED_MODULE_2__.useStore)(selector, zustand_shallow__WEBPACK_IMPORTED_MODULE_3__.shallow);\n    const patternColor = color || defaultColor[variant];\n    const patternSize = size || defaultSize[variant];\n    const isDots = variant === BackgroundVariant.Dots;\n    const isCross = variant === BackgroundVariant.Cross;\n    const gapXY = Array.isArray(gap) ? gap : [gap, gap];\n    const scaledGap = [gapXY[0] * transform[2] || 1, gapXY[1] * transform[2] || 1];\n    const scaledSize = patternSize * transform[2];\n    const patternDimensions = isCross ? [scaledSize, scaledSize] : scaledGap;\n    const patternOffset = isDots\n        ? [scaledSize / offset, scaledSize / offset]\n        : [patternDimensions[0] / offset, patternDimensions[1] / offset];\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", { className: (0,classcat__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(['react-flow__background', className]), style: {\n            ...style,\n            position: 'absolute',\n            width: '100%',\n            height: '100%',\n            top: 0,\n            left: 0,\n        }, ref: ref, \"data-testid\": \"rf__background\" },\n        react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"pattern\", { id: patternId + id, x: transform[0] % scaledGap[0], y: transform[1] % scaledGap[1], width: scaledGap[0], height: scaledGap[1], patternUnits: \"userSpaceOnUse\", patternTransform: `translate(-${patternOffset[0]},-${patternOffset[1]})` }, isDots ? (react__WEBPACK_IMPORTED_MODULE_0__.createElement(DotPattern, { color: patternColor, radius: scaledSize / offset })) : (react__WEBPACK_IMPORTED_MODULE_0__.createElement(LinePattern, { dimensions: patternDimensions, color: patternColor, lineWidth: lineWidth }))),\n        react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"rect\", { x: \"0\", y: \"0\", width: \"100%\", height: \"100%\", fill: `url(#${patternId + id})` })));\n}\nBackground.displayName = 'Background';\nvar Background$1 = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(Background);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0ByZWFjdGZsb3crYmFja2dyb3VuZEAxMS4zLjE0X0B0eXBlcytyZWFjdEAxOC4zLjIzX3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xL25vZGVfbW9kdWxlcy9AcmVhY3RmbG93L2JhY2tncm91bmQvZGlzdC9lc20vaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUE0QztBQUNsQjtBQUNpQjtBQUNEOztBQUUxQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyw4Q0FBOEM7O0FBRS9DLHVCQUF1Qiw4QkFBOEI7QUFDckQsWUFBWSxnREFBbUIsV0FBVyw4Q0FBOEMsbUJBQW1CLEtBQUssZUFBZSxLQUFLLG1CQUFtQixHQUFHLGNBQWMsR0FBRztBQUMzSztBQUNBLHNCQUFzQixlQUFlO0FBQ3JDLFdBQVcsZ0RBQW1CLGFBQWEsZ0RBQWdEO0FBQzNGOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLDhDQUE4QyxPQUFPLEdBQUc7QUFDbkYsc0JBQXNCO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBLDJEQUEyRDtBQUMzRCxnQkFBZ0IsNkNBQU07QUFDdEIsWUFBWSx1QkFBdUIsRUFBRSx5REFBUSxXQUFXLG9EQUFPO0FBQy9EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLGdEQUFtQixVQUFVLFdBQVcsb0RBQUU7QUFDdEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUyw2Q0FBNkM7QUFDdEQsUUFBUSxnREFBbUIsY0FBYywrTEFBK0wsaUJBQWlCLElBQUksaUJBQWlCLElBQUksWUFBWSxnREFBbUIsZUFBZSxrREFBa0QsTUFBTSxnREFBbUIsZ0JBQWdCLDBFQUEwRTtBQUNyZSxRQUFRLGdEQUFtQixXQUFXLDZEQUE2RCxlQUFlLElBQUk7QUFDdEg7QUFDQTtBQUNBLG1CQUFtQiwyQ0FBSTs7QUFFa0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AZmxvd3dpc2UtY2xvbmUvdWkvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0ByZWFjdGZsb3crYmFja2dyb3VuZEAxMS4zLjE0X0B0eXBlcytyZWFjdEAxOC4zLjIzX3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xL25vZGVfbW9kdWxlcy9AcmVhY3RmbG93L2JhY2tncm91bmQvZGlzdC9lc20vaW5kZXgubWpzP2Q3ZDYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0LCB7IG1lbW8sIHVzZVJlZiB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBjYyBmcm9tICdjbGFzc2NhdCc7XG5pbXBvcnQgeyB1c2VTdG9yZSB9IGZyb20gJ0ByZWFjdGZsb3cvY29yZSc7XG5pbXBvcnQgeyBzaGFsbG93IH0gZnJvbSAnenVzdGFuZC9zaGFsbG93JztcblxudmFyIEJhY2tncm91bmRWYXJpYW50O1xuKGZ1bmN0aW9uIChCYWNrZ3JvdW5kVmFyaWFudCkge1xuICAgIEJhY2tncm91bmRWYXJpYW50W1wiTGluZXNcIl0gPSBcImxpbmVzXCI7XG4gICAgQmFja2dyb3VuZFZhcmlhbnRbXCJEb3RzXCJdID0gXCJkb3RzXCI7XG4gICAgQmFja2dyb3VuZFZhcmlhbnRbXCJDcm9zc1wiXSA9IFwiY3Jvc3NcIjtcbn0pKEJhY2tncm91bmRWYXJpYW50IHx8IChCYWNrZ3JvdW5kVmFyaWFudCA9IHt9KSk7XG5cbmZ1bmN0aW9uIExpbmVQYXR0ZXJuKHsgY29sb3IsIGRpbWVuc2lvbnMsIGxpbmVXaWR0aCB9KSB7XG4gICAgcmV0dXJuIChSZWFjdC5jcmVhdGVFbGVtZW50KFwicGF0aFwiLCB7IHN0cm9rZTogY29sb3IsIHN0cm9rZVdpZHRoOiBsaW5lV2lkdGgsIGQ6IGBNJHtkaW1lbnNpb25zWzBdIC8gMn0gMCBWJHtkaW1lbnNpb25zWzFdfSBNMCAke2RpbWVuc2lvbnNbMV0gLyAyfSBIJHtkaW1lbnNpb25zWzBdfWAgfSkpO1xufVxuZnVuY3Rpb24gRG90UGF0dGVybih7IGNvbG9yLCByYWRpdXMgfSkge1xuICAgIHJldHVybiBSZWFjdC5jcmVhdGVFbGVtZW50KFwiY2lyY2xlXCIsIHsgY3g6IHJhZGl1cywgY3k6IHJhZGl1cywgcjogcmFkaXVzLCBmaWxsOiBjb2xvciB9KTtcbn1cblxuY29uc3QgZGVmYXVsdENvbG9yID0ge1xuICAgIFtCYWNrZ3JvdW5kVmFyaWFudC5Eb3RzXTogJyM5MTkxOWEnLFxuICAgIFtCYWNrZ3JvdW5kVmFyaWFudC5MaW5lc106ICcjZWVlJyxcbiAgICBbQmFja2dyb3VuZFZhcmlhbnQuQ3Jvc3NdOiAnI2UyZTJlMicsXG59O1xuY29uc3QgZGVmYXVsdFNpemUgPSB7XG4gICAgW0JhY2tncm91bmRWYXJpYW50LkRvdHNdOiAxLFxuICAgIFtCYWNrZ3JvdW5kVmFyaWFudC5MaW5lc106IDEsXG4gICAgW0JhY2tncm91bmRWYXJpYW50LkNyb3NzXTogNixcbn07XG5jb25zdCBzZWxlY3RvciA9IChzKSA9PiAoeyB0cmFuc2Zvcm06IHMudHJhbnNmb3JtLCBwYXR0ZXJuSWQ6IGBwYXR0ZXJuLSR7cy5yZklkfWAgfSk7XG5mdW5jdGlvbiBCYWNrZ3JvdW5kKHsgaWQsIHZhcmlhbnQgPSBCYWNrZ3JvdW5kVmFyaWFudC5Eb3RzLCBcbi8vIG9ubHkgdXNlZCBmb3IgZG90cyBhbmQgY3Jvc3NcbmdhcCA9IDIwLCBcbi8vIG9ubHkgdXNlZCBmb3IgbGluZXMgYW5kIGNyb3NzXG5zaXplLCBsaW5lV2lkdGggPSAxLCBvZmZzZXQgPSAyLCBjb2xvciwgc3R5bGUsIGNsYXNzTmFtZSwgfSkge1xuICAgIGNvbnN0IHJlZiA9IHVzZVJlZihudWxsKTtcbiAgICBjb25zdCB7IHRyYW5zZm9ybSwgcGF0dGVybklkIH0gPSB1c2VTdG9yZShzZWxlY3Rvciwgc2hhbGxvdyk7XG4gICAgY29uc3QgcGF0dGVybkNvbG9yID0gY29sb3IgfHwgZGVmYXVsdENvbG9yW3ZhcmlhbnRdO1xuICAgIGNvbnN0IHBhdHRlcm5TaXplID0gc2l6ZSB8fCBkZWZhdWx0U2l6ZVt2YXJpYW50XTtcbiAgICBjb25zdCBpc0RvdHMgPSB2YXJpYW50ID09PSBCYWNrZ3JvdW5kVmFyaWFudC5Eb3RzO1xuICAgIGNvbnN0IGlzQ3Jvc3MgPSB2YXJpYW50ID09PSBCYWNrZ3JvdW5kVmFyaWFudC5Dcm9zcztcbiAgICBjb25zdCBnYXBYWSA9IEFycmF5LmlzQXJyYXkoZ2FwKSA/IGdhcCA6IFtnYXAsIGdhcF07XG4gICAgY29uc3Qgc2NhbGVkR2FwID0gW2dhcFhZWzBdICogdHJhbnNmb3JtWzJdIHx8IDEsIGdhcFhZWzFdICogdHJhbnNmb3JtWzJdIHx8IDFdO1xuICAgIGNvbnN0IHNjYWxlZFNpemUgPSBwYXR0ZXJuU2l6ZSAqIHRyYW5zZm9ybVsyXTtcbiAgICBjb25zdCBwYXR0ZXJuRGltZW5zaW9ucyA9IGlzQ3Jvc3MgPyBbc2NhbGVkU2l6ZSwgc2NhbGVkU2l6ZV0gOiBzY2FsZWRHYXA7XG4gICAgY29uc3QgcGF0dGVybk9mZnNldCA9IGlzRG90c1xuICAgICAgICA/IFtzY2FsZWRTaXplIC8gb2Zmc2V0LCBzY2FsZWRTaXplIC8gb2Zmc2V0XVxuICAgICAgICA6IFtwYXR0ZXJuRGltZW5zaW9uc1swXSAvIG9mZnNldCwgcGF0dGVybkRpbWVuc2lvbnNbMV0gLyBvZmZzZXRdO1xuICAgIHJldHVybiAoUmVhY3QuY3JlYXRlRWxlbWVudChcInN2Z1wiLCB7IGNsYXNzTmFtZTogY2MoWydyZWFjdC1mbG93X19iYWNrZ3JvdW5kJywgY2xhc3NOYW1lXSksIHN0eWxlOiB7XG4gICAgICAgICAgICAuLi5zdHlsZSxcbiAgICAgICAgICAgIHBvc2l0aW9uOiAnYWJzb2x1dGUnLFxuICAgICAgICAgICAgd2lkdGg6ICcxMDAlJyxcbiAgICAgICAgICAgIGhlaWdodDogJzEwMCUnLFxuICAgICAgICAgICAgdG9wOiAwLFxuICAgICAgICAgICAgbGVmdDogMCxcbiAgICAgICAgfSwgcmVmOiByZWYsIFwiZGF0YS10ZXN0aWRcIjogXCJyZl9fYmFja2dyb3VuZFwiIH0sXG4gICAgICAgIFJlYWN0LmNyZWF0ZUVsZW1lbnQoXCJwYXR0ZXJuXCIsIHsgaWQ6IHBhdHRlcm5JZCArIGlkLCB4OiB0cmFuc2Zvcm1bMF0gJSBzY2FsZWRHYXBbMF0sIHk6IHRyYW5zZm9ybVsxXSAlIHNjYWxlZEdhcFsxXSwgd2lkdGg6IHNjYWxlZEdhcFswXSwgaGVpZ2h0OiBzY2FsZWRHYXBbMV0sIHBhdHRlcm5Vbml0czogXCJ1c2VyU3BhY2VPblVzZVwiLCBwYXR0ZXJuVHJhbnNmb3JtOiBgdHJhbnNsYXRlKC0ke3BhdHRlcm5PZmZzZXRbMF19LC0ke3BhdHRlcm5PZmZzZXRbMV19KWAgfSwgaXNEb3RzID8gKFJlYWN0LmNyZWF0ZUVsZW1lbnQoRG90UGF0dGVybiwgeyBjb2xvcjogcGF0dGVybkNvbG9yLCByYWRpdXM6IHNjYWxlZFNpemUgLyBvZmZzZXQgfSkpIDogKFJlYWN0LmNyZWF0ZUVsZW1lbnQoTGluZVBhdHRlcm4sIHsgZGltZW5zaW9uczogcGF0dGVybkRpbWVuc2lvbnMsIGNvbG9yOiBwYXR0ZXJuQ29sb3IsIGxpbmVXaWR0aDogbGluZVdpZHRoIH0pKSksXG4gICAgICAgIFJlYWN0LmNyZWF0ZUVsZW1lbnQoXCJyZWN0XCIsIHsgeDogXCIwXCIsIHk6IFwiMFwiLCB3aWR0aDogXCIxMDAlXCIsIGhlaWdodDogXCIxMDAlXCIsIGZpbGw6IGB1cmwoIyR7cGF0dGVybklkICsgaWR9KWAgfSkpKTtcbn1cbkJhY2tncm91bmQuZGlzcGxheU5hbWUgPSAnQmFja2dyb3VuZCc7XG52YXIgQmFja2dyb3VuZCQxID0gbWVtbyhCYWNrZ3JvdW5kKTtcblxuZXhwb3J0IHsgQmFja2dyb3VuZCQxIGFzIEJhY2tncm91bmQsIEJhY2tncm91bmRWYXJpYW50IH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@reactflow+background@11.3.14_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@reactflow/background/dist/esm/index.mjs\n");

/***/ })

};
;