"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const morgan_1 = __importDefault(require("morgan"));
const compression_1 = __importDefault(require("compression"));
const http_1 = require("http");
const socket_io_1 = require("socket.io");
const dotenv_1 = __importDefault(require("dotenv"));
// Load environment variables
dotenv_1.default.config();
// Import routes
const flows_1 = __importDefault(require("./routes/flows"));
const nodes_1 = __importDefault(require("./routes/nodes"));
const chat_1 = __importDefault(require("./routes/chat"));
const agents_1 = __importDefault(require("./routes/agents"));
const users_1 = __importDefault(require("./routes/users"));
const workspaces_1 = __importDefault(require("./routes/workspaces"));
const executions_1 = __importDefault(require("./routes/executions"));
const templates_1 = __importDefault(require("./routes/templates"));
// Import middleware
const errorHandler_1 = require("./middleware/errorHandler");
const rateLimiter_1 = require("./middleware/rateLimiter");
const auth_1 = require("./middleware/auth");
// Import services
const database_1 = require("./services/database");
const socket_1 = require("./services/socket");
const app = (0, express_1.default)();
const server = (0, http_1.createServer)(app);
const io = new socket_io_1.Server(server, {
    cors: {
        origin: process.env.CORS_ORIGIN || "http://localhost:3000",
        methods: ["GET", "POST"]
    }
});
const PORT = process.env.PORT || 3001;
// Initialize services
const databaseService = new database_1.DatabaseService();
const socketService = new socket_1.SocketService(io);
// Middleware
app.use((0, helmet_1.default)());
app.use((0, cors_1.default)({
    origin: process.env.CORS_ORIGIN || "http://localhost:3000",
    credentials: true
}));
app.use((0, compression_1.default)());
app.use((0, morgan_1.default)('combined'));
app.use(express_1.default.json({ limit: '50mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '50mb' }));
// Rate limiting
app.use(rateLimiter_1.rateLimiter);
// Health check endpoint
app.get('/health', (req, res) => {
    res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        version: process.env.npm_package_version || '1.0.0'
    });
});
// API routes
app.use('/api/flows', auth_1.authMiddleware, flows_1.default);
app.use('/api/nodes', auth_1.authMiddleware, nodes_1.default);
app.use('/api/chat', auth_1.authMiddleware, chat_1.default);
app.use('/api/agents', auth_1.authMiddleware, agents_1.default);
app.use('/api/users', users_1.default);
app.use('/api/workspaces', auth_1.authMiddleware, workspaces_1.default);
app.use('/api/executions', auth_1.authMiddleware, executions_1.default);
app.use('/api/templates', templates_1.default);
// Error handling
app.use(errorHandler_1.errorHandler);
// 404 handler
app.use('*', (req, res) => {
    res.status(404).json({ error: 'Route not found' });
});
// Initialize database and start server
async function startServer() {
    try {
        await databaseService.connect();
        console.log('✅ Database connected successfully');
        server.listen(PORT, () => {
            console.log(`🚀 Server running on port ${PORT}`);
            console.log(`📊 Health check: http://localhost:${PORT}/health`);
            console.log(`🌐 Environment: ${process.env.NODE_ENV || 'development'}`);
        });
    }
    catch (error) {
        console.error('❌ Failed to start server:', error);
        process.exit(1);
    }
}
// Graceful shutdown
process.on('SIGTERM', async () => {
    console.log('🔄 SIGTERM received, shutting down gracefully');
    await databaseService.disconnect();
    server.close(() => {
        console.log('✅ Server closed');
        process.exit(0);
    });
});
process.on('SIGINT', async () => {
    console.log('🔄 SIGINT received, shutting down gracefully');
    await databaseService.disconnect();
    server.close(() => {
        console.log('✅ Server closed');
        process.exit(0);
    });
});
startServer();
//# sourceMappingURL=index.js.map